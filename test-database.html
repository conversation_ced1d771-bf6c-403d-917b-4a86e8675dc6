<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قاعدة البيانات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار قاعدة البيانات</h1>
        
        <div class="test-section">
            <h3>حالة قاعدة البيانات</h3>
            <div id="dbStatus">جاري الفحص...</div>
        </div>
        
        <div class="test-section">
            <h3>الملفات المحفوظة</h3>
            <div id="filesStatus">جاري الفحص...</div>
        </div>
        
        <div class="test-section">
            <h3>الإجراءات</h3>
            <button onclick="testDatabase()">فحص قاعدة البيانات</button>
            <button onclick="resetDatabase()">إعادة تهيئة البيانات</button>
            <button onclick="clearAll()">مسح جميع البيانات</button>
        </div>
        
        <div class="test-section">
            <h3>سجل الأحداث</h3>
            <pre id="log"></pre>
        </div>
    </div>

    <script src="assets/js/database.js"></script>
    <script>
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = `test-section ${type}`;
        }

        async function testDatabase() {
            log('بدء اختبار قاعدة البيانات...');
            
            // Check if database exists
            if (!window.localDB) {
                updateStatus('dbStatus', 'خطأ: قاعدة البيانات غير متوفرة', 'error');
                log('خطأ: window.localDB غير موجود');
                return;
            }
            
            updateStatus('dbStatus', 'قاعدة البيانات متوفرة ✓', 'success');
            log('قاعدة البيانات متوفرة');
            
            // Test getting files
            try {
                const files = await window.localDB.getFiles();
                updateStatus('filesStatus', `تم العثور على ${files.length} ملف`, 'success');
                log(`تم تحميل ${files.length} ملف بنجاح`);
                
                if (files.length > 0) {
                    log('أول ملف: ' + JSON.stringify(files[0], null, 2));
                }
            } catch (error) {
                updateStatus('filesStatus', `خطأ في تحميل الملفات: ${error.message}`, 'error');
                log('خطأ في تحميل الملفات: ' + error.message);
            }
        }

        async function resetDatabase() {
            if (!confirm('هل أنت متأكد من إعادة تهيئة قاعدة البيانات؟')) return;
            
            log('إعادة تهيئة قاعدة البيانات...');
            
            try {
                // Clear localStorage
                localStorage.removeItem('archive_files');
                localStorage.removeItem('archive_folders');
                localStorage.removeItem('archive_activity_logs');
                
                // Re-initialize
                if (window.localDB) {
                    window.localDB.initializeLocalStorage();
                }
                
                log('تم إعادة تهيئة قاعدة البيانات بنجاح');
                await testDatabase();
            } catch (error) {
                log('خطأ في إعادة التهيئة: ' + error.message);
            }
        }

        function clearAll() {
            if (!confirm('هل أنت متأكد من مسح جميع البيانات؟')) return;
            
            localStorage.clear();
            log('تم مسح جميع البيانات من localStorage');
            location.reload();
        }

        // Auto-test on load
        window.addEventListener('load', () => {
            setTimeout(testDatabase, 1000);
        });
    </script>
</body>
</html>
