@echo off
echo Starting Archive System Server...
echo.

REM Try Python first
python -m http.server 8000 2>nul
if %errorlevel% neq 0 (
    echo Python not found or failed, trying Python3...
    python3 -m http.server 8000 2>nul
    if %errorlevel% neq 0 (
        echo Python3 not found or failed, trying Node.js...
        node server.js 2>nul
        if %errorlevel% neq 0 (
            echo Node.js not found or failed, trying PHP...
            php -S localhost:8000 2>nul
            if %errorlevel% neq 0 (
                echo All server options failed!
                echo Please install Python, Node.js, or PHP to run the server.
                pause
                exit /b 1
            )
        )
    )
)

echo Server started successfully!
echo Open your browser and go to: http://localhost:8000
pause
