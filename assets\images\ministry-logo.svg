<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   width="289.91315"
   height="300"
   viewBox="0 0 966.37716 1000"
   xml:space="preserve"
   version="1.1"
   id="svg168"
   sodipodi:docname="Saudi_Ministry_of_Defense_Logo.svg"
   inkscape:version="1.3.2 (091e20e, 2023-11-25, custom)"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg"><defs
   id="defs168">
	

		
	
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
			
		</defs><sodipodi:namedview
   id="namedview168"
   pagecolor="#ffffff"
   bordercolor="#999999"
   borderopacity="1"
   inkscape:showpageshadow="2"
   inkscape:pageopacity="0"
   inkscape:pagecheckerboard="0"
   inkscape:deskcolor="#d1d1d1"
   inkscape:zoom="1.5886332"
   inkscape:cx="97.568146"
   inkscape:cy="112.046"
   inkscape:window-width="1500"
   inkscape:window-height="912"
   inkscape:window-x="-8"
   inkscape:window-y="-8"
   inkscape:window-maximized="1"
   inkscape:current-layer="svg168" />
<style
   type="text/css"
   id="style1">
	.st0{fill:#C6C6C6;}
	.st1{clip-path:url(#SVGID_00000162339530966506230770000005014271129694862262_);}
	.st2{clip-path:url(#SVGID_00000183206814662217716860000004545732634884518021_);}
	.st3{clip-path:url(#SVGID_00000062900925352632784120000005394320386290334892_);}
	.st4{fill:#FFFFFF;}
	.st5{fill:#999999;}
	.st6{fill:none;stroke:#3A8DDE;stroke-width:2.5437;stroke-miterlimit:10;}
	.st7{fill:#3A8DDE;}
	.st8{fill:#C5212F;}
	.st9{fill:#4B9E46;}
	.st10{fill:#E4B632;}
	.st11{fill:#E8253C;}
	.st12{fill:#19496B;}
	.st13{fill:#00699D;}
	.st14{fill:#5FBA46;}
	.st15{fill:#1995D3;}
	.st16{fill:#417F44;}
	.st17{fill:#C6972D;}
	.st18{fill:#F89E27;}
	.st19{fill:#DC1768;}
	.st20{fill:#F16A2C;}
	.st21{fill:#A21C44;}
	.st22{fill:#F8C315;}
	.st23{fill:#27BCE1;}
	.st24{fill:#EE402B;}
	.st25{fill:#2F2C6B;}
	.st26{fill:#43C2CA;}
	.st27{fill:#3F3073;}
	.st28{fill:#657479;}
	.st29{fill:#D5BB78;}
	.st30{fill:#96863A;}
	.st31{fill:#959FA4;}
	.st32{fill:#3E9858;}
	.st33{fill:#05713A;}
	.st34{fill:#921D1D;}
	.st35{fill:#CB3E42;}
	.st36{fill:#B2BEC4;}
	
		.st37{clip-path:url(#SVGID_00000170995762615829111170000012641525279436657286_);fill:url(#SVGID_00000024720729683298790200000007560554982753170621_);}
	
		.st38{clip-path:url(#SVGID_00000021804636592897341580000004949892331619663531_);fill:url(#SVGID_00000071547684062479376750000006664333703556311991_);}
	
		.st39{clip-path:url(#SVGID_00000157279395652758398140000016264434664548668818_);fill:url(#SVGID_00000096049513955926184660000010356628068315159687_);}
	
		.st40{clip-path:url(#SVGID_00000063612694192870499370000017253137475807753364_);fill:url(#SVGID_00000088117314533018552600000013381601249783319977_);}
	
		.st41{clip-path:url(#SVGID_00000070091851830456299690000017799167796345182856_);fill:url(#SVGID_00000022560158261437038150000002593767502834741672_);}
	
		.st42{clip-path:url(#SVGID_00000088814348303923216490000013951199161492622765_);fill:url(#SVGID_00000139978831883967034280000010985943452120229517_);}
	
		.st43{clip-path:url(#SVGID_00000128465475089772347080000012820235132354574999_);fill:url(#SVGID_00000045608648437102420530000015401841919235659944_);}
	
		.st44{clip-path:url(#SVGID_00000086649989617885712630000015647529349635725706_);fill:url(#SVGID_00000075124778013774949960000017494949253818687152_);}
	
		.st45{clip-path:url(#SVGID_00000033353164357822019050000011833675908601378739_);fill:url(#SVGID_00000003100777000610589410000017939962126186950809_);}
	
		.st46{clip-path:url(#SVGID_00000019639082665847131620000015006063816638110355_);fill:url(#SVGID_00000160895932858741230840000001306191428414335163_);}
	
		.st47{clip-path:url(#SVGID_00000000943436670290586150000010440139412346609329_);fill:url(#SVGID_00000101104935389775852170000002833211723232558472_);}
	
		.st48{clip-path:url(#SVGID_00000066483029910694897860000000924077841174772147_);fill:url(#SVGID_00000112601441354590968550000006936520091851590587_);}
	
		.st49{clip-path:url(#SVGID_00000116938326457319842340000005463191525316703403_);fill:url(#SVGID_00000069365529884183375580000006900761452990836663_);}
	
		.st50{clip-path:url(#SVGID_00000021119863796420923720000012338243264593902976_);fill:url(#SVGID_00000142878678453663496220000018086990577139763343_);}
	
		.st51{clip-path:url(#SVGID_00000147190186576116087980000014819990441585290385_);fill:url(#SVGID_00000101085385292833265420000001181700003056597406_);}
	
		.st52{clip-path:url(#SVGID_00000145036520236342477280000010115338714043690399_);fill:url(#SVGID_00000160156754097864594480000004542982119190301611_);}
	
		.st53{clip-path:url(#SVGID_00000155863903153918062810000000059643862849100436_);fill:url(#SVGID_00000026143683494719727210000018302872507262154389_);}
	
		.st54{clip-path:url(#SVGID_00000176755132520321243510000016378468653173233304_);fill:url(#SVGID_00000174594887063458181570000002954393102821661096_);}
	
		.st55{clip-path:url(#SVGID_00000127723844303616274460000006017643808725015711_);fill:url(#SVGID_00000085228344325887709100000017311210471128417681_);}
	
		.st56{clip-path:url(#SVGID_00000168090231080519065310000008123476183695792019_);fill:url(#SVGID_00000145759651488845370580000013142527476648413057_);}
	
		.st57{clip-path:url(#SVGID_00000155124830755062870530000003238226516875261624_);fill:url(#SVGID_00000164496059619056022630000007226914259658650800_);}
	
		.st58{clip-path:url(#SVGID_00000179644531372641224310000010776528969947119246_);fill:url(#SVGID_00000121279678767051738380000018267532343473226933_);}
	
		.st59{clip-path:url(#SVGID_00000100376348632883898720000005050761264158570173_);fill:url(#SVGID_00000065066143637609115330000007477088538178420646_);}
	
		.st60{clip-path:url(#SVGID_00000075873820561113258240000002890750169330937005_);fill:url(#SVGID_00000181793465884368326560000009972946200005347254_);}
	
		.st61{clip-path:url(#SVGID_00000124154623506364975360000003032178911701053093_);fill:url(#SVGID_00000054253640509274644120000018360366727330027396_);}
	
		.st62{clip-path:url(#SVGID_00000058568001461010370740000018115654062709491353_);fill:url(#SVGID_00000061436132974506712160000005754823626010795933_);}
	
		.st63{clip-path:url(#SVGID_00000136390118710902738610000013649608891867823760_);fill:url(#SVGID_00000123436134434904430580000000599405144411306371_);}
	
		.st64{clip-path:url(#SVGID_00000129912813780904463790000000710358121282569913_);fill:url(#SVGID_00000070106956503705253370000007629370373827344259_);}
	
		.st65{clip-path:url(#SVGID_00000007406797047423312460000013752655270618347663_);fill:url(#SVGID_00000071561924554610990090000011493357785758060179_);}
	
		.st66{clip-path:url(#SVGID_00000138550024802893535790000010208075862563828665_);fill:url(#SVGID_00000116945039291005766660000016726723712096979119_);}
	
		.st67{clip-path:url(#SVGID_00000090262298399596368230000009190011745420254653_);fill:url(#SVGID_00000060006796880955148820000004273871040230026919_);}
	
		.st68{clip-path:url(#SVGID_00000176721711505687937400000014718881577945760672_);fill:url(#SVGID_00000043433479904965021750000013652723290602560685_);}
	
		.st69{clip-path:url(#SVGID_00000103970666956751749800000010162974753629071788_);fill:url(#SVGID_00000096046406266809077680000015727210547832077961_);}
	
		.st70{clip-path:url(#SVGID_00000145768953847255881370000016968112213937378230_);fill:url(#SVGID_00000054955373103464352250000006406933904871687047_);}
	
		.st71{clip-path:url(#SVGID_00000161630410043136138890000006149163672664857259_);fill:url(#SVGID_00000075143580854216234170000006970336764271138747_);}
	
		.st72{clip-path:url(#SVGID_00000154419526589385339650000013837899457813527974_);fill:url(#SVGID_00000063610187234190525720000018317183408521500290_);}
	
		.st73{clip-path:url(#SVGID_00000000195530227704735790000003524755203246363061_);fill:url(#SVGID_00000087399716320003720830000001100923830149411262_);}
	
		.st74{clip-path:url(#SVGID_00000126323162375798691510000000736111671625956484_);fill:url(#SVGID_00000170241286456343496950000010916824542963449737_);}
	
		.st75{clip-path:url(#SVGID_00000126318186072467778610000008598606353922798478_);fill:url(#SVGID_00000034788843831551017260000015289668162684447915_);}
	
		.st76{clip-path:url(#SVGID_00000181059839149004140620000008974883229666343555_);fill:url(#SVGID_00000038410378208032648240000003939147629380251809_);}
	
		.st77{clip-path:url(#SVGID_00000023281955996496672130000012983321023848042635_);fill:url(#SVGID_00000053540025403858687830000002392567983389233834_);}
	
		.st78{clip-path:url(#SVGID_00000165207753315559071310000000347029139438146707_);fill:url(#SVGID_00000031911385198785125710000004256098263482175679_);}
	
		.st79{clip-path:url(#SVGID_00000134237950761191009380000000635418838658736771_);fill:url(#SVGID_00000062894788031126108270000011565400929622324412_);}
	
		.st80{clip-path:url(#SVGID_00000170255746494675218660000010117018301704859529_);fill:url(#SVGID_00000106847186141753357500000017561287172944187315_);}
	
		.st81{clip-path:url(#SVGID_00000152973505246273605960000003262447168170246057_);fill:url(#SVGID_00000142155588398753602640000012620641370758070962_);}
	
		.st82{clip-path:url(#SVGID_00000112620247185026802610000014518587083216948359_);fill:url(#SVGID_00000089535912771436975230000007656123204203549062_);}
	
		.st83{clip-path:url(#SVGID_00000120531659158585670090000004333337352033687450_);fill:url(#SVGID_00000072255820127574063810000014592303717353395894_);}
	
		.st84{clip-path:url(#SVGID_00000148625205920849876950000009410425256112563894_);fill:url(#SVGID_00000047042593229192494590000008507765648299925649_);}
	
		.st85{clip-path:url(#SVGID_00000059271765971215546530000005755962093313997457_);fill:url(#SVGID_00000038386607004980018780000015856075860587377313_);}
	
		.st86{clip-path:url(#SVGID_00000054228490697324433110000006964848452758359223_);fill:url(#SVGID_00000144333764063224865740000004687477825507535505_);}
	
		.st87{clip-path:url(#SVGID_00000164491758721442773860000005035015738911371199_);fill:url(#SVGID_00000065793327542143338340000013350163039126218902_);}
	.st88{opacity:0.2;}
	.st89{clip-path:url(#SVGID_00000127016312466780544920000013194052290210260913_);fill:#A89853;}
	.st90{clip-path:url(#SVGID_00000147929972943626698450000014356055598278081979_);fill:#A89853;}
	
		.st91{clip-path:url(#SVGID_00000054955210697463017190000012154393192504324531_);fill:url(#SVGID_00000052801665709619204800000014850022457752470161_);}
	
		.st92{clip-path:url(#SVGID_00000041283967938321145100000010982146757038833832_);fill:url(#SVGID_00000149351244919131219260000003062883133091608207_);}
	
		.st93{clip-path:url(#SVGID_00000162321386703677001570000006115522030449922445_);fill:url(#SVGID_00000084530713332011670500000018308226629792066699_);}
	
		.st94{clip-path:url(#SVGID_00000139995733253018085470000003003617307810134422_);fill:url(#SVGID_00000096772657094048694040000008139863897289160379_);}
	
		.st95{clip-path:url(#SVGID_00000137846738217250450840000012704264305899714968_);fill:url(#SVGID_00000142886373186936412490000009752573443200021392_);}
	
		.st96{clip-path:url(#SVGID_00000033347375906973763850000001913580444724318379_);fill:url(#SVGID_00000116931679104212457130000000725356223076454818_);}
	
		.st97{clip-path:url(#SVGID_00000039819250777879481370000003915235460639219125_);fill:url(#SVGID_00000082328048553000990460000000978008299352309439_);}
	
		.st98{clip-path:url(#SVGID_00000056402620605681659390000009087579099085864836_);fill:url(#SVGID_00000026852397839224032830000003364232204786068667_);}
	.st99{fill:#444A55;}
	.st100{fill-rule:evenodd;clip-rule:evenodd;fill:#444A55;}
	
		.st101{clip-path:url(#SVGID_00000097501777592017338810000004405686767218189468_);fill:url(#SVGID_00000124865445323734734750000007039997117957800339_);}
	.st102{fill:#2DAC64;}
	.st103{fill:#123453;}
	.st104{clip-path:url(#SVGID_00000058554362212080912270000013896484758397894827_);fill:#13110C;}
	.st105{clip-path:url(#SVGID_00000058554362212080912270000013896484758397894827_);fill:#F7F7F9;}
	
		.st106{clip-path:url(#SVGID_00000028314464481338498800000005204605155006411679_);fill:url(#SVGID_00000158729107358763888370000014433534399275391118_);}
	
		.st107{clip-path:url(#SVGID_00000088104640080032060610000007098543627817190787_);fill:url(#SVGID_00000041259410514481702730000015864070137284684980_);}
	
		.st108{clip-path:url(#SVGID_00000015353495852605173130000001030979722706665619_);fill:url(#SVGID_00000178205871746143544970000017944196067999347617_);}
	.st109{clip-path:url(#SVGID_00000127745087477559753240000003213134977795904415_);}
	
		.st110{clip-path:url(#SVGID_00000153669281071064051920000009362476012342804396_);fill:url(#SVGID_00000174568478916546326810000007978353641486513540_);}
	.st111{clip-path:url(#SVGID_00000173144325626532833390000000871235151469043388_);}
	
		.st112{clip-path:url(#SVGID_00000034807448920919129670000004365994549083483056_);fill:url(#SVGID_00000156557460039937671250000009072425834175316407_);}
	.st113{clip-path:url(#SVGID_00000062154968422956551770000002150694448028370561_);}
	
		.st114{clip-path:url(#SVGID_00000024687301849331903420000009599255101201269913_);fill:url(#SVGID_00000170238592884751388480000002373052655196355516_);}
	.st115{clip-path:url(#SVGID_00000142875265045916331320000006749648933735127175_);}
	.st116{clip-path:url(#SVGID_00000144318800655059513770000000979240837430466182_);}
	
		.st117{clip-path:url(#SVGID_00000031927072229734775880000016099710195945191615_);fill:url(#SVGID_00000116229050926788133150000000714244511258890939_);}
	.st118{clip-path:url(#SVGID_00000108269603154713958450000010399625422520848259_);}
	
		.st119{clip-path:url(#SVGID_00000017491605806594630890000011934503683520755645_);fill:url(#SVGID_00000117661042472685631430000018216240009581216665_);}
	.st120{clip-path:url(#SVGID_00000039830774777932720760000012381576886544183727_);}
	.st121{clip-path:url(#SVGID_00000153699049919724884310000007209331332903132850_);}
	
		.st122{clip-path:url(#SVGID_00000182522446984305470540000000865592600821008317_);fill:url(#SVGID_00000175297385964214467210000009371353774666882985_);}
	.st123{fill:#3E3089;}
	.st124{fill:#0F8ECD;}
	.st125{fill:#2F52A0;}
	.st126{fill:#5EB130;}
	.st127{fill:#0BADC2;}
	.st128{fill:#41AF66;}
	.st129{fill:#646363;}
	.st130{fill:#3D3935;}
	.st131{fill:#56514F;}
	.st132{fill:#61A331;}
	.st133{fill:#008837;}
	.st134{fill:#555253;}
	.st135{fill:#FCD100;}
	.st136{clip-path:url(#SVGID_00000178201018835605277970000008865102778318508685_);}
	.st137{fill:#9ABA1F;}
	.st138{clip-path:url(#SVGID_00000028300427756061497070000017219615637248287675_);}
	.st139{clip-path:url(#SVGID_00000088105576622790433290000017699065161299245242_);}
	.st140{fill:#686666;}
	.st141{fill-rule:evenodd;clip-rule:evenodd;fill:#8ECEA9;}
	.st142{fill-rule:evenodd;clip-rule:evenodd;fill:#F48D81;}
	.st143{fill-rule:evenodd;clip-rule:evenodd;fill:#FBB662;}
	.st144{fill-rule:evenodd;clip-rule:evenodd;fill:#9AC4E8;}
	.st145{fill-rule:evenodd;clip-rule:evenodd;fill:#9E7B56;}
	.st146{fill:#FFFFFF;filter:url(#Adobe_OpacityMaskFilter);}
	.st147{mask:url(#mask0_583_31341_00000135671484554273776360000011107368791950673292_);}
	.st148{fill:#FFFFFF;filter:url(#Adobe_OpacityMaskFilter_00000095321219527995173620000011229743156088771219_);}
	.st149{mask:url(#mask1_583_31341_00000030457114086357956030000012806092536184568999_);}
	.st150{fill:#225730;}
	.st151{fill:#A1893A;}
	.st152{fill:#10A8E1;}
	.st153{fill:#58BCAD;}
	.st154{fill:#027F9B;}
	.st155{fill:#F28C2B;}
	.st156{fill:#F7C75D;}
	.st157{fill:#E86E26;}
	.st158{fill:#62835D;}
	.st159{fill:#715121;}
	.st160{fill:#8C7B2F;}
	.st161{fill:#7C6227;}
	.st162{fill:#3B4E24;}
	.st163{fill:#03613F;}
	.st164{fill:#363C1C;}
	.st165{fill:#BDBEC0;}
	.st166{fill:#046061;}
	.st167{fill:#2BB7C4;}
	.st168{fill:#6F6F6E;}
	.st169{fill-rule:evenodd;clip-rule:evenodd;fill:#192C4B;}
	.st170{fill-rule:evenodd;clip-rule:evenodd;fill:#1D4524;}
	.st171{fill-rule:evenodd;clip-rule:evenodd;fill:#B4B9D3;}
	.st172{fill:#192C4B;}
	.st173{fill:url(#Ellipse_14_00000054241907292449406050000000230991018533212317_);}
	.st174{fill:url(#Path_19892_00000136397799223183298860000010284009401004342967_);}
	.st175{fill:url(#Path_19893_00000045609775987481995430000016904509629819311289_);}
	.st176{fill:url(#Path_19894_00000007415488420148805610000005444988519985019045_);}
	.st177{fill:url(#Ellipse_15_00000155140993886862257060000017163597984941513625_);}
	.st178{fill:url(#Path_19895_00000016788070077467798610000009395087220447397310_);}
	.st179{fill:url(#Path_19896_00000079447462865654445760000008963097162293102231_);}
	.st180{fill:url(#Path_19897_00000137122884651295830480000011269204419848709311_);}
	.st181{fill:url(#Ellipse_16_00000005986809044574529240000010207751673003282347_);}
	.st182{fill:url(#Ellipse_17_00000175322827676083958250000012808502785358358658_);}
	.st183{fill:url(#Path_19898_00000142136503449702029150000005481629951807119804_);}
	.st184{fill:url(#Ellipse_18_00000072965720319263787580000004865998080479571627_);}
	.st185{fill:url(#Path_19899_00000169529120459359993750000007352546391984922531_);}
	.st186{fill:url(#Ellipse_19_00000155139741802711715940000016896352717739925927_);}
	
		.st187{clip-path:url(#SVGID_00000151506550685755661620000014051186105473066926_);fill:url(#SVGID_00000137107522174361879360000006212109565873499019_);}
	.st188{fill-rule:evenodd;clip-rule:evenodd;fill:#1F5545;}
	.st189{fill-rule:evenodd;clip-rule:evenodd;fill:#6D6A6A;}
	.st190{fill:#39B09A;}
	.st191{fill:#0C5162;}
	.st192{fill:#447CBD;}
	.st193{fill:#09A585;}
	.st194{fill:#605D5E;}
	.st195{fill:#88774F;}
	.st196{fill:#00080C;}
	.st197{fill-rule:evenodd;clip-rule:evenodd;fill:#C3A567;}
	.st198{fill:url(#SVGID_00000090975553558067360250000005006631951424491448_);}
	.st199{fill:url(#SVGID_00000150811033631627311570000003207958413977361055_);}
	.st200{fill:url(#SVGID_00000030483749839664839480000005468511634765787837_);}
	.st201{fill:#111E42;}
	.st202{fill:url(#SVGID_00000048470960275929876390000006353018460279570076_);}
	.st203{fill:url(#SVGID_00000183945978740713052330000006877532129017507458_);}
	.st204{fill:url(#SVGID_00000170253790531483812970000007861074211792829358_);}
	.st205{fill:#24477E;}
	.st206{fill:#3085C7;}
	.st207{fill:#00BBBC;}
	.st208{fill:none;}
	.st209{fill:#E10016;stroke:#E10016;stroke-width:0.216;}
	.st210{fill:#74BB23;}
	.st211{fill:#616363;}
	.st212{fill:#7C7B7B;}
	.st213{enable-background:new    ;}
	.st214{fill:#314268;}
	.st215{fill:#84A45E;}
	.st216{fill:#52366A;}
	.st217{fill:#DD7294;}
	.st218{fill:#AE5E9A;}
	.st219{fill:#E699A0;}
	.st220{fill:#F2C4B3;}
	.st221{fill:#804E9D;}
	.st222{fill:#814B9D;}
	.st223{fill:#B65B99;}
	.st224{fill:#114121;}
	.st225{fill:#778838;}
	.st226{fill-rule:evenodd;clip-rule:evenodd;fill:#FAEA5C;}
	.st227{fill-rule:evenodd;clip-rule:evenodd;fill:url(#SVGID_00000176749527426325309700000014299619965603189424_);}
	.st228{fill:#1E3F20;}
	.st229{fill:#E8F1CE;}
	.st230{fill:url(#SVGID_00000134955500599258586430000014994504088600894623_);}
	.st231{fill:#F1B031;}
	.st232{fill:#29422B;}
	.st233{fill:#6C7B6D;}
	.st234{fill:#617161;}
	.st235{fill:#F4EA80;}
	.st236{fill:#F1DE69;}
	.st237{fill:#F1DB6F;}
	.st238{fill:#F4EA86;}
	.st239{fill:#F0DA64;}
	.st240{fill:#CFB648;}
	.st241{fill:#EED75E;}
	.st242{fill:#F0E476;}
	.st243{fill:#EED555;}
	.st244{fill:#F3EE95;}
	.st245{fill:#E7C947;}
	.st246{fill:#DDC24E;}
	.st247{fill:#DAC141;}
	.st248{fill:#E7DC7B;}
	.st249{fill:#EBDF71;}
	.st250{fill:#C1B346;}
	.st251{fill:#EEE16E;}
	.st252{fill:#E3D16A;}
	.st253{fill:#DFC450;}
	.st254{fill:#DED773;}
	.st255{fill:#C8BD5E;}
	.st256{fill:#D8C862;}
	.st257{fill:#CDCB7F;}
	.st258{fill:#EBEA99;}
	.st259{fill:#F1D058;}
	.st260{fill:#5A6B47;}
	.st261{fill:#EFD775;}
	.st262{fill:#C7B960;}
	.st263{fill:#C1BA5B;}
	.st264{fill:#D7C65C;}
	.st265{fill:#E4DF7E;}
	.st266{fill:#DFCC63;}
	.st267{fill:#EBDE64;}
	.st268{fill:#617351;}
	.st269{fill:#4C5E3B;}
	.st270{fill:#D9C65C;}
	.st271{fill:#E3D773;}
	.st272{fill:#DFD066;}
	.st273{fill:#BCC677;}
	.st274{fill:#D1C055;}
	.st275{fill:#EDD469;}
	.st276{fill:#777A3B;}
	.st277{fill:#E2C044;}
	.st278{fill:#E9C546;}
	.st279{fill:#9BA871;}
	.st280{fill:#B3BC7C;}
	.st281{fill:#D9C957;}
	.st282{fill:#6F7644;}
	.st283{fill:#E0C352;}
	.st284{fill:#5E704E;}
	.st285{fill:#909C67;}
	.st286{fill:#666C39;}
	.st287{fill:#797A3A;}
	.st288{fill:#C2C57F;}
	.st289{fill:#818A5A;}
	.st290{fill:#D9CB6B;}
	.st291{fill:#5C6B41;}
	.st292{fill:#C2AF43;}
	.st293{fill:#67794E;}
	.st294{fill:#969344;}
	.st295{fill:#69784F;}
	.st296{fill:#BBB346;}
	.st297{fill:#5A6333;}
	.st298{fill:#A2A660;}
	.st299{fill:#C5C261;}
	.st300{fill:#B3B162;}
	.st301{fill:#6C7239;}
	.st302{fill:#858742;}
	.st303{fill:#69713F;}
	.st304{fill:#B1AD5D;}
	.st305{fill:#A4A760;}
	.st306{fill:#BDAB41;}
	.st307{fill:#919757;}
	.st308{fill:#727840;}
	.st309{fill:#ABB365;}
	.st310{fill:#CCB74D;}
	.st311{fill:#A3A85F;}
	.st312{fill:#BAB258;}
	.st313{fill:#7B8046;}
	.st314{fill:#ABA958;}
	.st315{fill:#F0D14E;}
	.st316{fill:#526240;}
	.st317{fill:#F4E77B;}
	.st318{fill:#E5E083;}
	.st319{fill:#D9D77F;}
	.st320{fill:#F0DC64;}
	.st321{fill:#D4CD71;}
	.st322{fill:#F5DF6E;}
	.st323{fill:#DFD576;}
	.st324{fill:#E7D05A;}
	.st325{fill:#EFD35D;}
	.st326{fill:#DACF79;}
	.st327{fill:#F4ED8F;}
	.st328{fill:#F1EC93;}
	.st329{fill:#E7E593;}
	.st330{fill:#E7EA9D;}
	.st331{fill:#F3DE68;}
	.st332{fill:#F2DD76;}
	.st333{fill:#E2D680;}
	.st334{fill:#E0D670;}
	.st335{fill:#EECA43;}
	.st336{fill:#EEC63D;}
	.st337{fill:#ECC73E;}
	.st338{fill:#F1C63D;}
	.st339{fill:#F1C540;}
	.st340{fill:#E9C73E;}
	.st341{fill:#ECC940;}
	.st342{fill:#F2C741;}
	.st343{fill:#E3C24E;}
	.st344{fill:#546039;}
	.st345{fill:#F4E177;}
	.st346{fill:#F2D059;}
	.st347{fill:#EDD570;}
	.st348{fill:#F4D760;}
	.st349{fill:#F5F093;}
	.st350{fill:#F1EE9D;}
	.st351{fill:#82853C;}
	.st352{fill:#E2E093;}
	.st353{fill:#D7D88A;}
	.st354{fill:#E1DF96;}
	.st355{fill:#EECE4B;}
	.st356{fill:#EDCD47;}
	.st357{fill:#EEC946;}
	.st358{fill:#EECC43;}
	.st359{fill:#EECB49;}
	.st360{fill:#EACE4A;}
	.st361{fill:#F3CA4A;}
	.st362{fill:#EDC738;}
	.st363{fill:#EEC83C;}
	.st364{fill:#F4E67C;}
	.st365{fill:#F1E373;}
	.st366{fill:#F4E981;}
	.st367{fill:#F2E074;}
	.st368{fill:#F1E777;}
	.st369{fill:#F0E16D;}
	.st370{fill:#F0DD76;}
	.st371{fill:#F4E577;}
	.st372{fill:#F0DE6D;}
	.st373{fill:#F3DD67;}
	.st374{fill:#F8E074;}
	.st375{fill:#EDC93C;}
	.st376{fill:#EEC63C;}
	.st377{fill:#E2C441;}
	.st378{fill:#ECC945;}
	.st379{fill:#DFC54E;}
	.st380{fill:#F2E273;}
	.st381{fill:#F2DC67;}
	.st382{fill:#F2D75D;}
	.st383{fill:#F3DD6E;}
	.st384{fill:#F0DA60;}
	.st385{fill:#EECA44;}
	.st386{fill:#EECE47;}
	.st387{fill:#EDD04C;}
	.st388{fill:#F4E578;}
	.st389{fill:#F2E06F;}
	.st390{fill:#F3DF72;}
	.st391{fill:#EFDF67;}
	.st392{fill:#EFD65A;}
	.st393{fill:#EFD14E;}
	.st394{fill:#ECD356;}
	.st395{fill:#F2D257;}
	.st396{fill:#F1D759;}
	.st397{fill:#E7D65F;}
	.st398{fill:#F0D966;}
	.st399{fill:#F3E67E;}
	.st400{fill:#F4E982;}
	.st401{fill:#F5EF90;}
	.st402{fill:#F5EB86;}
	.st403{fill:#ECEFAC;}
	.st404{fill:#F0EEA0;}
	.st405{fill:#F5EC8B;}
	.st406{fill:#F1EC97;}
	.st407{fill:#EFD04E;}
	.st408{fill:#EFD152;}
	.st409{fill:#F0D158;}
	.st410{fill:#F2CE4C;}
	.st411{fill:#EECF47;}
	.st412{fill:#DFC851;}
	.st413{fill:#F4E980;}
	.st414{fill:#F2D75C;}
	.st415{fill:#F1D860;}
	.st416{fill:#F2D25C;}
	.st417{fill:#F0D356;}
	.st418{fill:#E7D153;}
	.st419{fill:#F4D053;}
	.st420{fill:#EECE50;}
	.st421{fill:#EFDA66;}
	.st422{fill:#D9DB97;}
	.st423{fill:#818B4A;}
	.st424{fill:#878540;}
	.st425{fill:#EACB3B;}
	.st426{fill:#F4CE54;}
	.st427{fill:#EFC642;}
	.st428{fill:#EDCC47;}
	.st429{fill:#F5EB87;}
	.st430{fill:#DAC24D;}
	.st431{fill:#EFCA42;}
	.st432{fill:#324924;}
	.st433{fill:#D9D577;}
	.st434{fill:#A7AD6D;}
	.st435{fill:#F0DC5C;}
	.st436{fill:#3E4B23;}
	.st437{fill:#E0D56E;}
	.st438{fill:#465125;}
	.st439{fill:#485628;}
	.st440{fill:#E0CB5A;}
	.st441{fill:#495427;}
	.st442{fill:#71702F;}
	.st443{fill:#F0E064;}
	.st444{fill:#455124;}
	.st445{fill:#ABA867;}
	.st446{fill:#CAC46B;}
	.st447{fill:#4F5C2B;}
	.st448{fill:#D5BE52;}
	.st449{fill:#E4C64F;}
	.st450{fill:#E8C951;}
	.st451{fill:#E5C850;}
	.st452{fill:#E8CC3F;}
	.st453{fill:#E6CE45;}
	.st454{fill:#EEC742;}
	.st455{fill:#ECC83B;}
	.st456{fill:#F7EE89;}
	.st457{fill:#F4EC8A;}
	.st458{fill:#ECE785;}
	.st459{fill:#D3D280;}
	.st460{fill:#D9DC8D;}
	.st461{fill:#DDDD8C;}
	.st462{fill:#909344;}
	.st463{fill:#294529;}
	.st464{fill:#E1DE85;}
	.st465{fill:#5E6533;}
	.st466{fill:#EBCB56;}
	.st467{fill:#E9CE5B;}
	.st468{fill:#D6C456;}
	.st469{fill:#F5CB46;}
	.st470{fill:#F2CB44;}
	.st471{fill:#F0C93F;}
	.st472{fill:#E1C54E;}
	.st473{fill:#EBCC49;}
	.st474{fill:#DDC651;}
	.st475{fill:#ECCD49;}
	.st476{fill:#DDC758;}
	.st477{fill:#F1CD4F;}
	.st478{fill:#EAC935;}
	.st479{fill:#EACA3B;}
	.st480{fill:#E7C950;}
	.st481{fill:#E2C34A;}
	.st482{fill:#DBC346;}
	.st483{fill:#E2C44C;}
	.st484{fill:#E1C648;}
	.st485{fill:#F5EF8F;}
	.st486{fill:#DAD884;}
	.st487{fill:#E6E391;}
	.st488{fill:#E4DC80;}
	.st489{fill:#E3D774;}
	.st490{fill:#F2E06C;}
	.st491{fill:#D2D071;}
	.st492{fill:#EFE46A;}
	.st493{fill:#F1E570;}
	.st494{fill:#DED87C;}
	.st495{fill:#EEDE73;}
	.st496{fill:#EEDE5C;}
	.st497{fill:#D8BD50;}
	.st498{fill:#F0E46E;}
	.st499{fill:#4B572C;}
	.st500{fill:#DAD36D;}
	.st501{fill:#F5D765;}
	.st502{fill:#D6D272;}
	.st503{fill:#E2D467;}
	.st504{fill:#CDC86D;}
	.st505{fill:#E4CA53;}
	.st506{fill:#E0C64E;}
	.st507{fill:#A0963E;}
	.st508{fill:#C9B346;}
	.st509{fill:#F0CE4F;}
	.st510{fill:#CBBD57;}
	.st511{fill:#F1E473;}
	.st512{fill:#EBE17D;}
	.st513{fill:#BDBE6E;}
	.st514{fill:#D0CD78;}
	.st515{fill:#E0CE60;}
	.st516{fill:#EED652;}
	.st517{fill:#F2D862;}
	.st518{fill:#F3D45B;}
	.st519{fill:#D7CA5C;}
	.st520{fill:#E1DC7E;}
	.st521{fill:#E5DE87;}
	.st522{fill:#EDE68C;}
	.st523{fill:#DAC663;}
	.st524{fill:#D8C65F;}
	.st525{fill:#F3D053;}
	.st526{fill:#D5C759;}
	.st527{fill:#CCBD5D;}
	.st528{fill:#F4EC8B;}
	.st529{fill:#DDDC89;}
	.st530{fill:#E8E792;}
	.st531{fill:#E2CF65;}
	.st532{fill:#F0D651;}
	.st533{fill:#E3D568;}
	.st534{fill:#E9D264;}
	.st535{fill:#F4F095;}
	.st536{fill:#F3EE91;}
	.st537{fill:#354824;}
	.st538{fill:#3E4D25;}
	.st539{fill:#ECCC40;}
	.st540{fill:#F5C847;}
	.st541{fill:#E6E89D;}
	.st542{fill:#2A4427;}
	.st543{fill:#2A4527;}
	.st544{fill:#29432B;}
	.st545{fill:#384621;}
	.st546{fill:#51592A;}
	.st547{fill:#D4C352;}
	.st548{fill:#FFFFFC;}
	.st549{fill:#2A472E;}
	.st550{fill:#2A482D;}
	.st551{fill:#2C4830;}
	.st552{fill:#2A472D;}
	.st553{fill:#29482C;}
	.st554{fill:#2B482E;}
	.st555{fill:#384521;}
	.st556{fill:#2C472F;}
	.st557{fill:#2B472D;}
	.st558{fill:#334A34;}
	.st559{fill:#2E4931;}
	.st560{fill:#304934;}
	.st561{fill:#2C482F;}
	.st562{fill:#324D35;}
	.st563{fill:#EAEDE7;}
	.st564{fill:#465226;}
	.st565{fill:#3E5140;}
	.st566{fill:#334B37;}
	.st567{fill:#E2E8E2;}
	.st568{fill:#424E23;}
	.st569{fill:#DAE1D8;}
	.st570{fill:#304A33;}
	.st571{fill:#E8ECE6;}
	.st572{fill:#6D6D2D;}
	.st573{fill:#B3BFB1;}
	.st574{fill:#465026;}
	.st575{fill:#ADBAAF;}
	.st576{fill:#455024;}
	.st577{fill:#454F24;}
	.st578{fill:#E1E5DF;}
	.st579{fill:#404E25;}
	.st580{fill:#455025;}
	.st581{fill:#626A2E;}
	.st582{fill:#EDF1EC;}
	.st583{fill:#384F3B;}
	.st584{fill:#455645;}
	.st585{fill:#B6C2B6;}
	.st586{fill:#617162;}
	.st587{fill:#244A2A;}
	.st588{fill:#B9C3B7;}
	.st589{fill:#3F4B23;}
	.st590{fill:#D8E1D8;}
	.st591{fill:#4D5527;}
	.st592{fill:#D0C761;}
	.st593{fill:#4D5526;}
	.st594{fill:#434D25;}
	.st595{fill:#B4C1B7;}
	.st596{fill:#B9C8BA;}
	.st597{fill:#83914B;}
	.st598{fill:#BCC6BC;}
	.st599{fill:#526A51;}
	.st600{fill:#E3E8E2;}
	.st601{fill:#495327;}
	.st602{fill:#415228;}
	.st603{fill:#294726;}
	.st604{fill:#44552D;}
	.st605{fill:#475946;}
	.st606{fill:#E5E9E3;}
	.st607{fill:#B9C6BC;}
	.st608{fill:#ADBAAB;}
	.st609{fill:#424D24;}
	.st610{fill:#535C29;}
	.st611{fill:#5F642E;}
	.st612{fill:#C8D3C9;}
	.st613{fill:#A6B9A8;}
	.st614{fill:#C7D2C9;}
	.st615{fill:#4C5226;}
	.st616{fill:#CDD6CC;}
	.st617{fill:#CED7C9;}
	.st618{fill:#5B6831;}
	.st619{fill:#E1E6E0;}
	.st620{fill:#E6EBE6;}
	.st621{fill:#3E4C24;}
	.st622{fill:#D4DDD5;}
	.st623{fill:#B4BFAF;}
	.st624{fill:#495526;}
	.st625{fill:#C5CFC1;}
	.st626{fill:#3B4E3D;}
	.st627{fill:#F1F4F1;}
	.st628{fill:#DBDB8C;}
	.st629{fill:#5C642D;}
	.st630{fill:#667867;}
	.st631{fill:#474E24;}
	.st632{fill:#C6D3C3;}
	.st633{fill:#687034;}
	.st634{fill:#CCD6CA;}
	.st635{fill:#C8D7C7;}
	.st636{fill:#4C5728;}
	.st637{fill:#575E2B;}
	.st638{fill:#455328;}
	.st639{fill:#EAEFE9;}
	.st640{fill:#3E4B24;}
	.st641{fill:#434F25;}
	.st642{fill:#424E25;}
	.st643{fill:#2B492E;}
	.st644{fill:#C3D1C1;}
	.st645{fill:#D0BC52;}
	.st646{fill:#445126;}
	.st647{fill:#4A5829;}
	.st648{fill:#E0E4DE;}
	.st649{fill:#435225;}
	.st650{fill:#6A7A69;}
	.st651{fill:#B9C6B9;}
	.st652{fill:#435328;}
	.st653{fill:#314321;}
	.st654{fill:#485326;}
	.st655{fill:#5D755E;}
	.st656{fill:#434F24;}
	.st657{fill:#C5CDC3;}
	.st658{fill:#BCCABC;}
	.st659{fill:#647466;}
	.st660{fill:#5F725F;}
	.st661{fill:#E8EDE7;}
	.st662{fill:#5D745D;}
	.st663{fill:#354521;}
	.st664{fill:#EAEFE8;}
	.st665{fill:#EEF2ED;}
	.st666{fill:#3C4621;}
	.st667{fill:#506252;}
	.st668{fill:#3F4D25;}
	.st669{fill:#A5B8A3;}
	.st670{fill:#A4B9A4;}
	.st671{fill:#354320;}
	.st672{fill:#616029;}
	.st673{fill:#738072;}
	.st674{fill:#7C8545;}
	.st675{fill:#607332;}
	.st676{fill:#B2C0B2;}
	.st677{fill:#B6B860;}
	.st678{fill:#CED6CD;}
	.st679{fill:#9AB09A;}
	.st680{fill:#B4C0B5;}
	.st681{fill:#5C695D;}
	.st682{fill:#6B7D6E;}
	.st683{fill:#9DB19F;}
	.st684{fill:#617061;}
	.st685{fill:#6D876F;}
	.st686{fill:#3F4B24;}
	.st687{fill:#667868;}
	.st688{fill:#C4D2C3;}
	.st689{fill:#ECF0EB;}
	.st690{fill:#B3C0B0;}
	.st691{fill:#3B4922;}
	.st692{fill:#708171;}
	.st693{fill:#3F4C24;}
	.st694{fill:#A79A41;}
	.st695{fill:#DABF4C;}
	.st696{fill:#BDAC4A;}
	.st697{fill:#7F7E35;}
	.st698{fill:#A59F45;}
	.st699{fill:#F3EA81;}
	.st700{fill:#F6EF8E;}
	.st701{fill:#F6F092;}
	.st702{fill:#F8E984;}
	.st703{fill:#EED242;}
	.st704{fill:#C0BF71;}
	.st705{fill:#ECCD41;}
	.st706{fill:#EDD34B;}
	.st707{fill:#F9EF95;}
	.st708{fill:#F1EB93;}
	.st709{fill:#F3EB80;}
	.st710{fill:#E6CC37;}
	.st711{fill:#E6CF49;}
	.st712{fill:#EDCF44;}
	.st713{fill:#E5C936;}
	.st714{fill:#F1C833;}
	.st715{fill:#F4E672;}
	.st716{fill:#F0D849;}
	.st717{fill:#F2E780;}
	.st718{fill:#BEC078;}
	.st719{fill:#F6E377;}
	.st720{fill:#DDD06A;}
	.st721{fill:#E4D870;}
	.st722{fill:#F4E574;}
	.st723{fill:#374623;}
	.st724{fill:#435229;}
	.st725{fill:#DDD272;}
	.st726{fill:#314824;}
	.st727{fill:#3E5128;}
	.st728{fill:#304824;}
	.st729{fill:#31432C;}
	.st730{fill:#D3C163;}
	.st731{fill:#BBAB50;}
	.st732{fill:#BFAE50;}
	.st733{fill:#DABE48;}
	.st734{fill:#CAB94E;}
	.st735{fill:#F1EF9B;}
	.st736{fill:#EDEFAB;}
	.st737{fill:#DDE5A0;}
	.st738{fill:#A4A35D;}
	.st739{fill:#D5D372;}
	.st740{fill:#E0CE5D;}
	.st741{fill:#E1E089;}
	.st742{fill:#2A422E;}
	.st743{fill:#72753E;}
	.st744{fill:#434B23;}
	.st745{fill:#2A482E;}
	.st746{fill:#E3C442;}
	.st747{fill:#3A4520;}
	.st748{fill:#E0BB39;}
	.st749{fill:#D2BA51;}
	.st750{fill:#A48E3A;}
	.st751{fill:#D2DDD0;}
	.st752{fill:#E2E8E1;}
	.st753{fill:#ECF0EA;}
	.st754{fill:#C0CDC3;}
	.st755{fill:#B8C5B7;}
	.st756{fill:#B5C3B6;}
	.st757{fill:#E7ECE7;}
	.st758{fill:#C5D4C7;}
	.st759{fill:#ABBEAD;}
	.st760{fill:#F5F7F5;}
	.st761{fill:#ECEFEB;}
	.st762{fill:#E4E9E1;}
	.st763{fill:#BFCCBE;}
	.st764{fill:#E9EDE8;}
	.st765{fill:#8B9D8E;}
	.st766{fill:#E5EAE5;}
	.st767{fill:#78897A;}
	.st768{fill:#A9ABAA;}
	.st769{fill:#9EAD9E;}
	.st770{fill:#CFD7CB;}
	.st771{fill:#6B776C;}
	.st772{fill:#ACB8AF;}
	.st773{fill:#9EAC9A;}
	.st774{fill:#FCFCFB;}
	.st775{fill:#26482A;}
	.st776{fill:#28482A;}
	.st777{fill:#2C4930;}
	.st778{fill:#6C7D71;}
	.st779{fill:#ADBAAD;}
	.st780{fill:#525E53;}
	.st781{fill:#B1BBB2;}
	.st782{fill:#B1BBB1;}
	.st783{fill:#A9B5A9;}
	.st784{fill:#647365;}
	.st785{fill:#A8B1A4;}
	.st786{fill:#58695A;}
	.st787{fill:#546656;}
	.st788{fill:#264A2A;}
	.st789{fill:#D6DED7;}
	.st790{fill:#334E38;}
	.st791{fill:#364B38;}
	.st792{fill:#EAEDE9;}
	.st793{fill:#637763;}
	.st794{fill:#2C492F;}
	.st795{fill:#405140;}
	.st796{fill:#5C6D60;}
	.st797{fill:#4A5E4D;}
	.st798{fill:#E4E9E2;}
	.st799{fill:#99AE9D;}
	.st800{fill:#274B2A;}
	.st801{fill:#3F5441;}
	.st802{fill:#DAE1DB;}
	.st803{fill:#59705B;}
	.st804{fill:#ACB1AD;}
	.st805{fill:#344F37;}
	.st806{fill:#BDD2C0;}
	.st807{fill:#6F7F6E;}
	.st808{fill:#6D796F;}
	.st809{fill:#A9B4AB;}
	.st810{fill:#A3B1A8;}
	.st811{fill:#314B34;}
	.st812{fill:#384E3B;}
	.st813{fill:#A0AE9F;}
	.st814{fill:#AEBDAE;}
	.st815{fill:#2B482F;}
	.st816{fill:#264929;}
	.st817{fill:#596D5B;}
	.st818{fill:#AFB8AE;}
	.st819{fill:#627262;}
	.st820{fill:#28482B;}
	.st821{fill:#2E4832;}
	.st822{fill:#304532;}
	.st823{fill:#CDD6CA;}
	.st824{fill:#C0CEC1;}
	.st825{fill:#E8ECE5;}
	.st826{fill:#BAC5BA;}
	.st827{fill:#708271;}
	.st828{fill:#BDAD4D;}
	.st829{fill:#D9CA57;}
	.st830{fill:#D3BB4B;}
	.st831{fill:#DDD16E;}
	.st832{fill:#DECE69;}
	.st833{fill:#E0D865;}
	.st834{fill:#7E7A31;}
	.st835{fill:#7C6F35;}
	.st836{fill:#28492A;}
	.st837{fill:#D9DFD6;}
	.st838{fill:#566C5B;}
	.st839{fill:#C1CBC2;}
	.st840{fill:#AEC1B0;}
	.st841{fill:#A6B7AB;}
	.st842{fill:#617164;}
	.st843{fill:#C9D3C9;}
	.st844{fill:#B5C0B7;}
	.st845{fill:#374F3B;}
	.st846{fill:#405844;}
	.st847{fill:#274A2B;}
	.st848{fill:#C8D3C8;}
	.st849{fill:#2F4932;}
	.st850{fill:#E1E6DF;}
	.st851{fill:#EBEFEA;}
	.st852{fill:#485C4A;}
	.st853{fill:#BCCBB9;}
	.st854{fill:#687C6D;}
	.st855{fill:#ACBAAD;}
	.st856{fill:#2A492D;}
	.st857{fill:#E4E8E2;}
	.st858{fill:#2E4A33;}
	.st859{fill:#BDCBBD;}
	.st860{fill:#E3E8E0;}
	.st861{fill:#A4B3A6;}
	.st862{fill:#B3C2B7;}
	.st863{fill:#566A57;}
	.st864{fill:#57645A;}
	.st865{fill:#D8E0D4;}
	.st866{fill:#9CA9A0;}
	.st867{fill:#29482B;}
	.st868{fill:#2D472F;}
	.st869{fill:#B6C4B7;}
	.st870{fill:#B3C5B7;}
	.st871{fill:#F3F5F0;}
	.st872{fill:#BBC3B5;}
	.st873{fill:#B2C0B4;}
	.st874{fill:#ACB9AC;}
	.st875{fill:#C3D3C4;}
	.st876{fill:#515F52;}
	.st877{fill:#ABB9A7;}
	.st878{fill:#264A29;}
	.st879{fill:#2F4A31;}
	.st880{fill:#97A298;}
	.st881{fill:#A1B2A6;}
	.st882{fill:#F2F6F2;}
	.st883{fill:#CDD5C9;}
	.st884{fill:#EBEFEB;}
	.st885{fill:#284B2A;}
	.st886{fill:#E6EBE5;}
	.st887{fill:#576E57;}
	.st888{fill:#D6E0D6;}
	.st889{fill:#324C36;}
	.st890{fill:#C1CDC0;}
	.st891{fill:#CFD8CF;}
	.st892{fill:#B2BDAD;}
	.st893{fill:#4F5D53;}
	.st894{fill:#BECABC;}
	.st895{fill:#E0CC5F;}
	.st896{fill:#DDC550;}
	.st897{fill:#BFB048;}
	.st898{fill:#F2F4F0;}
	.st899{fill:#FCFCFA;}
	.st900{fill:#F2F3F0;}
	.st901{fill:#EAEEE9;}
	.st902{fill:#DFE4DF;}
	.st903{fill:#F0F1ED;}
	.st904{fill:#B9C5B8;}
	.st905{fill:#E8C63E;}
	.st906{fill:#F6ECC2;}
	.st907{fill:#B1BDB0;}
	.st908{fill:#EFF2ED;}
	.st909{fill:#CFD7CE;}
	.st910{fill:#B7C3B5;}
	.st911{fill:#E8D15E;}
	.st912{fill:#E7DB73;}
	.st913{fill:#E3C84B;}
	.st914{fill:#BEAF47;}
	.st915{fill:#FBFAF3;}
	.st916{fill:#29482A;}
	.st917{fill:#415647;}
	.st918{fill:#E8DF7E;}
	.st919{fill:#A79D4D;}
	.st920{fill:#E9C441;}
	.st921{fill:#E6C73D;}
	.st922{fill:#E8D57E;}
	.st923{fill:#DFC75C;}
	.st924{fill:#F0E475;}
	.st925{fill:#F9E680;}
	.st926{fill:#EDDB75;}
	.st927{fill:#F4F097;}
	.st928{fill:#F7F092;}
	.st929{fill:#F9EC80;}
	.st930{fill:#FCF189;}
	.st931{fill:#364B24;}
	.st932{fill:#E7D76D;}
	.st933{fill:#374925;}
	.st934{fill:#F9E675;}
	.st935{fill:#EAC420;}
	.st936{fill:#EDCC2E;}
	.st937{fill:#FCEF9A;}
	.st938{fill:#F6EE8D;}
	.st939{fill:#B0B05F;}
	.st940{fill:#FAE668;}
	.st941{fill:#F7E275;}
	.st942{fill:#4A532A;}
	.st943{fill:#D1CE79;}
	.st944{fill:#E8DB6F;}
	.st945{fill:#F7DF6D;}
	.st946{fill:#F9E069;}
	.st947{fill:#F0E36A;}
	.st948{fill:#EACE2E;}
	.st949{fill:#EFC832;}
	.st950{fill:#F2DF69;}
	.st951{fill:#F5DC63;}
	.st952{fill:#F2DB64;}
	.st953{fill:#DFD067;}
	.st954{fill:#E7DB69;}
	.st955{fill:#E8CA50;}
	.st956{fill:#284B24;}
	.st957{fill:#F0C634;}
	.st958{fill:#EEC746;}
	.st959{fill:#C3B156;}
	.st960{fill:#717043;}
	.st961{fill:#887B37;}
	.st962{fill:#ECC93F;}
	.st963{fill:#5A6D5C;}
	.st964{fill:#5C6F5D;}
	.st965{fill:#EAEFEA;}
	.st966{fill:#ABB4AA;}
	.st967{fill:#B6C1B7;}
	.st968{fill:#FCF9E6;}
	.st969{fill:#B0C3B1;}
	.st970{fill:#909890;}
	.st971{fill:#B0C0B1;}
	.st972{fill:#F1F4F0;}
	.st973{fill:#274A2A;}
	.st974{fill:#354E38;}
	.st975{fill:#708573;}
	.st976{fill:#A8B6AB;}
	.st977{fill:#C9D6C9;}
	.st978{fill:#748374;}
	.st979{fill:#EDEFEB;}
	.st980{fill:#CBD6CD;}
	.st981{fill:#506652;}
	.st982{fill:#354E37;}
	.st983{fill:#5D695F;}
	.st984{fill:#E7EDE6;}
	.st985{fill:#274B2B;}
	.st986{fill:#314C23;}
	.st987{fill:#284A2C;}
	.st988{fill:#5E6B31;}
	.st989{fill:#2B492F;}
	.st990{fill:#2F4A32;}
	.st991{fill:#254D2B;}
	.st992{fill:#28482E;}
	.st993{fill:#E7ECE5;}
	.st994{fill:#B7C9BA;}
	.st995{fill:#E8EEE9;}
	.st996{fill:#ECF1EB;}
	.st997{fill:#F0F6F1;}
	.st998{fill:#DFE5DE;}
	.st999{fill:#D0D9CA;}
	.st1000{fill:#D3DDD3;}
	.st1001{fill:#BCCEBA;}
	.st1002{fill:#C3CEC3;}
	.st1003{fill:#405245;}
	.st1004{fill:#29492E;}
	.st1005{fill:#2D4C32;}
	.st1006{fill:#E0E6DF;}
	.st1007{fill:#EAEEEA;}
	.st1008{fill:#CCDACA;}
	.st1009{fill:#EDF1ED;}
	.st1010{fill:#AABAAF;}
	.st1011{fill:#95A097;}
	.st1012{fill:#EEF3EE;}
	.st1013{fill:#2A492C;}
	.st1014{fill:#3B4D3C;}
	.st1015{fill:#334C36;}
	.st1016{fill:#435744;}
	.st1017{fill:#304932;}
	.st1018{fill:#2D4B31;}
	.st1019{fill:#667969;}
	.st1020{fill:#788B7B;}
	.st1021{fill:#EBEEE8;}
	.st1022{fill:#B7C7B6;}
	.st1023{fill:#6D7E6E;}
	.st1024{fill:#758475;}
	.st1025{fill:#E0C14B;}
	.st1026{fill:#E4C43F;}
	.st1027{fill:#E7C843;}
	.st1028{fill:#F1CC4D;}
	.st1029{fill:#F3D354;}
	.st1030{fill:#F5D55C;}
	.st1031{fill:#F1C640;}
	.st1032{fill:#DFC452;}
	.st1033{fill:#2B4830;}
	.st1034{fill:#2C4929;}
	.st1035{fill:#CFDAD2;}
	.st1036{fill:#B0B5B0;}
	.st1037{fill:#354F38;}
	.st1038{fill:#59665A;}
	.st1039{fill:#3C5340;}
	.st1040{fill:#394D24;}
	.st1041{fill:#274734;}
	.st1042{fill:#E7EBE4;}
	.st1043{fill:#6E8370;}
	.st1044{fill:#28492B;}
	.st1045{fill:#D3DCD1;}
	.st1046{fill:#485E48;}
	.st1047{fill:#D4DED6;}
	.st1048{fill:#344C36;}
	.st1049{fill:#E2E7DF;}
	.st1050{fill:#A9B6AA;}
	.st1051{fill:#B2BEB2;}
	.st1052{fill:#E2E8E3;}
	.st1053{fill:#C2CFC1;}
	.st1054{fill:#C1CDC5;}
	.st1055{fill:#DEE8DD;}
	.st1056{fill:#637363;}
	.st1057{fill:#7C8F7D;}
	.st1058{fill:#A4B4A8;}
	.st1059{fill:#B1C2B0;}
	.st1060{fill:#717870;}
	.st1061{fill:#344D35;}
	.st1062{fill:#95AE96;}
	.st1063{fill:#3F5440;}
	.st1064{fill:#3E613F;}
	.st1065{fill:#334D33;}
	.st1066{fill:#B4C1B6;}
	.st1067{fill:#BAC6BA;}
	.st1068{fill:#A6B9A7;}
	.st1069{fill:#6D806E;}
	.st1070{fill:#E8EBE5;}
	.st1071{fill:#DCE5DC;}
	.st1072{fill:#5D735E;}
	.st1073{fill:#E8ECE8;}
	.st1074{fill:#647B63;}
	.st1075{fill:#364E38;}
	.st1076{fill:#E2E5DD;}
	.st1077{fill:#D6D5D2;}
	.st1078{fill:#D5E0D6;}
	.st1079{fill:#CBD7CC;}
	.st1080{fill:#B0BFB0;}
	.st1081{fill:#E5EBE4;}
	.st1082{fill:#6F766F;}
	.st1083{fill:#B4BCB4;}
	.st1084{fill:#B6C1B5;}
	.st1085{fill:#BBC6BB;}
	.st1086{fill:#E7E9E3;}
	.st1087{fill:#C7D2C7;}
	.st1088{fill:#B5C3B5;}
	.st1089{fill:#697A6C;}
	.st1090{fill:#A2B1A4;}
	.st1091{fill:#EEF1EA;}
	.st1092{fill:#EBF0EC;}
	.st1093{fill:#B5C3B8;}
	.st1094{fill:#E8DA6A;}
	.st1095{fill:#D2BD4E;}
	.st1096{fill:#AAA959;}
	.st1097{fill:#A19A3D;}
	.st1098{fill:#E2C347;}
	.st1099{fill:#A3B0A4;}
	.st1100{fill:#CBD8CA;}
	.st1101{fill:#A6B6A5;}
	.st1102{fill:#BDC9BF;}
	.st1103{fill:#AEBAAE;}
	.st1104{fill:#ECCC66;}
	.st1105{fill:#EBC644;}
	.st1106{fill:#D1BF48;}
	.st1107{fill:#BEAD4D;}
	.st1108{fill:#D9C146;}
	.st1109{fill:#F8F0C7;}
	.st1110{fill:#B9C8BB;}
	.st1111{fill:#D5DED5;}
	.st1112{fill:#C3CFC3;}
	.st1113{fill:#94A493;}
	.st1114{fill:#304B35;}
	.st1115{fill:#D7E1D7;}
	.st1116{fill:#B4BEB6;}
	.st1117{fill:#B7C1B8;}
	.st1118{fill:#B3C8B4;}
	.st1119{fill:#C1D0C1;}
	.st1120{fill:#EFF3EF;}
	.st1121{fill:#E3E7E1;}
	.st1122{fill:#BAC7BA;}
	.st1123{fill:#A8B7AE;}
	.st1124{fill:#DDD379;}
	.st1125{fill:#CEBF55;}
	.st1126{fill:#F0E881;}
	.st1127{fill:#ECD986;}
	.st1128{fill:#455748;}
	.st1129{fill:#D0DFD3;}
	.st1130{fill:#EFEFA2;}
	.st1131{fill:#E4D672;}
	.st1132{fill:#C8B348;}
	.st1133{fill:#E5C540;}
	.st1134{fill:#DEC04C;}
	.st1135{fill:#F5EA83;}
	.st1136{fill:#F4E880;}
	.st1137{fill:#F5EC8A;}
	.st1138{fill:#F3EC8D;}
	.st1139{fill:#FAEE87;}
	.st1140{fill:#EFEEA3;}
	.st1141{fill:#ECD04B;}
	.st1142{fill:#27482B;}
	.st1143{fill:#444D24;}
	.st1144{fill:#7B803B;}
	.st1145{fill:#F2DD64;}
	.st1146{fill:#D7D578;}
	.st1147{fill:#F2D965;}
	.st1148{fill:#FDF38F;}
	.st1149{fill:#F5E880;}
	.st1150{fill:#F1EB80;}
	.st1151{fill:#EDEA9D;}
	.st1152{fill:#FBF08D;}
	.st1153{fill:#284828;}
	.st1154{fill:#384924;}
	.st1155{fill:#F4E277;}
	.st1156{fill:#F1DD65;}
	.st1157{fill:#F3DE6A;}
	.st1158{fill:#F7DB71;}
	.st1159{fill:#EACA3C;}
	.st1160{fill:#EAC42D;}
	.st1161{fill:#E7CA33;}
	.st1162{fill:#FAEB6A;}
	.st1163{fill:#EBE160;}
	.st1164{fill:#F4D95D;}
	.st1165{fill:#F3D355;}
	.st1166{fill:#F3D85B;}
	.st1167{fill:#EFC62C;}
	.st1168{fill:#F0DD57;}
	.st1169{fill:#F1D05E;}
	.st1170{fill:#F2CA3F;}
	.st1171{fill:#EBC631;}
	.st1172{fill:#F4D047;}
	.st1173{fill:#ECC52C;}
	.st1174{fill:#ECC527;}
	.st1175{fill:#ECBF21;}
	.st1176{fill:#F1C740;}
	.st1177{fill:#F1C32F;}
	.st1178{fill:#D7C057;}
	.st1179{fill:#DDC754;}
	.st1180{fill:#4E5527;}
	.st1181{fill:#F4CB44;}
	.st1182{fill:#F5C74B;}
	.st1183{fill:#E6CA53;}
	.st1184{fill:#F7D849;}
	.st1185{fill:#DDBF4D;}
	.st1186{fill:#AD9534;}
	.st1187{fill:#2E4B33;}
	.st1188{fill:#485726;}
	.st1189{fill:#546A57;}
	.st1190{fill:#58745C;}
	.st1191{fill:#334F36;}
	.st1192{fill:#314C34;}
	.st1193{fill:#304C34;}
	.st1194{fill:#2E4A32;}
	.st1195{fill:#7F8680;}
	.st1196{fill:#274D2C;}
	.st1197{fill:#264D2B;}
	.st1198{fill:#274F2C;}
	.st1199{fill:#254D2A;}
	.st1200{fill:#335038;}
	.st1201{fill:#2A4B2E;}
	.st1202{fill:#415426;}
	.st1203{fill:#284D27;}
	.st1204{fill:#39553D;}
	.st1205{fill:#345039;}
	.st1206{fill:#516A55;}
	.st1207{fill:#234F29;}
	.st1208{fill:#294E30;}
	.st1209{fill:#365339;}
	.st1210{fill:#344F38;}
	.st1211{fill:#284C2C;}
	.st1212{fill:#2F4E35;}
	.st1213{fill:#254F2A;}
	.st1214{fill:#36533A;}
	.st1215{fill:#274B2E;}
	.st1216{fill:#284A2E;}
	.st1217{fill:#2B4F30;}
	.st1218{fill:#365037;}
	.st1219{fill:#304C26;}
	.st1220{fill:#3B5024;}
	.st1221{fill:#385740;}
	.st1222{fill:#314924;}
	.st1223{fill:#274831;}
	.st1224{fill:#344D37;}
	.st1225{fill:#274C26;}
	.st1226{fill:#415746;}
	.st1227{fill:#49523F;}
	.st1228{fill:#758375;}
	.st1229{fill:#254C28;}
	.st1230{fill:#234D28;}
	.st1231{fill:#284830;}
	.st1232{fill:#415428;}
	.st1233{fill:#4B644F;}
	.st1234{fill:#334E35;}
	.st1235{fill:#415226;}
	.st1236{fill:#496131;}
	.st1237{fill:#484F22;}
	.st1238{fill:#D3BF50;}
	.st1239{fill:#475224;}
	.st1240{fill:#D1C35B;}
	.st1241{fill:#E8D16C;}
	.st1242{fill:#D2BF49;}
	.st1243{fill:#BFB34F;}
	.st1244{fill:#A3A659;}
	.st1245{fill:#2D4931;}
	.st1246{fill:#586A59;}
	.st1247{fill:#5F675E;}
	.st1248{fill:#728171;}
	.st1249{fill:#AAB9AD;}
	.st1250{fill:#58755D;}
	.st1251{fill:#829084;}
	.st1252{fill:#EECF43;}
	.st1253{fill:#DBC652;}
	.st1254{fill:#EEC63A;}
	.st1255{fill:#F3CA3D;}
	.st1256{fill:#E9C430;}
	.st1257{fill:#E7C83F;}
	.st1258{fill:#F0C735;}
	.st1259{fill:#ECC32B;}
	.st1260{fill:#EAC82D;}
	.st1261{fill:#EFCF48;}
	.st1262{fill:#EBCC3F;}
	.st1263{fill:#F0D653;}
	.st1264{fill:#E0CF57;}
	.st1265{fill:#EAC72D;}
	.st1266{fill:#D2D7D4;}
	.st1267{fill:#D8E3D8;}
	.st1268{fill:#444C21;}
	.st1269{fill:#5B5A26;}
	.st1270{fill:#434C21;}
	.st1271{fill:#254C29;}
	.st1272{fill:#DBE3DB;}
	.st1273{fill:#979C96;}
	.st1274{fill:#3E5640;}
	.st1275{fill:#D0B84C;}
	.st1276{fill:#374E24;}
	.st1277{fill:#4D6256;}
	.st1278{fill:#465425;}
	.st1279{fill:#424E22;}
	.st1280{fill:#28462A;}
	.st1281{fill:#2A4828;}
	.st1282{fill:#485529;}
	.st1283{fill:#E6C544;}
	.st1284{fill:#3C4B22;}
	.st1285{fill:#B4A244;}
	.st1286{fill:#CAB74A;}
	.st1287{fill:#797A31;}
	.st1288{fill:#F7E671;}
	.st1289{fill:#EFD047;}
	.st1290{fill:#E1C937;}
	.st1291{fill:#F6E371;}
	.st1292{fill:#EDDE78;}
	.st1293{fill:#EBD144;}
	.st1294{fill:#EBD040;}
	.st1295{fill:#435023;}
	.st1296{fill:#FAEC78;}
	.st1297{fill:#294632;}
	.st1298{fill:#F5D862;}
	.st1299{fill:#F7E16E;}
	.st1300{fill:#F0DC5B;}
	.st1301{fill:#EBC337;}
	.st1302{fill:#F3E369;}
	.st1303{fill:#EDE275;}
	.st1304{fill:#E8D976;}
	.st1305{fill:#FCEE85;}
	.st1306{fill:#29462E;}
	.st1307{fill:#F6E06C;}
	.st1308{fill:#E9D96C;}
	.st1309{fill:#F0E573;}
	.st1310{fill:#EFE471;}
	.st1311{fill:#E6DE8A;}
	.st1312{fill:#F5E163;}
	.st1313{fill:#F0DC62;}
	.st1314{fill:#F6DB64;}
	.st1315{fill:#F8E05B;}
	.st1316{fill:#EEC936;}
	.st1317{fill:#EDD654;}
	.st1318{fill:#F6D161;}
	.st1319{fill:#F4CD5E;}
	.st1320{fill:#F6CF41;}
	.st1321{fill:#F9CD49;}
	.st1322{fill:#F1D454;}
	.st1323{fill:#F4D247;}
	.st1324{fill:#EABE2C;}
	.st1325{fill:#FCF395;}
	.st1326{fill:#FBF396;}
	.st1327{fill:#FBE977;}
	.st1328{fill:#474B1F;}
	.st1329{fill:#EDCD45;}
	.st1330{fill:#E3C454;}
	.st1331{fill:#EFD35A;}
	.st1332{fill:#EBD55F;}
	.st1333{fill:#ACA044;}
	.st1334{fill:#364522;}
	.st1335{fill:#284822;}
	.st1336{fill:#EAC621;}
	.st1337{fill:#EED245;}
	.st1338{fill:#F5CC55;}
	.st1339{fill:#EECA37;}
	.st1340{fill:#FCEA76;}
	.st1341{fill:#E6C53E;}
	.st1342{fill:#C5BB4C;}
	.st1343{fill:#D7C147;}
	.st1344{fill:#D5C452;}
	.st1345{fill:#24512B;}
	.st1346{fill:#264E2D;}
	.st1347{fill:#234F2B;}
	.st1348{fill:#244F2B;}
	.st1349{fill:#2C4E33;}
	.st1350{fill:#2B4C31;}
	.st1351{fill:#2F4F33;}
	.st1352{fill:#2E5033;}
	.st1353{fill:#2C4C33;}
	.st1354{fill:#33533B;}
	.st1355{fill:#274A2D;}
	.st1356{fill:#325138;}
	.st1357{fill:#2E4F35;}
	.st1358{fill:#365127;}
	.st1359{fill:#2F4F26;}
	.st1360{fill:#294931;}
	.st1361{fill:#2B4F31;}
	.st1362{fill:#325036;}
	.st1363{fill:#2C4F32;}
	.st1364{fill:#47604C;}
	.st1365{fill:#35533B;}
	.st1366{fill:#2F4E33;}
	.st1367{fill:#244E2B;}
	.st1368{fill:#305035;}
	.st1369{fill:#627668;}
	.st1370{fill:#374F3C;}
	.st1371{fill:#345338;}
	.st1372{fill:#B4C9BB;}
	.st1373{fill:#B2C2B1;}
	.st1374{fill:#687A6D;}
	.st1375{fill:#305336;}
	.st1376{fill:#26502B;}
	.st1377{fill:#25502B;}
	.st1378{fill:#2E5233;}
	.st1379{fill:#3C5942;}
	.st1380{fill:#274E2E;}
	.st1381{fill:#CAD6CC;}
	.st1382{fill:#ABB8AD;}
	.st1383{fill:#697E6C;}
	.st1384{fill:#809882;}
	.st1385{fill:#3E5125;}
	.st1386{fill:#E5D26A;}
	.st1387{fill:#ECE280;}
	.st1388{fill:#E6CE5E;}
	.st1389{fill:#E5D164;}
	.st1390{fill:#DCDE89;}
	.st1391{fill:#E3DB78;}
	.st1392{fill:#E2DA78;}
	.st1393{fill:#A8A545;}
	.st1394{fill:#E0E5DB;}
	.st1395{fill:#C3D2C2;}
	.st1396{fill:#2B4E33;}
	.st1397{fill:#C9D7C9;}
	.st1398{fill:#647665;}
	.st1399{fill:#516954;}
	.st1400{fill:#566958;}
	.st1401{fill:#E7ECE6;}
	.st1402{fill:#576F5B;}
	.st1403{fill:#A3B6A9;}
	.st1404{fill:#B7C8BA;}
	.st1405{fill:#F9FAF9;}
	.st1406{fill:#697C6D;}
	.st1407{fill:#BAC3B8;}
	.st1408{fill:#95A595;}
	.st1409{fill:#D3DFD6;}
	.st1410{fill:#B8C8BB;}
	.st1411{fill:#575926;}
	.st1412{fill:#4C5E2C;}
	.st1413{fill:#D1BD47;}
	.st1414{fill:#2D4520;}
	.st1415{fill:#55672F;}
	.st1416{fill:#F2EC8E;}
	.st1417{fill:#E8C84B;}
	.st1418{fill:#EEDC6B;}
	.st1419{fill:#E9CF51;}
	.st1420{fill:#E9D55D;}
	.st1421{fill:#BEAD48;}
	.st1422{fill:#EED65C;}
	.st1423{fill:#EAC53F;}
	.st1424{fill:#F3EB85;}
	.st1425{fill:#F1E77C;}
	.st1426{fill:#EDC22B;}
	.st1427{fill:#EECB50;}
	.st1428{fill:#EDD04E;}
	.st1429{fill:#F5D34E;}
	.st1430{fill:#F1C739;}
	.st1431{fill:#EBD652;}
	.st1432{fill:#E0CE64;}
	.st1433{fill:#F2D65A;}
	.st1434{fill:#EAD262;}
	.st1435{fill:#EFC427;}
	.st1436{fill:#F5C437;}
	.st1437{fill:#F8E873;}
	.st1438{fill:#F3C83B;}
	.st1439{fill:#ECC022;}
	.st1440{fill:#E6C32F;}
	.st1441{fill:#ECCE3D;}
	.st1442{fill:#F6DD55;}
	.st1443{fill:#ECC125;}
	.st1444{fill:#E2C624;}
	.st1445{fill:#FDF298;}
	.st1446{fill:#F5C94D;}
	.st1447{fill:#D4C257;}
	.st1448{fill:#F9D156;}
	.st1449{fill:#F7D451;}
	.st1450{fill:#ECCC3A;}
	.st1451{fill:#F6E059;}
	.st1452{fill:#D8C24F;}
	.st1453{fill:#A7A85F;}
	.st1454{fill:#9B9235;}
	.st1455{fill:#E4C345;}
	.st1456{fill:#656A2E;}
	.st1457{fill:#53642F;}
	.st1458{fill:#CEBB4C;}
	.st1459{fill:#DAC862;}
	.st1460{fill:#C1B54F;}
	.st1461{fill:#CABE59;}
	.st1462{fill:#D0BD4C;}
	.st1463{fill:#D5C75A;}
	.st1464{fill:#B2AA45;}
	.st1465{fill:#59692E;}
	.st1466{fill:#BBAF44;}
	.st1467{fill:#26492A;}
	.st1468{fill:#354A22;}
	.st1469{fill:#2D4925;}
	.st1470{fill:#28482D;}
	.st1471{fill:#254927;}
	.st1472{fill:#3A4A22;}
	.st1473{fill:#274928;}
	.st1474{fill:#405024;}
	.st1475{fill:#415325;}
	.st1476{fill:#27492A;}
	.st1477{fill:#344C29;}
	.st1478{fill:#28472C;}
	.st1479{fill:#3F4F24;}
	.st1480{fill:#2D482D;}
	.st1481{fill:#5C6631;}
	.st1482{fill:#E8CF64;}
	.st1483{fill:#445627;}
	.st1484{fill:#D7DA8C;}
	.st1485{fill:#C7BD58;}
	.st1486{fill:#5E672E;}
	.st1487{fill:#6A7738;}
	.st1488{fill:#EFC43C;}
	.st1489{fill:#274A28;}
	.st1490{fill:#354B23;}
	.st1491{fill:#294924;}
	.st1492{fill:#3A4A24;}
	.st1493{fill:#2F4F27;}
	.st1494{fill:#E8C341;}
	.st1495{fill:#D0B54B;}
	.st1496{fill:#D2BB4B;}
	.st1497{fill:#CFB94C;}
	.st1498{fill:#E5CD46;}
	.st1499{fill:#C5B746;}
	.st1500{fill:#CCBA49;}
	.st1501{fill:#4A5827;}
	.st1502{fill:#EBCD40;}
	.st1503{fill:#F4E160;}
	.st1504{fill:#FBF394;}
	.st1505{fill:#EBCD42;}
	.st1506{fill:#E5D86D;}
	.st1507{fill:#FDF297;}
	.st1508{fill:#D4C060;}
	.st1509{fill:#FBF28E;}
	.st1510{fill:#EBC62F;}
	.st1511{fill:#F6D959;}
	.st1512{fill:#F0C838;}
	.st1513{fill:#F9E261;}
	.st1514{fill:#E7CD41;}
	.st1515{fill:#EECB39;}
	.st1516{fill:#ECC838;}
	.st1517{fill:#E7C425;}
	.st1518{fill:#FCEF89;}
	.st1519{fill:#FBED74;}
	.st1520{fill:#E9C531;}
	.st1521{fill:#F3D352;}
	.st1522{fill:#FCF296;}
	.st1523{fill:#9F9C46;}
	.st1524{fill:#F0C127;}
	.st1525{fill:#E5C641;}
	.st1526{fill:#ECD355;}
	.st1527{fill:#F3D050;}
	.st1528{fill:#2A452B;}
	.st1529{fill:#2A462E;}
	.st1530{fill:#FCF396;}
	.st1531{fill:#E4C543;}
	.st1532{fill:#DECB54;}
	.st1533{fill:#EAC951;}
	.st1534{fill:#2C4E31;}
	.st1535{fill:#3D5B42;}
	.st1536{fill:#315336;}
	.st1537{fill:#2C5132;}
	.st1538{fill:#67796A;}
	.st1539{fill:#415E43;}
	.st1540{fill:#495F50;}
	.st1541{fill:#9BB19D;}
	.st1542{fill:#2F4F36;}
	.st1543{fill:#325239;}
	.st1544{fill:#506654;}
	.st1545{fill:#2D4E32;}
	.st1546{fill:#AFC0B3;}
	.st1547{fill:#47664D;}
	.st1548{fill:#2F5132;}
	.st1549{fill:#25512B;}
	.st1550{fill:#254E2A;}
	.st1551{fill:#2A4F30;}
	.st1552{fill:#445E47;}
	.st1553{fill:#637964;}
	.st1554{fill:#B0BFB1;}
	.st1555{fill:#C1D1C1;}
	.st1556{fill:#CFDDCF;}
	.st1557{fill:#A9B4A6;}
	.st1558{fill:#B7C4B7;}
	.st1559{fill:#9FB3A2;}
	.st1560{fill:#EEF2EC;}
	.st1561{fill:#95AF99;}
	.st1562{fill:#E9EEE8;}
	.st1563{fill:#B5C3B3;}
	.st1564{fill:#ACBDAD;}
	.st1565{fill:#BACEBD;}
	.st1566{fill:#D4E1D7;}
	.st1567{fill:#99AB99;}
	.st1568{fill:#D5E2D3;}
	.st1569{fill:#ABB9AF;}
	.st1570{fill:#B5C3B4;}
	.st1571{fill:#D3DDD0;}
	.st1572{fill:#ACBEAD;}
	.st1573{fill:#C5D5CB;}
	.st1574{fill:#B1BFB2;}
	.st1575{fill:#B1C3B3;}
	.st1576{fill:#778B79;}
	.st1577{fill:#CDD9D1;}
	.st1578{fill:#BECBC0;}
	.st1579{fill:#BAC6B8;}
	.st1580{fill:#A9BCAB;}
	.st1581{fill:#728376;}
	.st1582{fill:#D4E0D5;}
	.st1583{fill:#CAD7CB;}
	.st1584{fill:#B1C1B6;}
	.st1585{fill:#BFCEC3;}
	.st1586{fill:#2B4E32;}
	.st1587{fill:#335037;}
	.st1588{fill:#3E5943;}
	.st1589{fill:#2E5235;}
	.st1590{fill:#647766;}
	.st1591{fill:#3F5E45;}
	.st1592{fill:#34553A;}
	.st1593{fill:#637966;}
	.st1594{fill:#38543C;}
	.st1595{fill:#EDF2EE;}
	.st1596{fill:#B4C2B3;}
	.st1597{fill:#24512C;}
	.st1598{fill:#305036;}
	.st1599{fill:#27502D;}
	.st1600{fill:#415F44;}
	.st1601{fill:#25522C;}
	.st1602{fill:#23522B;}
	.st1603{fill:#2B5030;}
	.st1604{fill:#305338;}
	.st1605{fill:#35543D;}
	.st1606{fill:#2B5132;}
	.st1607{fill:#37573C;}
	.st1608{fill:#345336;}
	.st1609{fill:#305335;}
	.st1610{fill:#D5DED4;}
	.st1611{fill:#B1C1B5;}
	.st1612{fill:#4B624F;}
	.st1613{fill:#455F4E;}
	.st1614{fill:#37553D;}
	.st1615{fill:#CBD7CB;}
	.st1616{fill:#D3DED5;}
	.st1617{fill:#335339;}
	.st1618{fill:#9BAF9F;}
	.st1619{fill:#626F2F;}
	.st1620{fill:#ECE78F;}
	.st1621{fill:#ECE16B;}
	.st1622{fill:#EAE683;}
	.st1623{fill:#F0F4EF;}
	.st1624{fill:#A5B9A9;}
	.st1625{fill:#2B4E30;}
	.st1626{fill:#345436;}
	.st1627{fill:#2F5336;}
	.st1628{fill:#4F6752;}
	.st1629{fill:#2A502E;}
	.st1630{fill:#BDC9BC;}
	.st1631{fill:#E4EAE4;}
	.st1632{fill:#AEC0AA;}
	.st1633{fill:#647E6B;}
	.st1634{fill:#EBF0EA;}
	.st1635{fill:#E8EEE8;}
	.st1636{fill:#32503A;}
	.st1637{fill:#455F4B;}
	.st1638{fill:#304F36;}
	.st1639{fill:#F3F7F4;}
	.st1640{fill:#5F7363;}
	.st1641{fill:#596A5D;}
	.st1642{fill:#697F6D;}
	.st1643{fill:#E9F0EA;}
	.st1644{fill:#E3EAE2;}
	.st1645{fill:#B9C7BA;}
	.st1646{fill:#C7D5CB;}
	.st1647{fill:#E2E9E1;}
	.st1648{fill:#F3F6F3;}
	.st1649{fill:#E9EEE7;}
	.st1650{fill:#CED9CC;}
	.st1651{fill:#ADBFB3;}
	.st1652{fill:#D8C251;}
	.st1653{fill:#D9BD50;}
	.st1654{fill:#64652B;}
	.st1655{fill:#A69E42;}
	.st1656{fill:#B9B55C;}
	.st1657{fill:#B1AB53;}
	.st1658{fill:#BBA435;}
	.st1659{fill:#E5D364;}
	.st1660{fill:#CEBC50;}
	.st1661{fill:#E6D264;}
	.st1662{fill:#C6B33C;}
	.st1663{fill:#E2DD7F;}
	.st1664{fill:#E4C546;}
	.st1665{fill:#E9CE53;}
	.st1666{fill:#D4D681;}
	.st1667{fill:#F6F196;}
	.st1668{fill:#F4E77C;}
	.st1669{fill:#F4E274;}
	.st1670{fill:#F0E683;}
	.st1671{fill:#ECEA98;}
	.st1672{fill:#F1D657;}
	.st1673{fill:#EAC53A;}
	.st1674{fill:#D9C043;}
	.st1675{fill:#ECD05D;}
	.st1676{fill:#ECD765;}
	.st1677{fill:#F2DE68;}
	.st1678{fill:#EBD150;}
	.st1679{fill:#EFCB51;}
	.st1680{fill:#F5DD55;}
	.st1681{fill:#FBF080;}
	.st1682{fill:#EEC727;}
	.st1683{fill:#F6D147;}
	.st1684{fill:#E3CB26;}
	.st1685{fill:#FBF395;}
	.st1686{fill:#F7D647;}
	.st1687{fill:#FAE978;}
	.st1688{fill:#FBE870;}
	.st1689{fill:#424A22;}
	.st1690{fill:#6E6F2D;}
	.st1691{fill:#E4C631;}
	.st1692{fill:#E4D06A;}
	.st1693{fill:#D7D172;}
	.st1694{fill:#D4C159;}
	.st1695{fill:#E1C74D;}
	.st1696{fill:#D7C65D;}
	.st1697{fill:#DFDB81;}
	.st1698{fill:#B8BD6F;}
	.st1699{fill:#E1DF83;}
	.st1700{fill:#284925;}
	.st1701{fill:#264B2A;}
	.st1702{fill:#284A2B;}
	.st1703{fill:#425729;}
	.st1704{fill:#C5BE5E;}
	.st1705{fill:#657032;}
	.st1706{fill:#384F25;}
	.st1707{fill:#C9B74C;}
	.st1708{fill:#224C29;}
	.st1709{fill:#D6C55A;}
	.st1710{fill:#686D30;}
	.st1711{fill:#D7CF71;}
	.st1712{fill:#354C27;}
	.st1713{fill:#57632B;}
	.st1714{fill:#E7D871;}
	.st1715{fill:#D7D983;}
	.st1716{fill:#D1D490;}
	.st1717{fill:#EDCC51;}
	.st1718{fill:#F1DC6A;}
	.st1719{fill:#D1CF63;}
	.st1720{fill:#DFCF63;}
	.st1721{fill:#D0D17E;}
	.st1722{fill:#F0E982;}
	.st1723{fill:#C5C870;}
	.st1724{fill:#27472E;}
	.st1725{fill:#3A4D24;}
	.st1726{fill:#405426;}
	.st1727{fill:#254D26;}
	.st1728{fill:#3C4F24;}
	.st1729{fill:#597147;}
	.st1730{fill:#294D28;}
	.st1731{fill:#294A2E;}
	.st1732{fill:#7B8136;}
	.st1733{fill:#3F5326;}
	.st1734{fill:#FBF187;}
	.st1735{fill:#FBF28F;}
	.st1736{fill:#E9C833;}
	.st1737{fill:#FDF393;}
	.st1738{fill:#F9E464;}
	.st1739{fill:#FCF18B;}
	.st1740{fill:#F7DD57;}
	.st1741{fill:#FBEB7B;}
	.st1742{fill:#E2E9E0;}
	.st1743{fill:#CED9CE;}
	.st1744{fill:#345538;}
	.st1745{fill:#A5B6A7;}
	.st1746{fill:#687E68;}
	.st1747{fill:#DCE3D9;}
	.st1748{fill:#23512B;}
	.st1749{fill:#3E5845;}
	.st1750{fill:#314F35;}
	.st1751{fill:#2E4F33;}
	.st1752{fill:#738A78;}
	.st1753{fill:#C2CFC2;}
	.st1754{fill:#E3EBE3;}
	.st1755{fill:#B1C0B5;}
	.st1756{fill:#E5EFE6;}
	.st1757{fill:#96A899;}
	.st1758{fill:#B2C2B4;}
	.st1759{fill:#BBCABD;}
	.st1760{fill:#C6D0C5;}
	.st1761{fill:#25522A;}
	.st1762{fill:#37563B;}
	.st1763{fill:#587763;}
	.st1764{fill:#E1E8E2;}
	.st1765{fill:#EAF0E9;}
	.st1766{fill:#99A999;}
	.st1767{fill:#FCFDFB;}
	.st1768{fill:#C7D4C9;}
	.st1769{fill:#2C4F30;}
	.st1770{fill:#CED9CB;}
	.st1771{fill:#E5EBE2;}
	.st1772{fill:#B5C1B6;}
	.st1773{fill:#DBE4D9;}
	.st1774{fill:#4D6350;}
	.st1775{fill:#E5EBE3;}
	.st1776{fill:#CAD6C9;}
	.st1777{fill:#A7B8A7;}
	.st1778{fill:#A3B7AA;}
	.st1779{fill:#9EB8A4;}
	.st1780{fill:#AEBEAF;}
	.st1781{fill:#D9E4DD;}
	.st1782{fill:#9CAC9D;}
	.st1783{fill:#C5D0C7;}
	.st1784{fill:#A8B7AA;}
	.st1785{fill:#A4B3A5;}
	.st1786{fill:#A7B9AC;}
	.st1787{fill:#CEDBD0;}
	.st1788{fill:#ECF1EA;}
	.st1789{fill:#B9C6B6;}
	.st1790{fill:#D8E0D8;}
	.st1791{fill:#BFCDC1;}
	.st1792{fill:#BAC5C1;}
	.st1793{fill:#E7EDE7;}
	.st1794{fill:#D5DECE;}
	.st1795{fill:#A0B5A4;}
	.st1796{fill:#59715C;}
	.st1797{fill:#2D5032;}
	.st1798{fill:#47654B;}
	.st1799{fill:#435B46;}
	.st1800{fill:#DFE9DE;}
	.st1801{fill:#E4EFE6;}
	.st1802{fill:#B9C9BB;}
	.st1803{fill:#647E6A;}
	.st1804{fill:#2A5131;}
	.st1805{fill:#325236;}
	.st1806{fill:#70846F;}
	.st1807{fill:#506A54;}
	.st1808{fill:#BED0BF;}
	.st1809{fill:#C4D1C3;}
	.st1810{fill:#254E2C;}
	.st1811{fill:#315234;}
	.st1812{fill:#2A4E30;}
	.st1813{fill:#E6ECE4;}
	.st1814{fill:#5B6F5B;}
	.st1815{fill:#C1D0C2;}
	.st1816{fill:#93AE9C;}
	.st1817{fill:#BCCDBE;}
	.st1818{fill:#BBC9BB;}
	.st1819{fill:#C5D4C6;}
	.st1820{fill:#B6C4B9;}
	.st1821{fill:#B6C7BA;}
	.st1822{fill:#D8E1D6;}
	.st1823{fill:#D2DDCF;}
	.st1824{fill:#BECBC2;}
	.st1825{fill:#E8EEE7;}
	.st1826{fill:#C4CFC0;}
	.st1827{fill:#B0C3B5;}
	.st1828{fill:#BCC8BB;}
	.st1829{fill:#ACB7AB;}
	.st1830{fill:#EFF3EE;}
	.st1831{fill:#C6D5CC;}
	.st1832{fill:#AEBCAF;}
	.st1833{fill:#ABC2AB;}
	.st1834{fill:#E5ECE5;}
	.st1835{fill:#CFDCD0;}
	.st1836{fill:#3A563E;}
	.st1837{fill:#5E7A64;}
	.st1838{fill:#D8CB64;}
	.st1839{fill:#DBD074;}
	.st1840{fill:#EFEA8F;}
	.st1841{fill:#EBEB9C;}
	.st1842{fill:#F0E071;}
	.st1843{fill:#F2E27A;}
	.st1844{fill:#E6E182;}
	.st1845{fill:#EACE59;}
	.st1846{fill:#E8DA6B;}
	.st1847{fill:#F1E37A;}
	.st1848{fill:#FAF293;}
	.st1849{fill:#FDF08B;}
	.st1850{fill:#F8E96D;}
	.st1851{fill:#FDF190;}
	.st1852{fill:#FCF091;}
	.st1853{fill:#344623;}
	.st1854{fill:#274826;}
	.st1855{fill:#2E432F;}
	.st1856{fill:#F0EF9A;}
	.st1857{fill:#DADE98;}
	.st1858{fill:#F1EE97;}
	.st1859{fill:#ECEB90;}
	.st1860{fill:#284630;}
	.st1861{fill:#334F28;}
	.st1862{fill:#4C5D2C;}
	.st1863{fill:#465B2A;}
	.st1864{fill:#868D3B;}
	.st1865{fill:#2B472F;}
	.st1866{fill:#80843A;}
	.st1867{fill:#476032;}
	.st1868{fill:#435428;}
	.st1869{fill:#284A28;}
	.st1870{fill:#3C5227;}
	.st1871{fill:#3A5025;}
	.st1872{fill:#3B4F26;}
	.st1873{fill:#7B8A53;}
	.st1874{fill:#324C26;}
	.st1875{fill:#E3DD7F;}
	.st1876{fill:#DED46E;}
	.st1877{fill:#E3DC7A;}
	.st1878{fill:#BAB655;}
	.st1879{fill:#E0DA77;}
	.st1880{fill:#29472B;}
	.st1881{fill:#E6D463;}
	.st1882{fill:#F2CF49;}
	.st1883{fill:#E9E07C;}
	.st1884{fill:#E8E387;}
	.st1885{fill:#E0DE8E;}
	.st1886{fill:#EDE98A;}
	.st1887{fill:#2D4D28;}
	.st1888{fill:#27492F;}
	.st1889{fill:#284832;}
	.st1890{fill:#596830;}
	.st1891{fill:#C6B74D;}
	.st1892{fill:#85914E;}
	.st1893{fill:#B7B761;}
	.st1894{fill:#354E25;}
	.st1895{fill:#DBD876;}
	.st1896{fill:#54622D;}
	.st1897{fill:#697438;}
	.st1898{fill:#6A7A38;}
	.st1899{fill:#D2D587;}
	.st1900{fill:#CBD68F;}
	.st1901{fill:#A9B36E;}
	.st1902{fill:#6A8561;}
	.st1903{fill:#FDF38D;}
	.st1904{fill:#FCEE81;}
	.st1905{fill:#FCF28F;}
	.st1906{fill:#A7B9A9;}
	.st1907{fill:#647866;}
	.st1908{fill:#5F7A64;}
	.st1909{fill:#576F58;}
	.st1910{fill:#23522A;}
	.st1911{fill:#28472A;}
	.st1912{fill:#264636;}
	.st1913{fill:#E2DE86;}
	.st1914{fill:#DBD875;}
	.st1915{fill:#768F58;}
	.st1916{fill:#D1D58A;}
	.st1917{fill:#EAE384;}
	.st1918{fill:#D7D577;}
	.st1919{fill:#BAC279;}
	.st1920{fill:#D3D47D;}
	.st1921{fill:#A8B979;}
	.st1922{fill:#DBDC87;}
	.st1923{fill:#BFB24E;}
	.st1924{fill:#E8E589;}
	.st1925{fill:#D0C666;}
	.st1926{fill:#D1C961;}
	.st1927{fill:#C2BC5E;}
	.st1928{fill:#D3C156;}
	.st1929{fill:#F0EA8E;}
	.st1930{fill:#E4CE5D;}
	.st1931{fill:#F1CF57;}
	.st1932{fill:#DDC551;}
	.st1933{fill:#ECEA96;}
	.st1934{fill:#ACBB71;}
	.st1935{fill:#616F33;}
	.st1936{fill:#D7DC8F;}
	.st1937{fill:#D0C965;}
	.st1938{fill:#D6D37A;}
	.st1939{fill:#848339;}
	.st1940{fill:#ADB776;}
	.st1941{fill:#C2BF60;}
	.st1942{fill:#F0E988;}
	.st1943{fill:#F5E982;}
	.st1944{fill:#AFA244;}
	.st1945{fill:#F6F194;}
	.st1946{fill:#EEEB95;}
	.st1947{fill:#D4D98E;}
	.st1948{fill:#D1C879;}
	.st1949{fill:#C8D475;}
	.st1950{fill:#CFCA6F;}
	.st1951{fill:#F4E87C;}
	.st1952{fill:#0B73B2;}
	.st1953{fill:#F5893C;}
	.st1954{fill:#7B8574;}
	.st1955{fill:#FFF4ED;}
	.st1956{fill:#B4D7A1;}
	.st1957{fill:#FFEFCF;}
	.st1958{fill:#E5C473;}
	.st1959{fill:#204496;}
	.st1960{fill:#101720;}
	.st1961{fill:#9E4E26;}
	.st1962{fill:#1F7F4F;}
	.st1963{fill:#F8CD6D;}
	.st1964{fill:#CEE3F1;}
	.st1965{fill:#2E4D4B;}
	.st1966{fill:#39368C;}
	.st1967{fill:#111B24;}
	.st1968{fill:#322F30;}
	.st1969{stroke:#000000;stroke-width:0.25;stroke-miterlimit:10;}
	.st1970{fill:#131312;stroke:#131312;stroke-width:0.25;stroke-miterlimit:10;}
	.st1971{fill:#739B3F;}
	.st1972{opacity:0.99;enable-background:new    ;}
	.st1973{fill:#4F9242;}
	.st1974{fill:#221F1F;}
	.st1975{opacity:0.99;fill:#221F1F;enable-background:new    ;}
	.st1976{stroke:#221F1F;stroke-width:0.25;}
	.st1977{fill:#EDA620;}
	.st1978{fill:#1A171B;}
	.st1979{fill:#E94F3D;}
	.st1980{fill:#883A8D;}
	.st1981{fill:#15999B;}
	.st1982{fill:#34B1E6;}
	.st1983{fill:#DEBA41;}
	.st1984{fill:#243771;}
	.st1985{fill:#231F20;}
	.st1986{fill:#F6921E;}
	.st1987{fill:none;stroke:#FFFFFF;stroke-width:0.672;}
	.st1988{fill:none;stroke:#731720;stroke-width:0.43;}
	.st1989{fill:#E08026;}
	.st1990{fill:#731720;}
	.st1991{fill:none;stroke:#FFFFFF;stroke-width:0.286;}
	.st1992{fill:#157FC3;}
	.st1993{fill:#808184;}
	.st1994{fill:#CA2026;}
	.st1995{fill:none;stroke:#1D1C1A;stroke-width:0.1605;stroke-miterlimit:10;}
	.st1996{fill:#00984A;}
	.st1997{fill:#D89928;}
	.st1998{fill-rule:evenodd;clip-rule:evenodd;fill:#AE841F;}
	.st1999{fill:#AE841F;}
	.st2000{fill-rule:evenodd;clip-rule:evenodd;fill:#0F206C;}
	.st2001{fill:#F4B223;}
	.st2002{fill:#7A2682;}
	.st2003{fill:#00A887;}
	.st2004{fill:#D50057;}
	.st2005{fill:#B07C2D;}
	.st2006{fill:#929497;}
	.st2007{fill:#3755A5;}
	.st2008{fill:url(#SVGID_00000138537263999885942670000002883253987757954955_);}
	.st2009{fill:url(#SVGID_00000085955153377250451870000005857465490892461489_);}
	.st2010{fill:url(#SVGID_00000018216892512238207450000002957356210617691781_);}
	.st2011{fill:url(#SVGID_00000099637746353481130260000004946440499680298425_);}
	.st2012{fill:url(#SVGID_00000076603694059387851990000006183676030192383116_);}
	.st2013{fill:#A3DDF8;}
	.st2014{fill:url(#SVGID_00000013908757229683829770000004715913938816962460_);}
	.st2015{fill:url(#SVGID_00000151526958556052426280000005260170648749945741_);}
	.st2016{fill:url(#SVGID_00000058588075898302309480000001787861604528219805_);}
	.st2017{fill:url(#SVGID_00000102524506105778003360000009969487217603075242_);}
	.st2018{fill:url(#SVGID_00000177457272790287088310000014286971494080521873_);}
	.st2019{fill:url(#SVGID_00000162346907244339552960000015552690088066940861_);}
	.st2020{fill:url(#SVGID_00000065755103823331772990000018128557709641009823_);}
	.st2021{fill:url(#SVGID_00000042728393573888813870000001879997696325246348_);}
	.st2022{fill:url(#SVGID_00000057856059760478005830000008201898979295783864_);}
	.st2023{fill:url(#SVGID_00000149345832245009719400000017725031940613567155_);}
	.st2024{fill:url(#SVGID_00000087409183869377718520000003438470664982316693_);}
	.st2025{fill:url(#SVGID_00000014616274100794708490000002505623671610730896_);}
	.st2026{fill:url(#SVGID_00000061452015196335831260000015500442634654266033_);}
	.st2027{fill:url(#SVGID_00000164505288292795405940000016511992166618984851_);}
	.st2028{fill:url(#SVGID_00000008856627595076268300000008232698348233070219_);}
	.st2029{fill:url(#SVGID_00000016762967416732072160000016465204051594273439_);}
	.st2030{fill:url(#SVGID_00000070102360360212930700000006276450726865417900_);}
	.st2031{fill:url(#SVGID_00000013908960057306264660000014946924017892852139_);}
	.st2032{fill:url(#SVGID_00000169531104151726493220000000460341816058901163_);}
	.st2033{fill:url(#SVGID_00000020395323750691132160000013414739929593306784_);}
	.st2034{fill:url(#SVGID_00000098899898992459052520000013428080118504726656_);}
	.st2035{fill:url(#SVGID_00000025416334712682730130000011327423228255616958_);}
	.st2036{fill:url(#SVGID_00000012458956007912626020000003270178980169801903_);}
	.st2037{fill:url(#SVGID_00000000207633028791840780000008136990782968944554_);}
	.st2038{fill:url(#SVGID_00000150093308243544583110000013412178303769809854_);}
	.st2039{fill:url(#SVGID_00000182505229484127309770000001897116436969889466_);}
	.st2040{fill:url(#SVGID_00000124125420515558772490000014146133373662179247_);}
	.st2041{fill:url(#SVGID_00000024691795418382391160000007820706865348511909_);}
	.st2042{fill:url(#SVGID_00000110433308101366476250000016987566337620454029_);}
	.st2043{fill:url(#SVGID_00000107563238205275040920000014084354008797805204_);}
	.st2044{fill:url(#SVGID_00000150101282633304686290000008799813122604086952_);}
	.st2045{fill:url(#SVGID_00000106868210191203583260000015442122508454176151_);}
	.st2046{fill:url(#SVGID_00000102522709469437963030000012034242554383374511_);}
	.st2047{fill:#94C4DC;}
	.st2048{fill:#313838;}
	.st2049{fill:#00AFC4;}
	.st2050{fill:#B7B9B3;}
	.st2051{fill:#2C5A37;}
	.st2052{fill:#425D59;}
	.st2053{fill:#313B4B;}
	.st2054{fill:#2E423A;}
	.st2055{fill:#464B2A;}
	.st2056{fill:#484239;}
	.st2057{fill:#474141;}
	.st2058{fill:#3A4139;}
	.st2059{fill:#4A464A;}
	.st2060{fill:url(#SVGID_00000058580365266478225510000006135231037087157637_);}
	.st2061{fill:#313A42;}
	.st2062{fill:url(#SVGID_00000034774324708990484860000010411060256023953064_);}
	.st2063{fill:url(#SVGID_00000181056701983241043240000009410325393926601651_);}
	.st2064{fill:#455745;}
	.st2065{fill:#221E1F;}
	.st2066{fill:#425351;}
	.st2067{fill:#549242;}
	.st2068{fill:#F27B21;}
	.st2069{fill:#219893;}
	.st2070{fill:#238A8D;}
	.st2071{fill:url(#SVGID_00000077297884066820796930000013401965853445096612_);}
	.st2072{fill:url(#SVGID_00000044862877390339428860000010536379997923821728_);}
	.st2073{fill:#0A73B8;}
	.st2074{fill:#5A595B;}
	.st2075{fill:#91742D;}
	.st2076{fill:#005C77;}
	.st2077{fill:#00B4A4;}
	.st2078{fill:#A4A7A8;}
	.st2079{fill:#1D441F;}
	.st2080{fill:#AA7C2C;}
	.st2081{fill:#1D431E;}
	.st2082{fill:#721011;}
	.st2083{fill:#090909;}
	.st2084{fill:#060707;}
	.st2085{fill:#0B0B0B;}
	.st2086{fill:#0F0F10;}
	.st2087{fill:#161616;}
	.st2088{fill:#EDA22A;}
	.st2089{fill:#046EB6;}
	.st2090{fill:#272E69;}
	.st2091{fill:#272E6A;}
	.st2092{fill:#262D68;}
	.st2093{fill:#272D69;}
	.st2094{fill:#262C67;}
	.st2095{fill:#28306D;}
	.st2096{fill:#282E6A;}
	.st2097{fill:#272F6A;}
	.st2098{fill:#282F6C;}
	.st2099{fill:#293372;}
	.st2100{fill:#283372;}
	.st2101{fill:#28306C;}
	.st2102{fill:#4196B4;}
	.st2103{fill:#3D8FB1;}
	.st2104{fill:#50AC6D;}
	.st2105{fill:#3C8BAE;}
	.st2106{fill:#4DA974;}
	.st2107{fill:#275F91;}
	.st2108{fill:#24588C;}
	.st2109{fill:#225389;}
	.st2110{fill:#2F6F9A;}
	.st2111{fill:#2A6795;}
	.st2112{fill:#3986AA;}
	.st2113{fill:#2C6A98;}
	.st2114{fill:#204D85;}
	.st2115{fill:#337AA2;}
	.st2116{fill:#30759E;}
	.st2117{fill:#50AD68;}
	.st2118{fill:#3A85A9;}
	.st2119{fill:#3881A7;}
	.st2120{fill:#357CA3;}
	.st2121{fill:#3377A0;}
	.st2122{fill:#3E90B1;}
	.st2123{fill:#4095B4;}
	.st2124{fill:#357DA5;}
	.st2125{fill:#30709B;}
	.st2126{fill:#4DA978;}
	.st2127{fill:#367FA5;}
	.st2128{fill:#3680A7;}
	.st2129{fill:#4AA389;}
	.st2130{fill:#4DA77D;}
	.st2131{fill:#55B257;}
	.st2132{fill:#4DA681;}
	.st2133{fill:#2F6B99;}
	.st2134{fill:#3982A9;}
	.st2135{fill:#4196B5;}
	.st2136{fill:#3E90B2;}
	.st2137{fill:#49A191;}
	.st2138{fill:#3C8EB0;}
	.st2139{fill:#3B89AD;}
	.st2140{fill:#3A86AA;}
	.st2141{fill:#1F4E84;}
	.st2142{fill:#215387;}
	.st2143{fill:#54B05D;}
	.st2144{fill:#4DA87A;}
	.st2145{fill:#4DAA74;}
	.st2146{fill:#25578B;}
	.st2147{fill:#469F98;}
	.st2148{fill:#2C6796;}
	.st2149{fill:#57B44E;}
	.st2150{fill:#459D9F;}
	.st2151{fill:#3279A1;}
	.st2152{fill:#275C8F;}
	.st2153{fill:#479BA4;}
	.st2154{fill:#3D92B1;}
	.st2155{fill:#286292;}
	.st2156{fill:#4199B1;}
	.st2157{fill:#439AAA;}
	.st2158{fill:#30749F;}
	.st2159{fill:#4197B3;}
	.st2160{fill:#48A28E;}
	.st2161{fill:#357CA4;}
	.st2162{fill:#32749D;}
	.st2163{fill:#50AC6E;}
	.st2164{fill:#489F96;}
	.st2165{fill:#54AF60;}
	.st2166{fill:#5AB54A;}
	.st2167{fill:#3883A8;}
	.st2168{fill:#469D9F;}
	.st2169{fill:#52AE66;}
	.st2170{fill:#1D4781;}
	.st2171{fill:#439AAD;}
	.st2172{fill:#3A87AC;}
	.st2173{fill:#4AA388;}
	.st2174{fill:#52AF63;}
	.st2175{fill:#459AA8;}
	.st2176{fill:#449BA6;}
	.st2177{fill:#3477A0;}
	.st2178{fill:#459EA1;}
	.st2179{fill:#3E8DAF;}
	.st2180{fill:#57B256;}
	.st2181{fill:#4AA387;}
	.st2182{fill:#3F92B2;}
	.st2183{fill:#49A290;}
	.st2184{fill:#53AF63;}
	.st2185{fill:#52AC6C;}
	.st2186{fill:#3479A1;}
	.st2187{fill:#4EA780;}
	.st2188{fill:#4DAA75;}
	.st2189{fill:#75B2C9;}
	.st2190{fill:#3D4D86;}
	.st2191{fill:#49588E;}
	.st2192{fill:#6787AD;}
	.st2193{fill:#7380A7;}
	.st2194{fill:#6F79A5;}
	.st2195{fill:#639CBA;}
	.st2196{fill:#4D5C90;}
	.st2197{fill:#62A2BD;}
	.st2198{fill:#608CAF;}
	.st2199{fill:#518EB1;}
	.st2200{fill:#609BB9;}
	.st2201{fill:#619CBA;}
	.st2202{fill:#5F99B7;}
	.st2203{fill:#4F8CAF;}
	.st2204{fill:#609AB8;}
	.st2205{fill:#689EBC;}
	.st2206{fill:#5077A2;}
	.st2207{fill:#527AA3;}
	.st2208{fill:#537BA4;}
	.st2209{fill:#537CA5;}
	.st2210{fill:#537FA6;}
	.st2211{fill:#5680A7;}
	.st2212{fill:#537AA3;}
	.st2213{fill:#609DBB;}
	.st2214{fill:#60AE99;}
	.st2215{fill:#469F99;}
	.st2216{fill:#7EC475;}
	.st2217{fill:#5FAD9D;}
	.st2218{fill:#50C7EB;}
	.st2219{fill:#54C2BA;}
	.st2220{fill:#447EC1;}
	.st2221{fill:#1A375D;}
	.st2222{fill:#64686C;}
	.st2223{fill:none;stroke:#000000;stroke-miterlimit:10;}
	.st2224{fill:#008356;}
	.st2225{fill:#8F9185;}
	.st2226{fill:#3F3A60;}
	.st2227{fill:#C89211;}
	.st2228{fill:url(#SVGID_00000080898382131485546010000007241017201616606354_);}
	.st2229{fill:url(#SVGID_00000045609467092948642650000016913861698926787515_);}
	.st2230{fill:url(#SVGID_00000009554775778503782440000002706195563116553891_);}
	.st2231{fill:#F48326;}
	.st2232{fill:#00AEA9;}
	.st2233{fill:#1C355E;}
	.st2234{fill:#3F738D;}
	.st2235{fill:#757B81;stroke:#757B81;stroke-miterlimit:10;}
	.st2236{fill:#757B81;}
	.st2237{fill:#BB2026;}
	.st2238{fill:#017F48;}
	.st2239{fill:#0F0D0C;}
	.st2240{fill:#342F2B;}
	.st2241{fill:#F6D864;}
	.st2242{fill:#FFF1D0;}
	.st2243{fill:#919FBA;}
	.st2244{fill:#92A0BA;}
	.st2245{fill:#A1ACC4;}
	.st2246{fill:#C1C8D7;}
	.st2247{fill:#AEB8CC;}
	.st2248{fill:#FDF5E5;}
	.st2249{fill:#FFF4DC;}
	.st2250{fill:#D7DBE6;}
	.st2251{fill:#EAEDF0;}
	.st2252{fill:#070707;}
	.st2253{fill:#F6F6F5;}
	.st2254{fill:url(#SVGID_00000137119496379804456920000007837278547586586032_);}
	.st2255{fill:url(#SVGID_00000075861040188971168880000007569656574666957450_);}
	.st2256{fill:url(#SVGID_00000181791860802840679370000002991805713234814383_);}
	.st2257{fill:url(#SVGID_00000045615162370356472160000013187971772346421381_);}
	.st2258{fill:#0E0F50;}
	.st2259{fill:url(#SVGID_00000114053854814612852000000002624235010614770826_);}
	.st2260{fill:url(#SVGID_00000083047015663809366580000017995998965593377409_);}
	.st2261{fill:url(#SVGID_00000118384987325275395060000002032137895475565993_);}
	.st2262{fill:url(#SVGID_00000168823516101563646680000014867734441638591378_);}
	.st2263{fill:url(#SVGID_00000098924966805961780270000010805642946765481911_);}
	.st2264{fill:#006A54;}
	.st2265{fill:#4DA5A4;}
	.st2266{fill:#9ACF8C;}
	.st2267{fill:#225D80;}
	.st2268{fill:#7CC9AD;}
	.st2269{fill:#1BAE59;}
	.st2270{fill-rule:evenodd;clip-rule:evenodd;fill:#1BAE59;}
	.st2271{fill:#2080C1;}
	.st2272{fill:#838486;}
	.st2273{fill:#3382C2;}
	.st2274{fill:#035F32;stroke:#035F32;stroke-width:0.25;stroke-miterlimit:10;}
	.st2275{fill:#2B2655;}
	.st2276{fill:#671F48;}
	.st2277{fill:#B22A26;}
	.st2278{fill:#1272B9;}
	.st2279{fill:#B9BABB;}
	.st2280{fill:#E8632A;}
	.st2281{fill:#CE202F;}
	.st2282{fill:#878A8F;}
	.st2283{fill:#7FAA40;}
	.st2284{fill:#EB2528;}
	.st2285{fill:#077047;}
	.st2286{fill:#9E782C;}
	.st2287{fill:#B4BED1;}
	.st2288{fill:#2E3D62;}
	.st2289{fill:#6F7B9A;}
	.st2290{fill:#4E5A79;}
	.st2291{fill:#D5DAE8;}
	.st2292{fill:#1B1C1E;}
	.st2293{fill:#232C38;}
	.st2294{fill:#A0A6C1;}
	.st2295{fill:#8B92B0;}
	.st2296{fill:#F5F9F7;}
	.st2297{fill:#2A6094;}
	.st2298{fill:#B67445;}
	.st2299{fill:#F9F291;}
	.st2300{fill:#F0D654;}
	.st2301{fill:#F3E804;}
	.st2302{fill:#344184;}
	.st2303{fill:url(#SVGID_00000137100011049453499620000015557776152422403460_);}
	.st2304{fill-rule:evenodd;clip-rule:evenodd;fill:#FFF100;}
	.st2305{fill-rule:evenodd;clip-rule:evenodd;fill:#00ADEE;}
	.st2306{fill-rule:evenodd;clip-rule:evenodd;fill:#C7A42F;}
	.st2307{fill-rule:evenodd;clip-rule:evenodd;fill:#231F20;}
	.st2308{fill-rule:evenodd;clip-rule:evenodd;fill:#C0A02F;}
	.st2309{fill-rule:evenodd;clip-rule:evenodd;fill:#C3A434;}
	
		.st2310{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;stroke:#060404;stroke-width:0.8073;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:2.6131;}
	.st2311{fill-rule:evenodd;clip-rule:evenodd;fill:#9D005D;}
	.st2312{fill-rule:evenodd;clip-rule:evenodd;fill:#EB1D25;}
	.st2313{fill-rule:evenodd;clip-rule:evenodd;fill:#00A551;}
	.st2314{fill:#303F59;}
	.st2315{fill:#DAA754;}
	.st2316{fill:#DBA852;}
	.st2317{fill:#69747D;}
	.st2318{fill:#9D9BA1;}
	.st2319{fill:#6DBF59;}
	.st2320{fill:#19B7B7;}
	.st2321{fill:#00A557;}
	.st2322{fill:#2D87C6;}
	.st2323{fill:#017F9A;}
	.st2324{fill:#326A8C;}
	.st2325{fill:#00728E;}
	.st2326{fill:#A29060;}
	.st2327{fill:#EC2025;}
	.st2328{fill:#1D1C1A;}
	.st2329{fill:#016A37;}
	.st2330{fill:#00A7AA;}
	.st2331{fill:#005298;}
	.st2332{fill:#808082;}
	
		.st2333{clip-path:url(#SVGID_00000109745347864547943930000005967222294446077345_);fill:url(#SVGID_00000000916307241524438760000004828880243956686271_);}
	
		.st2334{clip-path:url(#SVGID_00000025403318686551834740000004360024743257780406_);fill:url(#SVGID_00000054246820512435302980000001918902886867550910_);}
	
		.st2335{clip-path:url(#SVGID_00000087394175810207416720000001516791535805519497_);fill:url(#SVGID_00000154416212983888333960000006276993686770258055_);}
	
		.st2336{clip-path:url(#SVGID_00000066485443877194448380000014361512955193359798_);fill:url(#SVGID_00000165954872080782226480000018132654260097033393_);}
	
		.st2337{clip-path:url(#SVGID_00000021107668545030135580000003398410676930660020_);fill:url(#SVGID_00000021094426321854075790000014982538011071771298_);}
	
		.st2338{clip-path:url(#SVGID_00000093868142319617875480000001534359112589712014_);fill:url(#SVGID_00000137115543603731287640000010361469075069953667_);}
	
		.st2339{clip-path:url(#SVGID_00000065770267030255774010000007744331964231364535_);fill:url(#SVGID_00000063632169367732216760000002727539871094439590_);}
	
		.st2340{clip-path:url(#SVGID_00000111193325345894300720000001945957081781232776_);fill:url(#SVGID_00000012462237648899938330000011152305134178647191_);}
	
		.st2341{clip-path:url(#SVGID_00000173162265841917396190000002517882072247614084_);fill:url(#SVGID_00000166636299035490271900000009293055345302302892_);}
	
		.st2342{clip-path:url(#SVGID_00000000935283994847469070000013684646901847071110_);fill:url(#SVGID_00000026121232951153983870000002143540409052043406_);}
	
		.st2343{clip-path:url(#SVGID_00000158015893272872887800000011712925294398435205_);fill:url(#SVGID_00000008108665027501569910000006595256916821615278_);}
	
		.st2344{clip-path:url(#SVGID_00000136412672369775545230000012494688519264232877_);fill:url(#SVGID_00000065057937399095152470000012427633022415507634_);}
	
		.st2345{clip-path:url(#SVGID_00000064343973638566426510000002088288908050683295_);fill:url(#SVGID_00000151517759927518559540000012352516213621863612_);}
	
		.st2346{clip-path:url(#SVGID_00000183212034581148009920000013488217965136785563_);fill:url(#SVGID_00000151533160693927040330000003871148857418023840_);}
	
		.st2347{clip-path:url(#SVGID_00000075159192984550543850000001309914621012057243_);fill:url(#SVGID_00000079453392456378579400000015097161749023052458_);}
	
		.st2348{clip-path:url(#SVGID_00000065036007988316702670000006284780133680047025_);fill:url(#SVGID_00000101802119078214270430000004621124470459217571_);}
	
		.st2349{clip-path:url(#SVGID_00000030472327115787091510000010653854144795366572_);fill:url(#SVGID_00000071561774577709414350000004862962813411932827_);}
	
		.st2350{clip-path:url(#SVGID_00000005230096536998908510000017232689339116407945_);fill:url(#SVGID_00000045584760608978527870000011222263815072761512_);}
	
		.st2351{clip-path:url(#SVGID_00000091706560588879433600000017638854044738776713_);fill:url(#SVGID_00000060711134105169406270000018184261210197168292_);}
	
		.st2352{clip-path:url(#SVGID_00000131368836408729615650000002884502065389892526_);fill:url(#SVGID_00000132081603917861031230000007496850734155657356_);}
	
		.st2353{clip-path:url(#SVGID_00000012467963026808081160000006846354813955988406_);fill:url(#SVGID_00000161610078923711243870000015276080591652773791_);}
	
		.st2354{clip-path:url(#SVGID_00000008840958781729298370000010677314893228456607_);fill:url(#SVGID_00000045610854501199216750000003984082420363330967_);}
	
		.st2355{clip-path:url(#SVGID_00000090277727269740500330000003221628749428528780_);fill:url(#SVGID_00000116197496538108075530000015084593114093670801_);}
	
		.st2356{clip-path:url(#SVGID_00000130636402440905008550000004937161410175643061_);fill:url(#SVGID_00000016783359881016815930000010009959891257277847_);}
	
		.st2357{clip-path:url(#SVGID_00000079453232047313514920000000042022651767790765_);fill:url(#SVGID_00000129166286908927540850000013379817797136000931_);}
	
		.st2358{clip-path:url(#SVGID_00000124159063518274518510000002218490396488504204_);fill:url(#SVGID_00000079460224769438395480000001079618555661961661_);}
	
		.st2359{clip-path:url(#SVGID_00000140720210784829470650000011002963120536347014_);fill:url(#SVGID_00000042732700926365969220000001987210887775535540_);}
	
		.st2360{clip-path:url(#SVGID_00000041983267499953297310000016319619791767504530_);fill:url(#SVGID_00000098219056898912389630000009301479575648422301_);}
	
		.st2361{clip-path:url(#SVGID_00000132800229679371547540000017338247292972523670_);fill:url(#SVGID_00000103244089452352581910000013647916916059063739_);}
	
		.st2362{clip-path:url(#SVGID_00000049220350264702852790000004697042780400838796_);fill:url(#SVGID_00000060715968219315631320000011057462124653240508_);}
	
		.st2363{clip-path:url(#SVGID_00000126288593033435776400000013442494164183102882_);fill:url(#SVGID_00000071534302510503852000000007989395446349389445_);}
	
		.st2364{clip-path:url(#SVGID_00000175302928223279208540000013796729074552549257_);fill:url(#SVGID_00000067204712390068649660000014457442185869063341_);}
	
		.st2365{clip-path:url(#SVGID_00000084494052278809755820000001282279842865003679_);fill:url(#SVGID_00000082334892756956873460000009597197984300395178_);}
	
		.st2366{clip-path:url(#SVGID_00000134224861249339600100000007166966515461706115_);fill:url(#SVGID_00000103973992256769035570000006864677486683132057_);}
	
		.st2367{clip-path:url(#SVGID_00000165934098657611917390000000090808509857970066_);fill:url(#SVGID_00000132066252926822528550000012690220943800214448_);}
	
		.st2368{clip-path:url(#SVGID_00000183210033976956834200000008487770505047091367_);fill:url(#SVGID_00000030465442537591127950000010812024099307383937_);}
	
		.st2369{clip-path:url(#SVGID_00000011744658666459278420000013487120736394274199_);fill:url(#SVGID_00000084489881255942100580000015547728618214232767_);}
	
		.st2370{clip-path:url(#SVGID_00000155839411818556680570000007756724892028958652_);fill:url(#SVGID_00000104702370818953960990000014191622733622884740_);}
	
		.st2371{clip-path:url(#SVGID_00000059308374306776972180000009331570796290033546_);fill:url(#SVGID_00000168074860365656715300000017776718220123816102_);}
	
		.st2372{clip-path:url(#SVGID_00000163042224383815274600000006435713230193577616_);fill:url(#SVGID_00000136400143697582554100000000897018569114396337_);}
	
		.st2373{clip-path:url(#SVGID_00000147198470744687820720000018363784708661981350_);fill:url(#SVGID_00000134950638916887721490000013174103764093332906_);}
	
		.st2374{clip-path:url(#SVGID_00000153693639388113511810000016364163054237484173_);fill:url(#SVGID_00000075153768758986961840000011057052603192330647_);}
	
		.st2375{clip-path:url(#SVGID_00000120546554734941241620000003338495616516994438_);fill:url(#SVGID_00000154425773659497875110000008766117571723426199_);}
	
		.st2376{clip-path:url(#SVGID_00000052067803307566403060000001381340878583800207_);fill:url(#SVGID_00000124141810516356412630000002930050325780470679_);}
	
		.st2377{clip-path:url(#SVGID_00000019646164229082014470000013049662406967770267_);fill:url(#SVGID_00000178887127761991030750000013694606297083074192_);}
	
		.st2378{clip-path:url(#SVGID_00000068646731076000920680000018396791557480976295_);fill:url(#SVGID_00000129921811697565617590000011659928093281543301_);}
	
		.st2379{clip-path:url(#SVGID_00000005243391325971791040000010893465319812759219_);fill:url(#SVGID_00000062885906330404734100000003401714352808736433_);}
	
		.st2380{clip-path:url(#SVGID_00000085960670536478459780000002887812321402600888_);fill:url(#SVGID_00000109019161232346491780000015288087104413930423_);}
	
		.st2381{clip-path:url(#SVGID_00000000222528382991352870000014499427066870994359_);fill:url(#SVGID_00000130644253424513703750000013574155381658938266_);}
	
		.st2382{clip-path:url(#SVGID_00000158739399647671594940000013618399236385325222_);fill:url(#SVGID_00000083052482553447594840000014948769563294217906_);}
	
		.st2383{clip-path:url(#SVGID_00000016062168667814047280000008518797489024014723_);fill:url(#SVGID_00000142138389796504684510000011774955682601993351_);}
	
		.st2384{clip-path:url(#SVGID_00000090995496730033507690000017812761905673472154_);fill:url(#SVGID_00000134946391370198417660000017033113117158654655_);}
	
		.st2385{clip-path:url(#SVGID_00000111898389444410497770000011543613243518695093_);fill:url(#SVGID_00000177484412840873105980000011688850702460288189_);}
	
		.st2386{clip-path:url(#SVGID_00000170274651843269368040000016968807707153891499_);fill:url(#SVGID_00000054969582048633812910000012466154617897109171_);}
	
		.st2387{clip-path:url(#SVGID_00000152974175112058584690000000166837103230861489_);fill:url(#SVGID_00000125586890671104385210000017710199035841192619_);}
	
		.st2388{clip-path:url(#SVGID_00000170236789400799589640000000061316637131666060_);fill:url(#SVGID_00000088124534304049540940000002208725843825496507_);}
	
		.st2389{clip-path:url(#SVGID_00000059274637342046889610000009169118858281510313_);fill:url(#SVGID_00000039834743841425702040000000614563348575626885_);}
	
		.st2390{clip-path:url(#SVGID_00000084499241281005920120000004964902880440827521_);fill:url(#SVGID_00000149364704296267944580000000333935817282855311_);}
	
		.st2391{clip-path:url(#SVGID_00000043438526619258723490000016531124602000103566_);fill:url(#SVGID_00000026859938627283236770000002520223994877034939_);}
	
		.st2392{clip-path:url(#SVGID_00000107584878599640647330000013961291641898277033_);fill:url(#SVGID_00000127002459565442416530000007825917953653980313_);}
	
		.st2393{clip-path:url(#SVGID_00000009576300836541514040000001419233648801847709_);fill:url(#SVGID_00000181088517831707687610000001187240756448556432_);}
	
		.st2394{clip-path:url(#SVGID_00000100379466693212154340000005434029670637103280_);fill:url(#SVGID_00000106848260646125795880000004079547236335207568_);}
	
		.st2395{clip-path:url(#SVGID_00000091717705062663996190000005082046815962254513_);fill:url(#SVGID_00000057137464056145681160000014722651160385364106_);}
	
		.st2396{clip-path:url(#SVGID_00000110466200727337370440000011120118126936673720_);fill:url(#SVGID_00000131358733025220214020000017765168819109749945_);}
	.st2397{fill:#4FB74E;}
	.st2398{fill:#437EC0;}
	.st2399{fill:#FCB833;}
	.st2400{fill:#ED302F;}
	.st2401{fill:#3C84C5;}
	.st2402{fill:#008041;}
	.st2403{fill:#306782;}
	.st2404{fill:#54B0D9;}
	.st2405{fill:#2D6780;}
	.st2406{fill:#8DC341;}
	.st2407{fill:#005B2F;}
	.st2408{fill:#0C9247;}
	.st2409{fill:#CCBF44;}
	.st2410{fill:#BB9B46;}
	.st2411{fill:#437A9B;}
	.st2412{fill:#8AB9D5;}
	.st2413{fill:#659ABE;}
	.st2414{fill:#7F8082;}
	.st2415{fill:#C1A02E;}
	.st2416{fill:#4E5A30;}
	.st2417{fill:#716F6F;}
	.st2418{fill:#6A6157;}
	.st2419{fill:#B5BD34;}
	.st2420{fill:#73A84E;}
	.st2421{clip-path:url(#SVGID_00000085950183159937780950000012215027767613911722_);fill:#4C7637;}
	.st2422{fill:#08BAED;}
	.st2423{fill:#848386;}
	.st2424{fill:#F8A029;}
	.st2425{fill:#0093CE;}
	.st2426{fill:#F8A022;}
	.st2427{fill:#FCC43B;}
	.st2428{fill:#0094D1;}
	.st2429{fill:#077C79;}
	.st2430{fill:#AD936D;}
	.st2431{fill:#383838;}
	.st2432{fill:#72C7F0;}
	.st2433{fill:#FFCC33;}
	.st2434{fill:#973794;}
	.st2435{fill:#5C201B;}
	.st2436{fill:#6D6E71;}
	.st2437{fill:#036333;}
	.st2438{fill:#9A9D9F;}
	.st2439{fill:#6CB955;}
	.st2440{fill:#1AB6B8;}
	.st2441{fill:#194172;}
	.st2442{fill:#2A85C4;}
	.st2443{fill:#BED347;}
	.st2444{fill:#005A51;}
	.st2445{fill:#3EB562;}
	.st2446{fill:url(#SVGID_00000047022756265348393020000013101235949675031206_);}
	.st2447{fill:#030406;}
	.st2448{fill:url(#SVGID_00000006674461531335577250000010894657360488639382_);}
	.st2449{fill:url(#SVGID_00000093883891318188582800000018390229349032005552_);}
	.st2450{fill:url(#SVGID_00000098932125859665421420000000869716368083263673_);}
	.st2451{fill:#A6934D;}
	.st2452{fill:#535352;}
	.st2453{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
	.st2454{fill-rule:evenodd;clip-rule:evenodd;fill:#329866;}
	.st2455{fill-rule:evenodd;clip-rule:evenodd;fill:#CFDA82;}
	.st2456{fill-rule:evenodd;clip-rule:evenodd;fill:#8C5E25;}
	.st2457{fill-rule:evenodd;clip-rule:evenodd;}
	
		.st2458{clip-path:url(#SVGID_00000052072312726823885160000004098719833723988404_);fill:url(#SVGID_00000153666390076837482930000004744021026731898512_);}
	.st2459{fill-rule:evenodd;clip-rule:evenodd;fill:#9C6D40;}
	.st2460{fill-rule:evenodd;clip-rule:evenodd;fill:#A0782B;}
	.st2461{fill-rule:evenodd;clip-rule:evenodd;fill:#FFCA11;}
	.st2462{fill-rule:evenodd;clip-rule:evenodd;fill:#221F1F;}
	.st2463{fill-rule:evenodd;clip-rule:evenodd;fill:#CBDD67;}
	.st2464{fill-rule:evenodd;clip-rule:evenodd;fill:#A77744;}
	.st2465{fill:#27306B;}
	.st2466{fill:url(#SVGID_00000095330674524996652070000005736099649412724666_);}
	.st2467{fill:url(#SVGID_00000161600115500490038080000011936207807670122119_);}
	.st2468{fill:url(#SVGID_00000169537828058416202720000000000070525770531472_);}
	.st2469{fill:url(#SVGID_00000165918104571993404230000014012241012062852503_);}
	.st2470{fill:url(#SVGID_00000039832031339695495540000004568718638995842486_);}
	.st2471{fill:url(#SVGID_00000178185031900685777520000015101440217409371554_);}
	.st2472{fill:url(#SVGID_00000144328138706435154100000002696211104589643395_);}
	.st2473{fill:url(#SVGID_00000150064618678068220210000002107238088775743628_);}
	.st2474{fill:url(#SVGID_00000103955235131325045550000016514304517165770415_);}
	.st2475{fill:url(#SVGID_00000008142790467671927370000009054465264322972322_);}
	.st2476{fill:url(#SVGID_00000159442329169922165440000012348240753443050408_);}
	.st2477{fill:url(#SVGID_00000124848621416187445700000005369029600105107586_);}
	.st2478{fill:url(#SVGID_00000141456350092022668580000009797608069792113844_);}
	.st2479{fill:url(#SVGID_00000150077890332156462670000004140353038353084586_);}
	.st2480{fill:url(#SVGID_00000073001178639592428410000007338561313384635794_);}
	.st2481{fill:url(#SVGID_00000147907014111244502020000005858496334296142482_);}
	.st2482{fill:url(#SVGID_00000021081560069776717910000008894203805896688304_);}
	.st2483{fill:url(#SVGID_00000144299052919792359880000004260851952152998026_);}
	.st2484{fill:url(#SVGID_00000130627713536809795680000010754408988208166045_);}
	.st2485{fill:url(#SVGID_00000112623622181755661170000003138420673468190115_);}
	.st2486{fill:url(#SVGID_00000121982410319539155840000015907436575417206414_);}
	.st2487{fill:url(#SVGID_00000165952989528353484460000011494225842876073655_);}
	.st2488{fill:url(#SVGID_00000180343263236583651680000009624359217131920563_);}
	.st2489{fill:url(#SVGID_00000093138898042740345670000000826570365854882983_);}
	.st2490{fill:url(#SVGID_00000042699254557771876770000002465750677817324188_);}
	.st2491{fill:url(#SVGID_00000142149562388132190430000009878809175178629045_);}
	.st2492{fill:url(#SVGID_00000114780162471904292040000017946505398015062927_);}
	.st2493{fill:url(#SVGID_00000013182594425573322330000015856853694311700652_);}
	.st2494{fill:url(#SVGID_00000009578407894701556480000001754166379233408408_);}
	.st2495{fill:url(#SVGID_00000127011459064718500150000010523775136692453286_);}
	.st2496{fill:url(#SVGID_00000177486637041816480080000010139440173204462014_);}
	.st2497{fill:#00A44F;}
	.st2498{clip-path:url(#SVGID_00000042710188129336462470000000394324026649754299_);}
	.st2499{fill:#60240E;}
	.st2500{fill:#234486;}
	.st2501{fill:#FFD542;}
	.st2502{fill:#50B648;}
	.st2503{fill:#3CB34A;}
	.st2504{fill:#EB2C31;}
	.st2505{fill:#FDE500;}
	.st2506{fill:#57565A;}
	.st2507{fill:#00ADBB;}
	.st2508{fill:#3A3A3A;}
	.st2509{fill:url(#SVGID_00000067238152621058998530000017934730336639710907_);}
	.st2510{fill:url(#SVGID_00000065792237370709983310000011749734361912391814_);}
	.st2511{fill:#59C5CF;}
	.st2512{fill:url(#SVGID_00000137115022309056890080000007681542223471406215_);}
	.st2513{fill:url(#SVGID_00000054247969529948478390000007054575476414051999_);}
	.st2514{fill:#292D70;}
	.st2515{fill:url(#SVGID_00000030473032117133377460000007528992875065025428_);}
	.st2516{fill:url(#SVGID_00000083802900824256377190000014258025290941323648_);}
	.st2517{fill:#F7F8F9;}
	.st2518{opacity:0.36;fill:url(#SVGID_00000077305292487510003950000000884276803997238957_);enable-background:new    ;}
	.st2519{fill:#1477AF;}
	.st2520{fill:#7DC197;}
	.st2521{fill:#A1D6E6;}
	.st2522{fill:#9BD4E5;}
	.st2523{fill:#96D2E4;}
	.st2524{fill:#73C7DC;}
	.st2525{fill:#8CCEE3;}
	.st2526{fill:#9DD4E5;}
	.st2527{fill:#92D1E3;}
	.st2528{fill:#98D3E4;}
	.st2529{fill:#93D1E4;}
	.st2530{fill:#1174AC;}
	.st2531{fill:#6F94A0;}
	.st2532{fill:#8DCFE3;}
	.st2533{fill:#6F939F;}
	.st2534{fill:#1477B1;}
	.st2535{fill:#D3D9DE;}
	.st2536{fill:#7BCADE;}
	.st2537{fill:#CBD3D8;}
	.st2538{fill:#C4CDD4;}
	.st2539{fill:#D9DEE1;}
	.st2540{fill:#D0D5DB;}
	.st2541{fill:#85CBE1;}
	.st2542{fill:#B9C4CC;}
	.st2543{fill:#1679B1;}
	.st2544{fill:#DBE0E4;}
	.st2545{fill:#B5C1C9;}
	.st2546{fill:#C0C9D0;}
	.st2547{fill:#8BCFE2;}
	.st2548{fill:#B0BDC6;}
	.st2549{fill:#94D1E4;}
	.st2550{fill:#A0D6E5;}
	.st2551{fill:#D6DCE1;}
	.st2552{fill:#B2BFC9;}
	.st2553{fill:#ABDAE9;}
	.st2554{fill:#7E9CA8;}
	.st2555{fill:#BBC6CF;}
	.st2556{fill:#CBD2D8;}
	.st2557{fill:#C3CBD3;}
	.st2558{fill:#CED5DB;}
	.st2559{fill:#A8B9C1;}
	.st2560{fill:#B7DEEB;}
	.st2561{fill:#CCD5D9;}
	.st2562{fill:#809CA9;}
	.st2563{fill:#7697A3;}
	.st2564{fill:#C0E1EE;}
	.st2565{fill:#6E92A1;}
	.st2566{fill:#C9E5EE;}
	.st2567{fill:#D3ECF3;}
	.st2568{fill:#638897;}
	.st2569{fill:#7E9CAA;}
	.st2570{fill:#A1B3BE;}
	.st2571{fill:#92A8B5;}
	.st2572{fill:#006AA9;}
	.st2573{fill:#A1DAE8;}
	.st2574{fill:#B4E2ED;}
	.st2575{fill:#78AFD0;}
	.st2576{fill:#BDCBD1;}
	.st2577{fill:#5D8493;}
	.st2578{fill:#AFC2CA;}
	.st2579{fill:#01AF8C;}
	.st2580{fill:#A0ACB3;}
	.st2581{fill:#201B51;}
	.st2582{fill:#00AF87;}
	.st2583{fill:#A2AAAD;}
	.st2584{fill:#6F6F6F;}
	.st2585{fill:#737374;}
	.st2586{fill:#F26E21;}
	.st2587{fill:#737373;}
	.st2588{clip-path:url(#XMLID_00000041996421124713013910000011232335721939838614_);}
	.st2589{clip-path:url(#XMLID_00000058552145926558039650000007353126769284913582_);}
	.st2590{clip-path:url(#XMLID_00000060031379697612188980000001826505333354108807_);}
	.st2591{clip-path:url(#XMLID_00000123417399319581707870000000853568023092280753_);}
	.st2592{clip-path:url(#XMLID_00000072962745098203893460000013671271929847355788_);}
	.st2593{clip-path:url(#XMLID_00000004531795433842916420000016998739524055817914_);}
	.st2594{clip-path:url(#XMLID_00000042718511923740951080000009079345262592975039_);}
	.st2595{clip-path:url(#XMLID_00000137816982973085496740000004774769485373686718_);}
	.st2596{clip-path:url(#XMLID_00000155841982804318291620000009354280370960164793_);}
	.st2597{clip-path:url(#XMLID_00000026850173006446639130000009126135725012616082_);}
	.st2598{clip-path:url(#XMLID_00000153690366356786388550000017457223764693255834_);}
	.st2599{clip-path:url(#XMLID_00000123437411615875020650000011665082325581382306_);}
	.st2600{clip-path:url(#XMLID_00000083774440867877905950000018166585949075943353_);}
	.st2601{clip-path:url(#XMLID_00000163044337884485965440000012720472675535553920_);}
	.st2602{fill:#00B2BF;}
	.st2603{fill:#3B6D89;}
</style>
<g
   id="g170"
   transform="matrix(1.4009527,0,0,1.4009527,-217.28776,-200.47633)"><path
     class="st28"
     d="m 208.4,777.6 c -11.7,3 -16.3,4.1 -21.3,4.1 -9.1,0 -16.5,-2.2 -21.4,-6.3 -0.8,-0.7 -1.5,-1.5 -2,-2.4 -1.8,-3.2 -1.1,-7.5 1.7,-10.5 0.3,-0.3 0.6,-0.6 0.7,-0.8 9.3,-2.8 20.5,-4.8 31.5,-5.6 h 0.3 l 2.2,-10.1 -0.6,0.1 c -7.5,0.9 -14.4,2.1 -20.3,3.5 -9.3,-1.7 -16.1,-9.3 -17.1,-18.6 3.6,-7 11.3,-11.8 18.6,-11.8 3.7,0 7.1,0.9 13.6,3.5 l 0.4,0.2 2.1,-3.8 -0.4,-0.2 c -2.4,-1.5 -4.2,-2.9 -6,-4.2 -2.3,-1.8 -4.6,-3.4 -8,-5.3 -13.1,0 -27.2,10.1 -27.2,25.2 0,7.4 4.5,15.2 11.3,19.5 -0.7,0.4 -2.8,1.6 -2.8,1.6 -1.4,0.9 -2.6,2.4 -3.8,4.4 -4.1,7.6 -3.6,16.5 1.2,22.8 2.9,3.8 6.9,6.4 11.5,7.6 4.3,1.1 9.2,1.7 14.5,1.7 h 0.1 0.1 c 4.1,-1.8 7.6,-3.4 11,-5 3.8,-1.7 7.5,-3.4 11.7,-5.3 l 0.4,-0.2 -1.5,-4 z m 223.9,-69.7 c -13.3,0 -21.9,19.5 -21.9,29.6 0,8.3 9.2,14.2 30.1,19.1 H 229.2 c -0.3,-24.2 -0.9,-37.2 -2,-60.8 l -0.5,-9.8 h -10.4 v 0.5 c 2.9,33 4.1,54.2 5,79.6 v 0.4 h 229.1 l 0.1,-0.4 c 0.4,-2.2 1.8,-16.5 1.8,-20.1 0.1,-23.9 -7.4,-38.1 -20,-38.1 m 12.3,40.7 c -12.7,-2.6 -18.9,-5.2 -27.1,-11.2 0.9,-10.7 7.3,-20.1 13.6,-20.1 7.9,0 13.5,12.5 13.5,30.4 z m -4.7,-55 -8,-7.6 -8.4,8.8 8.6,7.8 z m 341.8,7.3 -8,-7.6 -8.4,8.8 8.6,7.8 z m -99.3,0.2 -8,-7.8 -8.4,8.8 8.6,7.8 z m -18.2,-0.2 -8,-7.6 -8.4,8.8 8.6,7.8 z m 50.7,15.8 -9.7,5.5 0.2,0.4 c 6.7,13.1 10.8,23.8 13.3,34.3 -3.7,11.8 -11.6,21.2 -20.7,24.5 -4.1,1.8 -10.5,3 -17.1,3.2 h -0.4 l -0.2,4.2 22.6,3 c 13.9,-10.6 21.9,-24.4 21.9,-37.8 0,-8.4 -3,-19.7 -9.7,-36.8 z m 37.3,49.8 c -0.7,-33.2 -1.3,-48.4 -2.7,-80.1 V 686 H 739 v 0.5 c 2.4,30.8 3.3,48.8 4.7,73.8 l 0.3,6.2 z m 29,-49.8 -9.7,5.5 0.2,0.4 c 6.7,13.1 10.8,23.8 13.3,34.3 -3.7,11.8 -11.6,21.2 -20.7,24.5 -4.1,1.8 -10.4,3 -17.1,3.2 h -0.4 l -0.2,4.2 22.6,3 C 783,781.2 791,767.5 791,754 c 0,-8.4 -3,-19.7 -9.7,-36.8 z m 43.7,-1.7 c -12.6,0 -22,17.7 -22,29.2 0,9.8 10.7,16.3 32.8,19.9 -3.5,8 -9.8,14.5 -17,17.6 -3.9,1.8 -10,2.7 -18,2.9 h -0.4 l -0.4,4.2 22.6,3 c 14.4,-10.7 22.3,-25 22.3,-40.3 0.1,-22.1 -7.7,-36.5 -19.9,-36.5 m -15,29.1 c 1.2,-10.4 7.6,-19.6 13.8,-19.6 7.7,0 12.9,11.4 14.2,30.7 -14.2,-2.1 -22.8,-5.6 -28,-11.1 M 566.2,686.4 V 686 h -10.4 v 0.5 c 2.2,28.3 3.2,45.5 4.5,70.1 h -46.6 c -4.4,0 -7.7,-2.4 -10.5,-7.9 -4.1,-7.8 -6.8,-19.3 -8.4,-36.2 l -0.1,-0.6 -9,3.9 v 0.3 c 2.4,17 6.6,31 11.7,39.4 0.3,0.5 0.6,1 1,1.4 -5.2,0.9 -10.3,1.1 -16.2,1.1 -7.1,0 -13.2,-0.7 -20.1,-2.1 l -0.5,-0.1 -1.7,9.4 0.4,0.1 c 6.8,1.7 14.4,2.6 20.9,2.6 6.6,0 13.7,-0.2 20.1,-1.5 l 0.3,-0.1 1.1,-4.3 c 3.2,2.8 6.6,4.2 10,4.3 l 0.3,0.1 h 55.7 v -0.5 c -0.5,-33.4 -1.1,-47.6 -2.5,-79.5 m 123.9,69.1 c 0,-5.9 -9.8,-26.7 -17.9,-39.2 -2,1 -5.5,3.6 -7.6,5.3 -11.3,9.1 -19.2,21.2 -19.2,29.4 0,9.4 9.8,17 21.9,17 7.4,0 14.6,-1.9 19.8,-5.3 z m -22.1,2.9 c -6.4,0 -12.2,-2.8 -15.5,-7.1 3,-9 9.1,-17.3 16.7,-22.9 5.4,8.4 9,16.1 12.6,26.3 -4.3,2.8 -9.9,3.7 -13.8,3.7 m -72.8,8.1 c -0.7,-33.2 -1.3,-48.4 -2.7,-80.1 V 686 h -10.4 v 0.5 c 2.4,30.8 3.3,48.8 4.7,73.8 l 0.3,6.2 z"
     id="path1" /><g
     id="g169"><polygon
       class="st28"
       points="196.7,810.2 179.6,832.7 163.6,810.2 155.1,810.2 157.9,814.3 157.9,856 163.7,856 163.7,822.4 178.4,842.9 193.7,822.4 193.7,856 200.7,856 200.7,810.2 "
       id="polygon1" /><polygon
       class="st28"
       points="423.1,810.2 415.4,810.2 429.4,836.7 429.4,856 436.3,856 436.3,837.2 451.3,810.2 444.8,810.2 433.7,830.1 423.3,810.6 "
       id="polygon2" /><path
       class="st28"
       d="m 402.2,833.8 c 1.1,-0.7 2.1,-1.6 3,-2.7 0.9,-1.1 1.7,-2.3 2.4,-3.7 0.6,-1.4 1,-3.1 1,-4.9 0,-2.2 -0.4,-4.1 -1.3,-5.7 -0.8,-1.6 -2,-3 -3.4,-4 -1.5,-1.1 -3.2,-1.9 -5.1,-2.4 -2,-0.5 -4.1,-0.8 -6.4,-0.8 -2.9,0 -5.3,0.1 -7.1,0.4 -1.7,0.2 -3.4,0.6 -5,0.9 l -0.5,0.1 v 45 h 6.9 v -18.6 l 6.6,0.1 13.3,18.5 h 8.7 l -15.7,-20.7 c 0.7,-0.3 1.6,-0.8 2.6,-1.5 m -1.2,-7.3 c -0.4,1 -0.9,1.9 -1.5,2.7 -0.6,0.8 -1.3,1.4 -1.9,2 -0.5,0.4 -1.2,0.9 -2.1,1.3 h -9 v -17.7 h 3.9 c 3.8,0 6.6,0.7 8.5,2 1.8,1.3 2.7,3.5 2.7,6.4 0,1.2 -0.2,2.3 -0.6,3.3"
       id="path2" /><rect
       x="215.10001"
       y="810.20001"
       class="st28"
       width="6.9000001"
       height="45.799999"
       id="rect2" /><rect
       x="286.60001"
       y="810.20001"
       class="st28"
       width="6.9000001"
       height="45.799999"
       id="rect3" /><polygon
       class="st28"
       points="241.2,810.2 232.5,810.2 237.1,816 237.1,856 242.9,856 242.9,823.3 268.9,856 272.1,856 272.1,810.2 266.3,810.2 266.3,841.6 "
       id="polygon3" /><polygon
       class="st28"
       points="358.3,815.9 370.8,815.9 372.9,810.2 337.8,810.2 337.8,815.9 351.4,815.9 351.4,856 358.3,856 "
       id="polygon4" /><path
       class="st28"
       d="m 329.5,836.7 v 0 c -0.8,-0.9 -1.7,-1.7 -2.6,-2.5 -0.9,-0.7 -1.8,-1.3 -2.7,-1.8 l -4.2,-2.6 c -0.9,-0.5 -1.8,-1 -2.6,-1.6 -0.8,-0.5 -1.5,-1.1 -2.2,-1.7 -0.6,-0.6 -1.1,-1.3 -1.5,-2 -0.4,-0.7 -0.6,-1.5 -0.6,-2.5 0,-1.1 0.2,-2.1 0.7,-3 0.5,-0.9 1.1,-1.6 1.9,-2.2 0.8,-0.6 1.7,-1 2.7,-1.3 1,-0.3 2.1,-0.4 3.2,-0.4 2.1,0 4,0.4 5.6,1.3 1.1,0.6 2.2,1.3 3.1,2.1 l 1.1,1 v -7.2 l -0.3,-0.2 c -0.4,-0.2 -0.8,-0.5 -1.4,-0.7 -0.7,-0.3 -1.5,-0.6 -2.4,-0.9 -0.9,-0.3 -1.8,-0.5 -2.8,-0.7 -3.3,-0.6 -6.5,-0.2 -9.1,0.6 -1.9,0.6 -3.5,1.5 -4.9,2.7 -1.4,1.2 -2.4,2.6 -3.1,4.2 -0.7,1.6 -1.1,3.5 -1.1,5.5 0,2.2 0.7,4.3 2,6.2 1.3,1.9 3.5,3.8 6.5,5.7 l 6,3.6 c 0.7,0.4 1.3,0.9 2,1.5 0.7,0.6 1.2,1.3 1.7,2 0.5,0.8 0.7,1.6 0.7,2.6 0,1 -0.2,2 -0.7,2.8 -0.5,0.8 -1.1,1.5 -1.8,2.1 -0.8,0.6 -1.6,1.1 -2.6,1.4 -1,0.3 -2,0.5 -3,0.5 -2.4,0 -4.6,-0.4 -6.4,-1.2 -1.4,-0.6 -2.8,-1.4 -4,-2.3 l -1.3,-0.9 1,7.9 0.4,0.1 c 1.5,0.6 3.1,1 4.8,1.4 1.8,0.4 3.8,0.6 5.9,0.6 2.1,0 4.1,-0.3 5.9,-0.9 1.8,-0.6 3.4,-1.5 4.7,-2.6 1.3,-1.1 2.3,-2.5 3.1,-4.1 0.7,-1.6 1.1,-3.4 1.1,-5.4 0,-1.4 -0.3,-2.8 -0.8,-3.9 -0.5,-1.2 -1.2,-2.2 -2,-3.2"
       id="path4" /><path
       class="st28"
       d="m 804,836.7 c -0.8,-0.9 -1.7,-1.7 -2.6,-2.5 -0.9,-0.7 -1.8,-1.3 -2.7,-1.8 l -4.2,-2.6 c -0.9,-0.5 -1.8,-1 -2.6,-1.6 -0.8,-0.5 -1.5,-1.1 -2.2,-1.7 -0.6,-0.6 -1.1,-1.3 -1.5,-2 -0.4,-0.7 -0.6,-1.5 -0.6,-2.5 0,-1.1 0.2,-2.1 0.7,-3 0.5,-0.9 1.1,-1.6 1.9,-2.2 0.8,-0.6 1.7,-1 2.7,-1.3 1,-0.3 2.1,-0.4 3.2,-0.4 2.1,0 4,0.4 5.6,1.3 1.1,0.6 2.2,1.3 3.1,2.1 l 1.1,1 v -7.2 l -0.3,-0.2 c -0.4,-0.2 -0.8,-0.5 -1.4,-0.7 -0.7,-0.3 -1.6,-0.7 -2.4,-0.9 -0.9,-0.3 -1.8,-0.5 -2.8,-0.7 -3.3,-0.6 -6.5,-0.2 -9.1,0.6 -1.9,0.6 -3.5,1.5 -4.9,2.7 -1.4,1.2 -2.4,2.6 -3.2,4.2 -0.7,1.6 -1.1,3.5 -1.1,5.5 0,2.2 0.7,4.3 2,6.3 1.3,1.9 3.5,3.8 6.5,5.7 l 5.9,3.6 c 0.7,0.4 1.4,0.9 2,1.5 0.7,0.6 1.2,1.3 1.7,2 0.5,0.8 0.7,1.6 0.7,2.6 0,1 -0.2,2 -0.7,2.8 -0.4,0.8 -1,1.5 -1.8,2.1 -0.7,0.6 -1.6,1 -2.6,1.4 -1,0.3 -2,0.5 -3,0.5 -2.4,0 -4.6,-0.4 -6.5,-1.2 -1.4,-0.6 -2.7,-1.4 -4,-2.3 l -1.3,-0.9 1,7.9 0.4,0.1 c 1.5,0.6 3.1,1 4.8,1.4 1.8,0.4 3.8,0.6 5.9,0.6 2.1,0 4.1,-0.3 5.9,-0.9 1.8,-0.6 3.4,-1.5 4.7,-2.6 1.3,-1.1 2.3,-2.5 3.1,-4.1 0.7,-1.6 1.1,-3.4 1.1,-5.4 0,-1.4 -0.3,-2.8 -0.8,-3.9 -0.2,-1.3 -0.9,-2.3 -1.7,-3.3"
       id="path5" /><polygon
       class="st28"
       points="702.2,835.4 715,835.4 717.1,830.1 702.2,830.1 702.2,815.6 718,815.6 720.2,810.2 695.2,810.2 695.2,856 718.6,856 721.5,850.4 702.2,850.4 "
       id="polygon5" /><polygon
       class="st28"
       points="668,836.4 681.2,836.4 683.3,831.1 668,831.1 668,815.6 684.2,815.6 686.4,810.2 661,810.2 661,856 668,856 "
       id="polygon6" /><path
       class="st28"
       d="m 509.2,816.1 c -2,-2.1 -4.4,-3.8 -7.1,-4.9 -2.8,-1.2 -5.8,-1.7 -9,-1.7 -3.3,0 -6.3,0.6 -9.1,1.7 -2.7,1.2 -5.1,2.8 -7.1,4.9 -2,2.1 -3.6,4.6 -4.7,7.5 -1.1,2.9 -1.7,6.1 -1.7,9.5 0,3.5 0.6,6.7 1.7,9.6 1.1,2.9 2.7,5.4 4.7,7.5 2,2.1 4.4,3.7 7.1,4.9 2.7,1.2 5.8,1.7 9.1,1.7 3.3,0 6.3,-0.6 9,-1.7 2.8,-1.2 5.2,-2.8 7.1,-4.9 2,-2.1 3.6,-4.6 4.7,-7.5 1.1,-2.9 1.7,-6.1 1.7,-9.5 0,-3.4 -0.6,-6.7 -1.7,-9.5 -1.2,-3 -2.7,-5.5 -4.7,-7.6 m -1,17.1 c 0,2.6 -0.3,5.1 -1,7.3 -0.7,2.2 -1.7,4.1 -2.9,5.7 -1.3,1.6 -2.9,2.8 -4.7,3.7 -1.9,0.9 -4.1,1.4 -6.4,1.4 -2.4,0 -4.6,-0.5 -6.5,-1.4 -1.9,-0.9 -3.5,-2.1 -4.8,-3.7 -1.3,-1.6 -2.3,-3.5 -2.9,-5.7 -0.7,-2.2 -1,-4.6 -1,-7.3 0,-2.6 0.3,-5 1,-7.2 0.7,-2.2 1.7,-4.1 3,-5.7 1.3,-1.6 2.9,-2.8 4.7,-3.7 1.9,-0.9 4.1,-1.3 6.5,-1.3 2.4,0 4.6,0.5 6.4,1.3 1.9,0.9 3.5,2.1 4.7,3.7 1.3,1.6 2.3,3.5 2.9,5.7 0.6,2.2 1,4.6 1,7.2"
       id="path6" /><polygon
       class="st28"
       points="825.6,850.4 825.6,835.4 838.5,835.4 840.6,830.1 825.6,830.1 825.6,815.6 841.5,815.6 843.7,810.2 818.7,810.2 818.7,856 842,856 844.9,850.4 "
       id="polygon7" /><polygon
       class="st28"
       points="737,810.2 728.3,810.2 732.9,816 732.9,856 738.7,856 738.7,823.3 764.8,856 768,856 768,810.2 762.1,810.2 762.1,841.6 "
       id="polygon8" /><polygon
       class="st28"
       points="630.9,835.4 643.7,835.4 645.9,830.1 630.9,830.1 630.9,815.6 646.7,815.6 648.9,810.2 624,810.2 624,856 647.3,856 650.2,850.4 630.9,850.4 "
       id="polygon9" /><polygon
       class="st28"
       points="532.7,836.4 545.9,836.4 548.1,831.1 532.7,831.1 532.7,815.6 548.9,815.6 551.1,810.2 525.7,810.2 525.7,856 532.7,856 "
       id="polygon10" /><path
       class="st28"
       d="m 607.5,815.9 c -2,-2.1 -4.5,-3.7 -7.4,-4.8 -2.9,-1.1 -6.2,-1.7 -9.9,-1.7 -2.9,0 -5.5,0.1 -7.7,0.4 -2,0.3 -3.8,0.6 -5.5,0.9 l -0.6,0.1 V 855 l 0.5,0.1 c 1.6,0.4 3.4,0.8 5.6,1.1 2.3,0.4 4.9,0.6 7.6,0.6 3.6,0 6.8,-0.6 9.7,-1.7 2.9,-1.1 5.4,-2.8 7.5,-4.8 2,-2.1 3.6,-4.6 4.7,-7.4 1.1,-2.9 1.7,-6.1 1.7,-9.6 0,-3.6 -0.5,-7 -1.6,-9.9 -1,-2.9 -2.6,-5.4 -4.6,-7.5 m -1.1,17.5 c 0,2.3 -0.4,4.6 -1.1,6.7 -0.7,2.2 -1.8,4.1 -3.1,5.8 -1.4,1.7 -3.1,3.1 -5.1,4.1 -3,1.5 -6.8,1.9 -10.8,1.2 -1.3,-0.2 -2.3,-0.4 -3,-0.6 v -35.8 h 5.1 c 2.9,0 5.4,0.5 7.6,1.4 2.2,0.9 4.1,2.2 5.6,3.8 1.5,1.6 2.7,3.6 3.5,5.9 0.9,2.2 1.3,4.8 1.3,7.5"
       id="path10" /></g><g
     id="g168"><path
       class="st29"
       d="m 507.1,163.2 v 0 h -14.2 c 0,-3.2 2.1,-5.8 4.9,-6.8 0.7,-0.2 1.4,-0.3 2.2,-0.3 3.9,0 7.1,3.2 7.1,7.1"
       id="path11" /><path
       class="st30"
       d="m 507.1,163.2 c 0,3.9 -3.2,7.1 -7.1,7.1 -0.2,0 -0.5,0 -0.7,0 -2.3,-0.2 -4.2,-1.5 -5.4,-3.4 -0.6,-1.1 -1,-2.3 -1,-3.6 h 14.2 z"
       id="path12" /><path
       class="st29"
       d="m 500,143.1 v 0 c -1.5,4 -5.1,10.4 -5.1,10.4 h 5.1 5.2 c 0,0 -3.7,-6.4 -5.2,-10.4"
       id="path13" /><path
       class="st30"
       d="m 500,153.5 v 0 h -5.1 l 3,3 c 0.7,-0.2 1.4,-0.3 2.2,-0.3 0.8,0 1.5,0.1 2.2,0.3 l 3,-3 z"
       id="path14" /><path
       class="st31"
       d="m 425.3,293.8 c -0.7,0.4 -1.3,0.9 -2,1.3 l 1.4,1.8 c 0.7,-0.5 1.4,-0.9 2.1,-1.4 18.1,-11.7 39,-19.2 61.6,-21.1 0,-0.8 0.1,-1.6 0.1,-2.3 -23.2,2 -44.7,9.6 -63.2,21.7"
       id="path15" /><path
       class="st31"
       d="m 402.5,284.2 c -12.1,9.5 -22.8,20.7 -31.8,33.2 l -2.1,-1.1 c 9.1,-12.8 20,-24.3 32.4,-33.9 z"
       id="path16" /><path
       class="st31"
       d="m 500,591.1 c -2.4,0 -4.7,0 -7,-0.1 0.3,0.3 0.6,0.7 0.9,1 l 1.4,1.4 c 1.6,0 3.2,0.1 4.7,0.1 1.6,0 3.2,0 4.7,-0.1 l 1.4,-1.4 c 0.3,-0.3 0.6,-0.6 0.9,-1 -2.3,0.1 -4.6,0.1 -7,0.1"
       id="path17" /><path
       class="st31"
       d="m 477.4,494.7 c 4.9,1.4 9.3,2.1 14.3,2.6 v 2.3 c -4.7,-0.4 -8.8,-1.1 -13.5,-2.3 z"
       id="path18" /><path
       class="st31"
       d="m 320.9,365.6 c 0.6,0.2 1.4,0.5 2.2,0.8 v 0 c 16.4,-67.8 70.9,-120.8 139.4,-135.2 -0.3,-0.7 -0.7,-1.4 -1.1,-2.2 -69,14.7 -123.8,68.2 -140.5,136.6"
       id="path19" /><path
       class="st31"
       d="m 358.1,338.5 c -4.9,9.8 -8.8,20.1 -11.6,30.9 -0.8,0 -1.6,0 -2.4,0 2.8,-11.1 6.9,-21.8 11.9,-31.8 z"
       id="path20" /><path
       class="st31"
       d="m 343.5,383.7 c -1.3,8.3 -2.1,16.8 -2.1,25.5 0,11.3 1.2,22.3 3.4,32.9 -0.7,0.3 -1.5,0.5 -2.2,0.8 -2.3,-10.9 -3.6,-22.2 -3.6,-33.7 0,-8.8 0.7,-17.4 2.1,-25.9 z"
       id="path21" /><path
       class="st31"
       d="m 597.1,283.9 c 13.7,10.6 25.5,23.4 35.1,37.8 0.9,-0.1 1.8,-0.1 2.7,-0.1 C 625,306.5 612.7,293.1 598.5,282 Z"
       id="path22" /><path
       class="st31"
       d="m 560.1,341.1 c 2.6,2.3 5.1,4.8 7.5,7.4 l -2.2,1.1 c -2.1,-2.3 -4.4,-4.6 -6.7,-6.6 z"
       id="path23" /><path
       class="st31"
       d="m 569.9,318.9 c 7,5.5 13.4,11.7 19,18.7 l -2.1,1.1 c -5.4,-6.6 -11.6,-12.7 -18.3,-17.9 z"
       id="path24" /><path
       class="st31"
       d="m 582.8,302.3 c 9.5,7.4 18,16 25.2,25.6 0.7,-0.3 1.4,-0.7 2.2,-1 -7.4,-9.9 -16.2,-18.8 -26,-26.4 z"
       id="path25" /><path
       class="st31"
       d="m 319.7,371.2 c -2.6,12.3 -3.9,25 -3.9,38 0,21.8 3.8,42.7 10.8,62.1 l 2.1,-1.1 c -6.8,-19.1 -10.5,-39.6 -10.5,-61.1 0,-12.1 1.2,-23.9 3.4,-35.4 -0.9,-0.8 -1.5,-1.6 -1.9,-2.5"
       id="path26" /><path
       class="st31"
       d="m 362.9,397.7 c -0.3,3.8 -0.5,7.6 -0.5,11.5 0,9.4 0.9,18.6 2.8,27.5 l 2.3,-0.3 c -1.8,-8.8 -2.7,-17.8 -2.7,-27.1 0,-3.8 0.2,-7.5 0.5,-11.2 -0.8,-0.2 -1.6,-0.3 -2.4,-0.4 z"
       id="path27" /><path
       class="st31"
       d="m 385.8,408.9 c 0,8.3 0.9,16.4 2.5,24.2 l 2.3,-0.3 c -1.6,-7.7 -2.5,-15.6 -2.5,-23.8 v 0 c -0.7,0 -1.9,-0.1 -2.3,-0.1"
       id="path28" /><path
       class="st31"
       d="m 409.6,418 c 0.3,3.4 0.9,6.8 1.6,10.1 l 2.2,-1.1 c -0.6,-3 -1.1,-6 -1.4,-9.1 v 0 c -1,0.1 -1.5,0.1 -2.4,0.1 z"
       id="path29" /><path
       class="st31"
       d="m 419.5,545.8 c 19.9,11.8 42.4,19.3 66.6,21.4 0,0 -7,0.3 -12.3,0.8 -20.2,-3.3 -38.2,-9.9 -55.1,-20 0,0 0.7,-1.7 0.8,-2.2"
       id="path30" /><path
       class="st31"
       d="m 428.2,523.7 c 5.3,3.4 10.2,6 15.9,8.6 0.3,0.8 0.7,1.6 1.1,2.4 l 0.4,0.8 c -6.6,-2.9 -12.2,-5.9 -18.3,-9.7 z"
       id="path31" /><path
       class="st31"
       d="m 468.2,590.7 c -0.4,-0.8 -0.7,-1.7 -0.9,-2.5 -26.2,-4.8 -50.4,-15.2 -71.4,-29.9 -4.2,-1.9 -13.1,-7 -13.1,-7 24.1,19.9 53.3,33.8 85.4,39.4"
       id="path32" /><path
       class="st31"
       d="m 438.5,502.6 c 2.1,1.4 4.3,2.7 6.5,4 l -1,2.1 c -2.3,-1.3 -4.5,-2.6 -6.7,-4.1 z"
       id="path33" /><path
       class="st31"
       d="m 491.6,520.7 v 2.3 c -12.5,-0.9 -24.4,-3.8 -35.5,-8.4 -0.5,-0.7 -1,-1.5 -1.4,-2.3 -0.2,-0.4 -0.4,-0.8 -0.6,-1.2 11.6,5.3 24.3,8.6 37.5,9.6"
       id="path34" /><path
       class="st31"
       d="m 377.2,347.3 c -3.4,6.7 -6.2,13.7 -8.5,20.9 0.9,-0.1 1.7,-0.1 2.5,-0.2 2.2,-6.8 4.9,-13.5 8.1,-19.8 z"
       id="path35" /><path
       class="st31"
       d="m 400.1,353.9 c -2.4,4.4 -4.6,9 -6.5,13.7 0.8,0.1 1.6,0.1 2.4,0.2 1.8,-4.4 3.8,-8.7 6.1,-12.8 z"
       id="path36" /><path
       class="st31"
       d="m 421,364.5 c -1.7,3 -3.3,6.1 -4.6,9.4 v 0 c 0.7,0.4 1.3,0.8 2,1.3 1.4,-3.3 2.9,-6.5 4.7,-9.5 z"
       id="path37" /><path
       class="st31"
       d="m 441.2,343.1 -1.4,-1.9 c -4,3.5 -7.6,7.4 -10.9,11.5 l 2.1,1.1 c 3.1,-3.8 6.5,-7.4 10.2,-10.7"
       id="path38" /><path
       class="st31"
       d="m 429.7,319.3 c -8.3,6.5 -15.7,14.2 -22,22.8 l 2.1,1.1 c 6.1,-8.3 13.2,-15.7 21.3,-22 z"
       id="path39" /><path
       class="st31"
       d="m 415.4,300.8 c -9.9,7.7 -18.7,16.8 -26.2,26.9 l 2.1,1.1 c 7.3,-9.9 15.9,-18.7 25.5,-26.2 z"
       id="path40" /><path
       class="st31"
       d="m 679.1,365.6 c -0.6,0.2 -1.4,0.5 -2.2,0.8 C 660.5,298.6 606,245.6 537.5,231.2 c 0.3,-0.7 0.7,-1.4 1.1,-2.2 69,14.7 123.8,68.2 140.5,136.6"
       id="path41" /><path
       class="st31"
       d="m 521.9,497.3 c -4.5,1.1 -9,1.9 -13.6,2.3 v -2.3 c 4.8,-0.4 9.5,-1.3 14.3,-2.5 z"
       id="path42" /><path
       class="st31"
       d="m 581.7,547.8 c -16.8,9.9 -35.5,16.9 -55.5,20.2 -5.3,-0.5 -12.3,-0.8 -12.3,-0.8 24.3,-2.1 47.1,-9.7 67,-21.6 z"
       id="path43" /><path
       class="st31"
       d="m 572.9,525.8 c -5.9,3.7 -12,6.9 -18.4,9.7 l 0.4,-0.8 c 0.4,-0.8 0.7,-1.6 1.1,-2.4 5.6,-2.5 10.9,-5.4 16,-8.6 z"
       id="path44" /><path
       class="st31"
       d="m 617.3,551.2 v 0 c 0,0 -8.9,5.1 -13.1,7 v 0 c -21,14.7 -45.3,25.1 -71.5,29.9 -0.2,0.9 -0.5,1.7 -0.9,2.5 32.1,-5.5 61.4,-19.4 85.5,-39.4"
       id="path45" /><path
       class="st31"
       d="m 561,502.9 c -2,1.3 -4,2.5 -6,3.6 l 1,2.1 c 2.1,-1.2 4.2,-2.4 6.2,-3.8 z"
       id="path46" /><path
       class="st31"
       d="m 545.9,511.2 c -0.2,0.4 -0.4,0.8 -0.6,1.2 -0.5,0.8 -0.9,1.5 -1.5,2.3 -11,4.6 -23,7.5 -35.4,8.4 v -2.3 c 13.2,-1.1 25.9,-4.4 37.5,-9.6"
       id="path47" /><path
       class="st31"
       d="m 638.4,331.9 c 6.5,11.7 11.6,24.2 15.1,37.4 0.8,0 1.6,0 2.4,0 -3.6,-14 -9,-27.2 -16,-39.5 -0.4,0.7 -0.9,1.4 -1.5,2.1"
       id="path48" /><path
       class="st31"
       d="m 620.4,347.8 c 3.3,6.5 6.1,13.2 8.4,20.3 0.8,0 1.7,0.1 2.5,0.2 -2.4,-7.5 -5.3,-14.8 -8.9,-21.7 -1.5,0.9 -1.8,1.1 -2,1.2"
       id="path49" /><path
       class="st31"
       d="m 599.7,358.5 c 1.5,3 2.9,6.1 4.2,9.2 0.8,-0.1 1.6,-0.1 2.4,-0.2 -1.4,-3.4 -2.9,-6.8 -4.5,-10.1 z"
       id="path50" /><path
       class="st31"
       d="M 578.9,369.1 581,368 c 1,1.9 1.9,3.8 2.7,5.8 -0.7,0.4 -1.3,0.8 -2,1.3 -0.9,-2 -1.8,-4 -2.8,-6"
       id="path51" /><path
       class="st31"
       d="m 658.8,383.3 -2.3,0.4 c 1.4,8.3 2.1,16.8 2.1,25.5 0,12.5 -1.5,24.7 -4.2,36.3 l 2.4,-0.1 c 2.7,-11.7 4.1,-23.8 4.1,-36.3 0,-8.7 -0.7,-17.3 -2.1,-25.8"
       id="path52" /><path
       class="st31"
       d="m 637.1,397.7 c -0.8,0.1 -1.5,0.2 -2.3,0.3 0.3,3.7 0.5,7.5 0.5,11.2 0,10 -1.1,19.7 -3.2,29.1 l 2.2,1 c 2.2,-9.7 3.3,-19.7 3.3,-30.1 0,-3.9 -0.2,-7.7 -0.5,-11.5 z"
       id="path53" /><path
       class="st31"
       d="m 611.8,409 c 0.3,0 0.6,0 0.9,0 h 0.1 c 0.5,0 0.9,0 1.3,0 0,8.9 -1,17.6 -2.9,25.9 h -2.4 c 2,-8.3 3.1,-17 3,-25.9"
       id="path54" /><path
       class="st31"
       d="m 588,418 c 0.7,0 1.4,0 2.1,0 h 0.3 c -0.5,5.4 -1.5,10.6 -2.9,15.6 l -2.1,-1.1 c 1.2,-4.7 2.1,-9.5 2.6,-14.5 z"
       id="path55" /><path
       class="st32"
       d="m 396.4,209.5 c -7.7,6.6 -13.4,14 -17.2,21.6 -3.8,7.6 -5.5,15 -5.6,22 5,-14.7 12.5,-29.5 22.8,-43.6"
       id="path56" /><path
       class="st32"
       d="m 416.6,203.1 c -8,5.6 -14.3,12 -18.8,18.7 -4.5,6.8 -6.9,13.6 -7.7,20.1 6.4,-13.3 15.1,-26.5 26.5,-38.8"
       id="path57" /><path
       class="st32"
       d="m 436.7,198.7 c -8.3,4.6 -15,10 -20,15.9 -5,5.9 -8,12.1 -9.6,18 7.7,-11.8 17.5,-23.3 29.6,-33.9"
       id="path58" /><path
       class="st32"
       d="m 376.5,218 v 0 c -7.1,7.8 -12.2,16.1 -15.3,24.6 -3,8.4 -3.9,16.4 -3.1,23.7 v 0 c 3.4,-16 9.4,-32.3 18.4,-48.3"
       id="path59" /><path
       class="st32"
       d="m 345.7,610.8 c 19.7,7.5 40,10 58.4,8.1 16.9,-1.7 31.4,-6.9 43,-14 -0.7,-0.2 -1.4,-0.3 -2.1,-0.5 -31.4,9.1 -65.4,11.7 -99.3,6.4"
       id="path60" /><path
       class="st32"
       d="m 315.2,582.4 c 16.9,10.2 34.9,15.8 52,17 16.1,1.1 30.6,-1.6 42.6,-6.8 -0.4,-0.2 -0.8,-0.3 -1.1,-0.5 -30.4,3.7 -62.4,0.8 -93.5,-9.7"
       id="path61" /><path
       class="st32"
       d="m 291.8,550.5 v 0 c 14.2,12.3 29.9,20.4 45.4,24.3 15.1,3.7 29.1,3.3 41.2,0.1 -0.2,-0.1 -0.3,-0.2 -0.5,-0.4 -28.6,-1.2 -58.1,-9 -86.1,-24"
       id="path62" /><path
       class="st32"
       d="m 275,516.5 c 11.6,13.8 25.1,23.9 39,29.9 13.6,5.9 26.8,7.7 38.7,6.5 -0.1,-0.1 -0.2,-0.2 -0.3,-0.3 -26.4,-5.5 -53,-17.5 -77.4,-36.1"
       id="path63" /><path
       class="st32"
       d="m 264.1,481.8 c 9.2,14.8 20.5,26.4 32.7,34.2 12,7.7 24.1,11.4 35.5,12.1 -0.1,-0.1 -0.1,-0.2 -0.2,-0.3 -24,-9.5 -47.3,-24.9 -68,-46"
       id="path64" /><path
       class="st32"
       d="m 258.5,447.3 c 6.9,15.3 16.1,27.9 26.6,37.1 10.2,9 21.1,14.3 31.7,16.6 -0.1,-0.2 -0.1,-0.3 -0.2,-0.5 -21.2,-12.5 -41.1,-30.4 -58.1,-53.2"
       id="path65" /><path
       class="st32"
       d="m 257.5,413.7 c 4.7,15.5 11.9,28.7 20.8,38.9 8.5,9.8 18,16.4 27.6,20.2 -0.1,-0.2 -0.1,-0.5 -0.2,-0.7 -18.1,-15 -34.7,-34.6 -48.2,-58.4"
       id="path66" /><path
       class="st32"
       d="m 260.7,381.8 c 2.7,15.3 8.1,28.8 15.3,39.6 6.8,10.3 14.9,17.8 23.4,22.8 0,-0.3 -0.1,-0.6 -0.1,-0.9 -15.3,-16.8 -28.6,-37.5 -38.6,-61.5"
       id="path67" /><path
       class="st32"
       d="m 267.5,351.9 c 0.9,14.8 4.5,28.3 10.1,39.5 5.2,10.6 11.9,18.7 19.4,24.5 0,-0.3 0,-0.6 0,-1 -13.2,-18.4 -22.9,-39.9 -29.5,-63"
       id="path68" /><path
       class="st32"
       d="m 277.3,324.5 c -0.7,14.1 1.2,27.3 5.2,38.7 3.8,10.6 9.2,19.2 15.6,25.7 0,-0.3 0.1,-0.7 0.1,-1 -9.9,-18.8 -17.1,-40.8 -20.9,-63.4"
       id="path69" /><path
       class="st32"
       d="m 289.8,299.8 c -2.2,13.2 -1.8,25.9 0.8,37.2 2.5,10.6 6.7,19.4 12.1,26.4 0.1,-0.3 0.2,-0.6 0.2,-0.9 -6.8,-18.5 -11.9,-41 -13.1,-62.7"
       id="path70" /><path
       class="st32"
       d="m 304.3,277.9 c -3.5,12.3 -4.5,24.3 -3.2,35.2 1.2,10.4 4.4,19.3 8.7,26.7 0.1,-0.2 0.2,-0.5 0.3,-0.7 -4.5,-18.4 -6.9,-40.3 -5.8,-61.2"
       id="path71" /><path
       class="st32"
       d="m 320.7,258.8 c -4.7,11.2 -6.9,22.4 -6.9,32.9 0,10.1 2.1,19 5.5,26.6 0.1,-0.1 0.2,-0.3 0.2,-0.4 -2.3,-18.2 -2.3,-39.1 1.2,-59.1"
       id="path72" /><path
       class="st32"
       d="m 338.3,242.6 c -5.6,10.1 -9,20.4 -10.1,30.3 -1.1,9.7 0,18.4 2.5,26 l 0.2,-0.2 c -0.3,-17.8 1.9,-37.3 7.4,-56.1"
       id="path73" /><path
       class="st32"
       d="m 357.1,229 v 0 c -6.5,8.9 -10.7,18.3 -12.9,27.5 -2.1,9.1 -2,17.6 -0.4,25.1 0,0 0.1,-0.1 0.1,-0.1 1.5,-17 5.8,-35 13.2,-52.5"
       id="path74" /><path
       class="st32"
       d="m 480.8,613.4 c -30.2,12.8 -62.9,20.2 -96.8,20.8 22.6,4.1 45.1,2.9 64.8,-2.6 12.3,-3.4 23.1,-8.2 32.3,-13.9 -0.2,-0.5 -0.3,-1 -0.3,-1.6 z"
       id="path75" /><path
       class="st33"
       d="m 386.8,231.1 c 4,-7.4 7.3,-14.6 9.6,-21.6 -10.3,14.1 -17.8,28.9 -22.8,43.6 4.8,-7.3 9.2,-14.6 13.2,-22"
       id="path76" /><path
       class="st33"
       d="m 405.1,222.5 c 4.6,-6.6 8.5,-13 11.5,-19.4 -11.4,12.4 -20.1,25.5 -26.5,38.8 5.3,-6.4 10.4,-12.8 15,-19.4"
       id="path77" /><path
       class="st33"
       d="m 423.8,215.9 c 5.1,-5.8 9.5,-11.5 13,-17.2 -12.2,10.6 -21.9,22.1 -29.6,34 5.8,-5.5 11.4,-11.1 16.6,-16.8"
       id="path78" /><path
       class="st33"
       d="m 480.8,610.3 c -15.9,3.9 -30.9,7.3 -45.4,10.7 -18.9,4.4 -36,8.4 -51.4,13.2 33.9,-0.7 66.6,-8 96.8,-20.8 z"
       id="path79" /><path
       class="st33"
       d="m 481.6,606 c -97,-7.3 -165.1,-76.7 -177.9,-166 -13,-92.8 34.5,-174.8 110.7,-209.4 9.4,-4.4 19.9,-7.8 22.4,-8.7 l -0.1,-0.4 c 0,0 -2.1,0.6 -6,1.9 -28.3,9.1 -52.7,24 -72.6,42.9 4,-8.2 7.8,-16.3 11,-24.6 3.2,-8.2 5.8,-16.1 7.4,-23.8 -9,15.9 -15,32.3 -18.4,48.3 v 0 c -5,4.7 -9.7,9.8 -14.1,15 3.1,-8.9 5.9,-17.7 8.3,-26.6 2.4,-9 4.1,-17.6 4.8,-25.7 -7.4,17.5 -11.6,35.5 -13.2,52.5 0,0 0,0.1 -0.1,0.1 -4.5,5.3 -8.7,10.9 -12.6,16.7 2.1,-9.4 3.9,-18.8 5.2,-28.1 1.4,-9.7 2.1,-18.9 1.9,-27.6 -5.5,18.8 -7.7,38.3 -7.4,56.1 l -0.2,0.2 c -3.9,5.9 -7.5,12 -10.8,18.3 1,-9.8 1.7,-19.6 2,-29.2 0.3,-10.3 -0.1,-20 -1.3,-29.1 -3.4,19.9 -3.4,40.8 -1.1,59 -0.1,0.2 -0.2,0.3 -0.2,0.5 -3.3,6.4 -6.2,13 -8.8,19.8 -0.2,-10.1 -0.6,-20.1 -1.5,-29.9 -0.9,-10.8 -2.4,-20.9 -4.7,-30.2 -1.2,20.9 1.3,42.8 5.8,61.2 -0.1,0.2 -0.2,0.5 -0.3,0.7 -2.6,6.9 -4.8,14 -6.6,21.2 -1.4,-10.2 -3,-20.3 -5,-30.3 -2.3,-11.1 -5,-21.5 -8.4,-31 1.2,21.7 6.4,44.2 13.1,62.7 -0.1,0.3 -0.1,0.6 -0.2,0.9 -1.8,7.5 -3.2,15 -4.1,22.7 -2.7,-10.3 -5.5,-20.5 -8.7,-30.4 -3.7,-11.3 -7.7,-21.7 -12.4,-31.2 3.8,22.5 10.9,44.5 20.9,63.4 0,0.3 -0.1,0.6 -0.1,1 -0.9,8 -1.3,16.1 -1.3,24.2 -4.1,-10.3 -8.3,-20.5 -12.8,-30.5 -5.2,-11.2 -10.6,-21.5 -16.6,-30.7 6.6,23.1 16.3,44.6 29.4,63.1 0,0.3 0,0.7 0,1 0.2,8.5 0.9,17.1 2.1,25.7 -5.6,-10.3 -11.4,-20.4 -17.4,-30.3 -6.7,-10.9 -13.6,-20.9 -20.9,-29.6 10,24 23.3,44.8 38.5,61.5 0,0.3 0.1,0.6 0.1,0.9 1.4,9 3.4,17.9 6,26.7 -7.4,-10 -14.9,-20 -22.6,-29.5 -8.3,-10.3 -16.6,-19.6 -25.2,-27.7 13.4,23.8 30,43.4 48.2,58.4 0.1,0.2 0.1,0.4 0.2,0.7 2.8,9.1 6.2,18.1 10.2,26.9 -9.4,-9.4 -18.6,-18.6 -28.1,-27.5 -10,-9.4 -19.8,-17.8 -29.6,-24.9 17,22.8 37,40.7 58.1,53.3 0.1,0.2 0.1,0.3 0.2,0.5 4.4,9.2 9.3,17.9 14.8,26.1 -11.3,-8.2 -22.3,-16.3 -33.5,-24.1 -11.6,-8.1 -22.8,-15.3 -34,-21.2 20.7,21.1 44.1,36.4 68,45.9 0.1,0.1 0.2,0.2 0.2,0.3 6,8.7 12.5,16.7 19.7,24.1 -13.2,-6.5 -26,-13 -38.8,-19.2 -13.3,-6.4 -25.9,-12.1 -38.2,-16.4 24.4,18.5 51,30.5 77.5,36.2 0.1,0.1 0.2,0.2 0.3,0.3 7.5,7.7 15.6,14.6 24.2,20.8 -14.8,-4.2 -29,-8.6 -43,-12.7 -14.9,-4.4 -28.8,-8.1 -42.2,-10.6 28,15 57.5,22.8 86.1,24 0.2,0.1 0.3,0.3 0.5,0.4 8.9,6.3 18.3,11.7 28.1,16.3 -15.8,-1.5 -30.9,-3.4 -45.7,-5.1 -16.4,-1.9 -31.5,-3.4 -45.8,-3.7 31.1,10.6 63.1,13.5 93.4,9.7 0.4,0.2 0.8,0.3 1.1,0.5 10.2,4.6 20.8,8.2 31.7,11 -16.3,1.3 -31.8,2.1 -46.8,3 -17.8,1 -33.9,2.1 -48.9,4.2 33.9,5.3 67.9,2.7 99.3,-6.3 0.7,0.2 1.4,0.3 2.1,0.5 10.9,2.4 22.2,3.9 33.7,4.5 v 0 -0.9 c 0.3,-1 0.5,-1.9 1,-2.7"
       id="path80" /><path
       class="st32"
       d="m 603.6,209.5 c 7.7,6.6 13.4,14 17.2,21.6 3.8,7.6 5.5,15 5.6,22 -5,-14.7 -12.5,-29.5 -22.8,-43.6"
       id="path81" /><path
       class="st32"
       d="m 583.4,203.1 c 8,5.6 14.3,12 18.8,18.7 4.5,6.8 6.9,13.6 7.7,20.1 -6.4,-13.3 -15.1,-26.5 -26.5,-38.8"
       id="path82" /><path
       class="st32"
       d="m 563.3,198.7 c 8.3,4.6 15,10 20,15.9 5,5.9 8,12.1 9.6,18 -7.7,-11.8 -17.5,-23.3 -29.6,-33.9"
       id="path83" /><path
       class="st32"
       d="m 623.5,218 v 0 c 7.1,7.8 12.2,16.1 15.3,24.6 3,8.4 3.9,16.4 3.1,23.7 -3.4,-16 -9.4,-32.3 -18.4,-48.3"
       id="path84" /><path
       class="st32"
       d="m 654.3,610.8 c -19.7,7.5 -40,10 -58.4,8.1 -16.9,-1.7 -31.4,-6.9 -43,-14 0.7,-0.2 1.4,-0.3 2.1,-0.5 31.4,9.1 65.4,11.7 99.3,6.4"
       id="path85" /><path
       class="st32"
       d="m 684.8,582.4 c -16.9,10.2 -34.9,15.8 -52,17 -16.1,1.1 -30.6,-1.6 -42.6,-6.8 0.4,-0.2 0.8,-0.3 1.1,-0.5 30.4,3.7 62.4,0.8 93.5,-9.7"
       id="path86" /><path
       class="st32"
       d="m 708.2,550.5 v 0 c -14.2,12.3 -29.9,20.4 -45.4,24.3 -15.1,3.7 -29.1,3.3 -41.2,0.1 0.2,-0.1 0.3,-0.2 0.5,-0.4 28.6,-1.2 58.1,-9 86.1,-24"
       id="path87" /><path
       class="st32"
       d="m 725,516.5 c -11.6,13.8 -25.1,23.9 -39,29.9 -13.6,5.9 -26.8,7.7 -38.8,6.5 0.1,-0.1 0.2,-0.2 0.3,-0.3 26.5,-5.5 53.1,-17.5 77.5,-36.1"
       id="path88" /><path
       class="st32"
       d="m 735.9,481.8 c -9.2,14.8 -20.5,26.4 -32.7,34.2 -12,7.7 -24.1,11.4 -35.5,12.1 0.1,-0.1 0.1,-0.2 0.2,-0.3 24,-9.5 47.3,-24.9 68,-46"
       id="path89" /><path
       class="st32"
       d="m 741.5,447.3 c -6.9,15.3 -16.1,27.9 -26.6,37.1 -10.2,9 -21.1,14.3 -31.7,16.6 0.1,-0.2 0.2,-0.3 0.2,-0.5 21.2,-12.5 41.1,-30.4 58.1,-53.2"
       id="path90" /><path
       class="st32"
       d="m 742.5,413.7 c -4.7,15.5 -11.9,28.7 -20.8,38.9 -8.5,9.8 -18,16.4 -27.6,20.2 0.1,-0.2 0.1,-0.5 0.2,-0.7 18.1,-15 34.7,-34.6 48.2,-58.4"
       id="path91" /><path
       class="st32"
       d="m 739.3,381.8 c -2.7,15.3 -8.1,28.8 -15.2,39.6 -6.8,10.3 -14.9,17.8 -23.4,22.8 0,-0.3 0.1,-0.6 0.1,-0.9 15.2,-16.8 28.5,-37.5 38.5,-61.5"
       id="path92" /><path
       class="st32"
       d="m 732.5,351.9 c -0.9,14.8 -4.5,28.3 -10.1,39.5 -5.2,10.6 -11.9,18.7 -19.4,24.5 0,-0.3 0,-0.6 0,-1 13.2,-18.4 22.9,-39.9 29.5,-63"
       id="path93" /><path
       class="st32"
       d="m 722.7,324.5 c 0.7,14.1 -1.2,27.3 -5.2,38.7 -3.8,10.6 -9.2,19.2 -15.6,25.7 0,-0.3 -0.1,-0.7 -0.1,-1 9.9,-18.8 17.1,-40.8 20.9,-63.4"
       id="path94" /><path
       class="st32"
       d="m 710.3,299.8 c 2.2,13.2 1.8,25.9 -0.8,37.2 -2.5,10.6 -6.7,19.4 -12.1,26.4 -0.1,-0.3 -0.1,-0.6 -0.2,-0.9 6.7,-18.5 11.8,-41 13.1,-62.7"
       id="path95" /><path
       class="st32"
       d="m 695.7,277.9 c 3.5,12.3 4.5,24.3 3.2,35.2 -1.2,10.4 -4.4,19.3 -8.7,26.7 -0.1,-0.2 -0.2,-0.5 -0.3,-0.7 4.5,-18.4 6.9,-40.3 5.8,-61.2"
       id="path96" /><path
       class="st32"
       d="m 679.4,258.8 c 4.7,11.2 6.9,22.4 6.9,32.9 0,10.1 -2.1,19 -5.5,26.6 -0.1,-0.1 -0.2,-0.3 -0.2,-0.4 2.2,-18.2 2.2,-39.1 -1.2,-59.1"
       id="path97" /><path
       class="st32"
       d="m 661.7,242.6 c 5.6,10.1 9,20.4 10.1,30.3 1.1,9.7 0,18.4 -2.5,26 l -0.2,-0.2 c 0.3,-17.8 -1.9,-37.3 -7.4,-56.1"
       id="path98" /><path
       class="st32"
       d="m 642.9,229 v 0 c 6.5,8.9 10.7,18.3 12.9,27.5 2.1,9.1 2,17.6 0.4,25.1 0,0 -0.1,-0.1 -0.1,-0.1 -1.5,-17 -5.8,-35 -13.2,-52.5"
       id="path99" /><path
       class="st32"
       d="m 519.2,613.4 c 30.2,12.8 62.9,20.2 96.8,20.8 -22.6,4.1 -45.1,2.9 -64.8,-2.6 -12.3,-3.4 -23.1,-8.2 -32.3,-13.9 0.2,-0.5 0.3,-1 0.3,-1.6 z"
       id="path100" /><path
       class="st33"
       d="m 613.2,231.1 c -4,-7.4 -7.3,-14.6 -9.6,-21.6 10.3,14.1 17.8,28.9 22.8,43.6 -4.8,-7.3 -9.2,-14.6 -13.2,-22"
       id="path101" /><path
       class="st33"
       d="m 594.9,222.5 c -4.6,-6.6 -8.5,-13 -11.5,-19.4 11.4,12.4 20.1,25.5 26.5,38.8 -5.3,-6.4 -10.4,-12.8 -15,-19.4"
       id="path102" /><path
       class="st33"
       d="m 576.3,215.9 c -5.1,-5.8 -9.5,-11.5 -13,-17.2 12.2,10.6 21.9,22.1 29.6,34 -5.9,-5.5 -11.5,-11.1 -16.6,-16.8"
       id="path103" /><path
       class="st33"
       d="m 519.2,610.3 c 15.9,3.9 30.9,7.3 45.4,10.7 18.9,4.4 36,8.4 51.4,13.2 -33.9,-0.7 -66.6,-8 -96.8,-20.8 z"
       id="path104" /><path
       class="st33"
       d="m 518.4,606 c 97,-7.3 165.1,-76.7 177.9,-166 13,-92.8 -34.5,-174.8 -110.7,-209.4 -9.4,-4.4 -19.9,-7.8 -22.4,-8.7 l 0.1,-0.4 c 0,0 2.1,0.6 6,1.9 28.3,9.1 52.7,24 72.6,42.9 -4,-8.2 -7.8,-16.3 -11,-24.6 -3.2,-8.2 -5.8,-16.1 -7.4,-23.8 9,15.9 15,32.3 18.4,48.3 v 0 c 5,4.7 9.7,9.8 14.2,15 -3.1,-8.9 -5.9,-17.7 -8.3,-26.6 -2.4,-9 -4,-17.6 -4.8,-25.7 7.4,17.5 11.6,35.5 13.2,52.5 0,0 0,0.1 0.1,0.1 4.5,5.3 8.7,10.9 12.6,16.7 -2.1,-9.4 -3.9,-18.8 -5.2,-28.1 -1.4,-9.7 -2.1,-18.9 -1.9,-27.6 5.5,18.8 7.7,38.3 7.4,56.1 l 0.2,0.2 c 3.9,5.9 7.5,12 10.8,18.3 -1,-9.8 -1.7,-19.6 -2,-29.2 -0.3,-10.3 0,-20 1.3,-29.1 3.4,19.9 3.4,40.8 1.1,59 0.1,0.2 0.2,0.3 0.2,0.5 3.3,6.4 6.2,13 8.8,19.8 0.2,-10.1 0.6,-20.1 1.5,-29.9 0.9,-10.8 2.4,-20.9 4.7,-30.2 1.2,20.9 -1.3,42.8 -5.8,61.2 0.1,0.2 0.2,0.5 0.3,0.7 2.6,6.9 4.8,14 6.6,21.2 1.4,-10.2 3,-20.3 5,-30.3 2.3,-11.1 5,-21.5 8.4,-31 -1.2,21.7 -6.4,44.2 -13.1,62.7 0.1,0.3 0.1,0.6 0.2,0.9 1.8,7.5 3.2,15 4.1,22.7 2.7,-10.3 5.5,-20.5 8.7,-30.4 3.7,-11.3 7.7,-21.7 12.4,-31.2 -3.8,22.5 -10.9,44.5 -20.9,63.4 0,0.3 0.1,0.6 0.1,1 0.9,8 1.3,16.1 1.3,24.2 4.1,-10.3 8.3,-20.5 12.8,-30.5 5.2,-11.2 10.6,-21.5 16.6,-30.7 -6.6,23.1 -16.3,44.6 -29.4,63.1 0,0.3 0,0.7 0,1 -0.2,8.5 -0.9,17.1 -2.1,25.7 5.6,-10.3 11.4,-20.4 17.4,-30.3 6.7,-10.9 13.6,-20.9 20.9,-29.6 -10,24 -23.3,44.8 -38.5,61.5 0,0.3 -0.1,0.6 -0.1,0.9 -1.4,9 -3.4,17.9 -6,26.7 7.4,-10 14.9,-20 22.6,-29.5 8.3,-10.3 16.6,-19.6 25.2,-27.7 -13.4,23.8 -30,43.4 -48.2,58.4 -0.1,0.2 -0.1,0.4 -0.2,0.7 -2.8,9.1 -6.2,18.1 -10.2,26.9 9.4,-9.4 18.6,-18.6 28.1,-27.5 10,-9.4 19.7,-17.8 29.6,-24.9 -17,22.8 -37,40.7 -58.1,53.3 -0.1,0.2 -0.1,0.3 -0.2,0.5 -4.3,9.2 -9.3,17.9 -14.8,26.1 11.3,-8.2 22.4,-16.3 33.5,-24.1 11.6,-8.1 22.9,-15.3 34,-21.2 -20.7,21.1 -44.1,36.4 -67.9,45.9 -0.1,0.1 -0.2,0.2 -0.2,0.3 -6,8.7 -12.5,16.7 -19.7,24.1 13.2,-6.5 26,-13 38.8,-19.2 13.3,-6.4 25.9,-12.1 38.2,-16.4 -24.4,18.5 -51,30.5 -77.5,36.2 -0.1,0.1 -0.2,0.2 -0.3,0.3 -7.5,7.7 -15.6,14.6 -24.2,20.8 14.8,-4.2 29,-8.6 43,-12.7 14.9,-4.4 28.8,-8.1 42.2,-10.6 -28,15 -57.5,22.8 -86.1,24 -0.2,0.1 -0.3,0.3 -0.5,0.4 -8.9,6.3 -18.3,11.7 -28.1,16.3 15.8,-1.5 30.9,-3.4 45.7,-5.1 16.4,-1.9 31.5,-3.4 45.8,-3.7 -31.1,10.6 -63.1,13.5 -93.4,9.7 -0.4,0.2 -0.8,0.3 -1.1,0.5 -10.2,4.6 -20.8,8.2 -31.7,11 16.3,1.3 31.8,2.1 46.8,3 17.8,1 33.9,2.1 48.9,4.2 -33.9,5.3 -67.9,2.7 -99.3,-6.3 -0.7,0.2 -1.4,0.3 -2.1,0.5 -10.9,2.4 -22.2,3.9 -33.7,4.5 v -0.9 c -0.4,-1 -0.7,-1.9 -1.2,-2.7"
       id="path105" /><path
       class="st34"
       d="m 500,585.1 v 0 h 16.3 c 2.5,-3.7 4.6,-7.6 6.3,-11.7 l 1.1,-2.7 v -0.1 H 500 476.2 l 1.2,2.8 c 1.4,3.2 2.9,6.3 4.7,9.2 0.5,0.8 1,1.6 1.6,2.5 z"
       id="path106" /><path
       class="st35"
       d="m 512,620.7 h 2.5 c 2,0 3.7,-1.2 4.3,-3 0.2,-0.5 0.3,-1 0.3,-1.6 v -2.7 -3.1 -0.8 -0.9 c 0,-0.9 -0.3,-1.8 -0.8,-2.6 -0.5,-0.7 -1.2,-1.3 -2,-1.6 l 11.3,-13 c 3.3,-3.8 3.7,-9.2 1,-13.4 l -4.8,-7.4 v 0.1 l -1.1,2.7 c -1.7,4.1 -3.8,8 -6.3,11.7 -2.2,3.2 -4.7,6.3 -7.4,9.1 l -8.9,9.3 -8.9,-9.3 c -2.7,-2.8 -5.2,-5.9 -7.4,-9.1 -0.5,-0.8 -1.1,-1.6 -1.6,-2.5 -1.8,-2.9 -3.4,-6 -4.7,-9.2 l -1.2,-2.8 -4.8,7.4 c -2.7,4.2 -2.3,9.6 0.9,13.4 l 11.3,13 c -0.8,0.3 -1.5,0.9 -2,1.6 -0.5,0.7 -0.8,1.6 -0.8,2.6 v 0.9 0.8 3.1 2.7 c 0,0.6 0.1,1.1 0.3,1.6 0.6,1.7 2.3,3 4.3,3 h 2.5 c -7.2,7.7 -16.8,14.6 -29.3,20.6 h 17.7 c 0,0 13.5,-11 18.3,-20.6 v 0 h 5.2 5.3 v 0 c 4.8,9.6 18.3,20.6 18.3,20.6 h 17.7 c -12.4,-5.9 -22,-12.9 -29.2,-20.6"
       id="path107" /><path
       class="st34"
       d="m 476.4,641.3 -0.1,17.4 c 10.9,-12.1 19.5,-24.4 23.6,-37 4.1,12.6 12.7,24.9 23.6,37 l -0.1,-17.4 c 0,0 -13.5,-11 -18.3,-20.6 h -10.5 c -4.6,9.6 -18.2,20.6 -18.2,20.6"
       id="path108" /><path
       class="st4"
       d="m 500,469.3 c -32.9,0 -59.6,-26.7 -59.6,-59.6 0,-32.9 26.7,-59.6 59.6,-59.6 32.9,0 59.6,26.7 59.6,59.6 0,32.9 -26.7,59.6 -59.6,59.6"
       id="path109" /><path
       class="st33"
       d="m 544.3,413.8 c -12.9,0 -30.8,9.2 -41,15.1 -2,-21.8 -1.2,-36 -0.6,-43.3 0,0 0.1,-0.1 0.1,0 6.3,4 4.6,15.1 3.2,18.3 0,0.1 0.1,0.1 0.1,0.1 7.9,-9.3 4,-19.6 2.4,-22.2 0,-0.1 0,-0.1 0.1,-0.1 7,5.7 6.7,13.3 6.4,17.6 0,0.1 0.1,0.1 0.1,0 2.9,-5.6 3.7,-15.2 -5.1,-22.9 0,0 0,-0.1 0.1,-0.1 7.9,1.9 11,9.4 11.1,14.9 0,0.1 0,0.2 0,0.3 0,0.1 0.1,0.1 0.1,0 1.1,-4.2 0.9,-7.5 -0.1,-10.2 -2.3,-6.1 -8.4,-8.8 -11.4,-9.2 -0.1,0 -0.1,-0.1 0,-0.1 5.9,-0.6 9.4,1.3 11.4,3.1 1.3,1.1 1.9,2.2 2.2,2.7 0,0.1 0.1,0 0.1,0 -0.2,-2.2 -1.1,-4.1 -2.3,-5.6 -3.5,-4.1 -10,-5.3 -14.2,-3.5 -0.1,0 -0.1,0 -0.1,-0.1 2.7,-3.9 8.2,-4.3 11,-3 0.1,0 0.1,0 0.1,-0.1 -4.3,-4.9 -11.4,-5.1 -15.2,0.7 0,0 -0.1,0 -0.1,0 -0.4,-0.7 -0.8,-1.5 -1.2,-2.4 -0.8,-1.9 -1.2,-4 -1.5,-6.1 0,-0.1 -0.1,-0.1 -0.2,0 -0.3,2.1 -0.7,4.2 -1.5,6.1 -0.4,0.9 -0.8,1.7 -1.2,2.4 0,0 -0.1,0 -0.1,0 -3.8,-5.7 -10.9,-5.6 -15.2,-0.7 0,0.1 0,0.1 0.1,0.1 2.9,-1.4 8.3,-1 11,3 0,0.1 0,0.1 -0.1,0.1 -4.2,-1.8 -10.7,-0.6 -14.2,3.5 -1.3,1.5 -2.1,3.3 -2.3,5.6 0,0.1 0.1,0.1 0.1,0 0.3,-0.5 0.9,-1.6 2.2,-2.7 2,-1.8 5.6,-3.6 11.4,-3.1 0.1,0 0.1,0.1 0,0.1 -3,0.4 -9.2,3.1 -11.4,9.2 -1,2.7 -1.3,6.1 -0.1,10.2 0,0.1 0.1,0.1 0.1,0 0,-0.1 0,-0.2 0,-0.3 0.1,-5.6 3.2,-13 11.1,-14.9 0.1,0 0.1,0.1 0.1,0.1 -8.8,7.7 -7.9,17.3 -5.1,22.9 0,0.1 0.1,0 0.1,0 -0.4,-4.3 -0.7,-11.9 6.4,-17.6 0.1,0 0.1,0 0.1,0.1 -1.6,2.6 -5.5,12.9 2.4,22.2 0,0.1 0.1,0 0.1,-0.1 -1.3,-3.2 -3.1,-14.3 3.2,-18.3 0,0 0.1,0 0.1,0 0.6,7.3 1.4,21.5 -0.6,43.3 -10.2,-5.9 -28.1,-15.1 -41,-15.1 -4,0 -7.3,1.1 -7.1,1.1 17.1,2.7 37.4,14.6 45.9,20 -5.5,3.4 -23.1,14.3 -25.2,14.3 -1.1,0 -1.2,-1.3 -2.1,-1.3 -0.8,0 -0.8,0.6 -0.8,0.8 0,3.7 5,6 9,4.1 l 24.7,-14.3 24.7,14.3 c 4,1.9 9,-0.5 9,-4.1 0,-0.2 0,-0.8 -0.8,-0.8 -0.9,0 -1,1.3 -2.1,1.3 -2.1,0 -19.7,-10.9 -25.2,-14.3 8.5,-5.4 28.8,-17.3 45.9,-20 0.2,0 -3.1,-1.1 -7.1,-1.1"
       id="path110" /><path
       class="st28"
       d="m 598.7,400.9 c 8.2,0.7 25.7,2.8 31,-1 0.3,1.5 -5.4,7 -22.8,5.9 -14.7,-1 -33.7,-3.1 -39,1.3 1.4,-6.3 13.8,-7.6 30.8,-6.2"
       id="path111" /><polygon
       class="st28"
       points="331.4,464.2 333.3,467.9 379.1,444.6 "
       id="polygon111" /><polygon
       class="st28"
       points="657.2,449.7 659.8,445.4 656.8,445.5 654.4,445.5 648.2,445.7 "
       id="polygon112" /><path
       class="st28"
       d="M 646.8,470.1 604,447.9 c -5.9,-3.1 -12.6,-4.2 -19.1,-3.3 l 20,28.2 31.5,17.8 -2,4.7 15.4,7.9 4.6,-9.1 c 4.5,-8.8 1.1,-19.4 -7.6,-24"
       id="path112" /><path
       class="st28"
       d="m 553.4,507.5 c -0.8,-1.6 -1.7,-3.3 -2.8,-4.9 2,15.9 -8.3,23.1 -8.3,23.1 L 549,521 c 0,0 -5.5,15.3 -17.8,21.2 -12.3,5.8 -31.2,12.1 -31.2,14 0,-2 -19.9,-8.2 -32.2,-14 C 455.5,536.4 450,521 450,521 l 10.1,4.2 3.6,-0.7 c -3.9,-2.5 -7.8,-6.5 -10.6,-11.3 -0.6,-1.1 -1.2,-2.2 -1.7,-3.4 -1,-2.4 -1.7,-4.8 -2,-7.4 -1.1,1.6 -2,3.3 -2.8,4.9 -0.4,0.7 -0.7,1.4 -1,2.1 -3.2,7.9 -2.8,16 1.2,24.3 l 2.8,-3.8 c 7.5,21.9 39,20.8 50.4,36.5 11.4,-15.7 42.9,-14.6 50.4,-36.5 l 2.8,3.8 c 4,-8.3 4.4,-16.4 1.2,-24.3 -0.4,-0.5 -0.7,-1.2 -1,-1.9"
       id="path113" /><path
       class="st28"
       d="m 401.3,400.9 c -8.2,0.7 -25.7,2.8 -31,-1 -0.3,1.5 5.4,7 22.8,5.9 14.7,-1 33.7,-3.1 39,1.3 -1.4,-6.3 -13.8,-7.6 -30.8,-6.2"
       id="path114" /><path
       class="st28"
       d="m 420,394.9 c -0.5,-0.9 -1.1,-1.7 -1.8,-2.4 -8.6,-9.6 -25,-4.2 -42.3,-2.6 -14.9,1.4 -20.8,0.2 -25.5,-1.9 -3.2,-1.4 -3.4,-1.9 -3.4,-1.9 0,0 0,0.1 0,0.3 0,2.2 7.5,10.2 29.4,8.6 19.7,-1.5 35.3,-5.2 43.6,-0.1"
       id="path115" /><path
       class="st28"
       d="m 580,394.9 c 0.5,-0.9 1.1,-1.7 1.8,-2.4 8.6,-9.6 25,-4.2 42.3,-2.6 14.9,1.4 20.8,0.2 25.5,-1.9 3.1,-1.4 3.4,-1.9 3.4,-1.9 0,0 0,0.1 0,0.3 0,2.2 -7.5,10.2 -29.4,8.6 -19.7,-1.5 -35.3,-5.2 -43.6,-0.1"
       id="path116" /><polygon
       class="st36"
       points="627.8,485.6 636.4,490.5 634.5,495.2 "
       id="polygon116" /><path
       class="st36"
       d="m 428.8,352.8 -19,-9.7 -2.1,-1.1 -3.9,-2 0.5,-1 c 0.7,-1.3 0.2,-2.9 -1.1,-3.6 l -11.9,-6.6 -2.1,-1.1 -18.5,-10.3 -2.1,-1.1 -12.7,-7.1 -2.7,5.3 -5,9.8 -2.6,5.1 3.1,1.6 0.2,-0.3 c 2.6,-5.2 9,-7.2 14.2,-4.6 l 38,19.2 -2.4,4.8 2,-0.5 c 3.8,-0.9 7.9,-0.4 11.4,1.4 l 37.1,18.7 c 1.4,-1.4 2.9,-2.8 4.4,-4.1 L 431,353.9 Z"
       id="path117" /><path
       class="st36"
       d="m 542.3,525.7 c 0,0 10.2,-7.2 8.3,-23.1 -0.3,2.5 -1,5 -2,7.4 -0.5,1.2 -1.1,2.3 -1.7,3.4 -2.8,4.8 -6.7,8.8 -10.6,11.3 z"
       id="path118" /><path
       class="st36"
       d="m 519.9,540.4 c -1.2,0 -2.4,-0.1 -3.6,-0.4 v 0 c -6.2,1.9 -10.2,3.7 -12.8,5.1 -2.5,1.4 -3.5,2.3 -3.5,2.3 v -76.9 c -2.2,0 -4.4,-0.1 -6.5,-0.3 v 55.9 c 0,4.5 -1.2,8 -3.6,10.5 -7.6,7.8 -25.9,2.6 -32.2,-11 l 2.4,-0.4 L 450,521 c 0,0 5.5,15.3 17.8,21.2 12.3,5.8 32.2,12 32.2,14 0,-2 18.8,-8.2 31.2,-14 C 543.5,536.4 549,521 549,521 l -6.7,4.7 c -4.3,9.3 -14.2,14.7 -22.4,14.7"
       id="path119" /><path
       class="st36"
       d="m 507.5,270 c -1.5,0.4 -3.8,0.7 -7.5,1 -2.3,0.1 -4.7,0.2 -8.1,0.2 0,0 0,0.5 0.7,1.2 0.2,0.2 0.5,0.4 0.9,0.7 0.9,0.6 2.4,1.1 4.6,1.5 0.6,0.1 1.2,0.2 1.9,0.2 v 74.3 c -6.2,0 -12.2,0.7 -17.9,2.2 v -26.9 c 0,-1.7 0.9,-3.2 2.5,-4 l 3.8,-2.2 c -1.9,1.1 -2.9,3.2 -2.5,5.3 l 0.2,1.3 4,23.1 v -72.4 c 0,-6.9 1.3,-13.8 3.7,-20.2 l 6.2,-17.4 v 26 c 0,3.5 0.4,5.7 7.5,6.1"
       id="path120" /><polygon
       class="st36"
       points="388.4,463 342.7,486.3 344.6,490 "
       id="polygon120" /><path
       class="st36"
       d="m 390.1,410.4 c 0,0 0,-0.1 0,0 0,-0.1 0,0 0,0"
       id="path121" /><path
       class="st36"
       d="m 432.1,407.1 v 0 c -8.4,-2.4 -36.6,4 -42,3.2 0,0 0,0 0.1,0.1 1.3,1.5 11.4,1.4 23.3,1 3.8,-0.1 11.7,-0.9 19.1,1.7 -0.4,-5 -0.5,-6 -0.5,-6"
       id="path122" /><path
       class="st36"
       d="m 432,406.6 c -1,-3.7 -4.5,-8.3 -10.6,-11.1 -0.5,-0.2 -1,-0.4 -1.4,-0.6 -6.7,-2.4 -13.4,0.5 -35.4,4.2 -4.1,0.7 -12.3,1.2 -14.2,0.8 0,0 0,0 0,0 5.3,3.8 22.8,1.7 31,1 17.1,-1.4 29.4,-0.1 30.8,6.2 -0.1,-0.2 -0.2,-0.3 -0.2,-0.5"
       id="path123" /><path
       class="st36"
       d="m 419.4,393.9 c -3.2,-5 -6.4,-8 -9.8,-9.7 -7.1,-3.6 -15.1,-1.5 -26.8,-0.1 -15.7,1.9 -30.3,4 -35.9,1.9 0,0 0.2,0.5 3.4,1.9 4.8,2.1 10.6,3.3 25.5,1.9 17.2,-1.6 33.7,-7 42.3,2.6 0.6,0.7 1.2,1.5 1.8,2.4 -0.1,-0.2 -0.3,-0.6 -0.5,-0.9"
       id="path124" /><path
       class="st36"
       d="m 609.9,410.4 c 0,0 0,-0.1 0,0 0,-0.1 0,0 0,0"
       id="path125" /><path
       class="st36"
       d="m 567.9,407.1 v 0 c 8.4,-2.4 36.6,4 42,3.2 0,0 0,0 -0.1,0.1 -1.2,1.5 -11.4,1.4 -23.3,1 -3.8,-0.1 -11.7,-0.9 -19.1,1.7 0.4,-5 0.5,-6 0.5,-6"
       id="path126" /><path
       class="st36"
       d="m 568,406.6 c 1,-3.7 4.5,-8.3 10.6,-11.1 0.5,-0.2 0.9,-0.4 1.4,-0.6 6.7,-2.4 13.4,0.5 35.4,4.2 4.1,0.7 12.3,1.2 14.2,0.8 0,0 0,0 0,0 -5.3,3.8 -22.8,1.7 -31,1 -17.1,-1.4 -29.4,-0.1 -30.8,6.2 0.1,-0.2 0.2,-0.3 0.2,-0.5"
       id="path127" /><path
       class="st36"
       d="m 580.6,393.9 c 3.2,-5 6.4,-8 9.8,-9.7 7.1,-3.6 15.2,-1.5 26.8,-0.1 15.7,1.9 30.3,4 35.9,1.9 0,0 -0.2,0.5 -3.4,1.9 -4.8,2.1 -10.6,3.3 -25.5,1.9 -17.3,-1.6 -33.7,-7 -42.3,2.6 -0.6,0.7 -1.2,1.5 -1.8,2.4 0.1,-0.2 0.3,-0.6 0.5,-0.9"
       id="path128" /><path
       class="st36"
       d="m 411.2,428.2 -4.3,2.2 -16.2,2.4 -2.3,0.4 -20.8,3.1 -2.3,0.3 -2,0.3 c -6.2,1.3 -12.4,3 -18.3,5.1 -0.7,0.3 -1.5,0.5 -2.2,0.8 -4.7,1.8 -9.2,3.8 -13.7,6 l -4.2,2.1 6.7,13.1 47.6,-19.6 -45.7,23.3 -4.7,2.4 -2.1,1.1 c 2.4,6.5 5.1,12.9 8.1,19.1 -2.7,-5.2 -0.6,-11.6 4.6,-14.3 l 96.1,-48.9 c -1,-3.1 -1.8,-6.2 -2.3,-9.5 -0.3,-0.1 -0.6,-0.2 -0.9,-0.3 l -18.8,9.6 z"
       id="path129" /><path
       class="st36"
       d="m 416.3,373.8 c -5.8,-3.5 -12.3,-5.3 -20.2,-6.1 -0.8,-0.1 -1.6,-0.1 -2.4,-0.2 -1.8,-0.1 -3.6,-0.2 -5.5,-0.2 -5.2,0 -10.9,0.3 -17,0.7 -0.8,0.1 -1.7,0.1 -2.5,0.2 -3.2,0.2 -6.3,0.4 -9.4,0.6 -4.3,0.2 -8.6,0.4 -12.8,0.5 -0.8,0 -1.6,0 -2.4,0 h -2.7 c -5.2,-0.1 -13.3,-1.5 -18.2,-3 -0.9,-0.3 -1.6,-0.5 -2.2,-0.8 -0.5,-0.2 -0.9,-0.4 -1.2,-0.6 -0.1,-0.1 -0.2,-0.1 -0.3,-0.2 0,0 0.5,0.5 0.5,0.5 13.7,14.5 34,9.4 73.2,7.9 18.4,-0.7 30.5,12.9 39.9,28.5 0.4,-2.5 0.9,-4.9 1.6,-7.3 -2.3,-4.4 -5.4,-9.8 -9,-13.2 -2.3,-2.3 -4.7,-4.3 -7.2,-6 -0.8,-0.4 -1.5,-0.9 -2.2,-1.3"
       id="path130" /><path
       class="st36"
       d="m 581.7,375.1 c 0.7,-0.5 1.3,-0.9 2,-1.3 v 0 c 5.8,-3.5 12.3,-5.4 20.2,-6.1 0.8,-0.1 1.6,-0.1 2.4,-0.2 1.8,-0.1 3.6,-0.2 5.5,-0.2 5.2,0 10.9,0.3 17,0.7 0.8,0.1 1.7,0.1 2.5,0.2 3.2,0.2 6.3,0.4 9.4,0.6 4.3,0.2 8.6,0.5 12.8,0.5 0.8,0 1.6,0 2.4,0 h 2.7 c 5.2,-0.1 13.3,-1.5 18.2,-3 0.8,-0.2 1.6,-0.5 2.2,-0.7 0.5,-0.2 0.9,-0.4 1.2,-0.6 0.1,-0.1 0.2,-0.1 0.3,-0.2 0,0 -0.5,0.5 -0.5,0.5 -13.7,14.5 -34,9.4 -73.2,7.8 -18.4,-0.7 -30.5,12.9 -39.9,28.5 -0.4,-2.5 -0.9,-4.9 -1.6,-7.3 v 0 c 2.3,-4.4 5.5,-9.8 9.1,-13.2 2.4,-2.3 4.8,-4.3 7.3,-6"
       id="path131" /><path
       class="st30"
       d="m 500,170.3 c -0.2,1.2 -0.9,2.3 -1.9,3.4 -1,1.2 -2.3,2.5 -3.7,4.2 -7.1,3.5 -27.2,1.2 -27.2,21.2 0,5.5 2.6,10.7 7.1,14 -5.6,-1.2 -10.8,-5.3 -12.6,-13.7 -1.7,-8.9 3.4,-17.2 11.4,-21.2 7.6,-3.8 22.7,-3.5 26.2,-8.1 0.2,0.2 0.5,0.2 0.7,0.2"
       id="path132" /><path
       class="st30"
       d="m 460.2,185.5 c -0.4,0.7 -0.8,1.4 -1.2,2.2 -0.1,0.1 -0.1,0.2 -0.2,0.4 -6.5,1.6 -11.4,7.4 -11.4,14.4 0,2.8 0.6,5.5 2.1,7.6 0.3,0.4 0.6,0.8 0.9,1.1 v 0 c 0,0 0,0 0,0 -6.9,-6.8 -6.3,-15.3 -1.4,-22.4 2.8,-4.1 7,-7.7 12,-10.2 0,0 0.1,0 0.1,-0.1 -0.6,2 -1.1,4.6 -0.9,7"
       id="path133" /><path
       class="st30"
       d="m 455.1,218.2 v 0 0 c 6.1,3.6 11.2,10.9 12,18.2 10.8,-4.2 18.4,-6.7 32.9,-6.7 -15,-14.2 -36.9,-11.3 -44.9,-11.5"
       id="path134" /><path
       class="st30"
       d="m 500,170.3 c 0.2,1.2 0.9,2.3 1.9,3.4 1,1.2 2.3,2.5 3.7,4.2 7.1,3.5 27.2,1.2 27.2,21.2 0,5.5 -2.6,10.7 -7,14 5.6,-1.2 10.8,-5.3 12.6,-13.7 1.7,-8.9 -3.4,-17.2 -11.4,-21.2 -7.6,-3.8 -22.7,-3.5 -26.2,-8.1 -0.3,0.2 -0.6,0.2 -0.8,0.2"
       id="path135" /><path
       class="st30"
       d="m 539.8,185.5 c 0.4,0.7 0.8,1.4 1.2,2.2 0.1,0.1 0.1,0.2 0.2,0.4 6.5,1.6 11.4,7.4 11.4,14.4 0,2.8 -0.6,5.5 -2.1,7.6 -0.3,0.4 -0.6,0.8 -0.9,1.1 v 0 0 c 6.9,-6.8 6.3,-15.3 1.4,-22.4 -2.8,-4.1 -7,-7.7 -12,-10.2 0,0 -0.1,0 -0.1,-0.1 0.6,2 1.1,4.6 0.9,7"
       id="path136" /><path
       class="st29"
       d="m 538.9,178.5 c 0,0 0,0.1 0,0 5.1,2.6 9.3,6.2 12.1,10.3 4.9,7.1 5.5,15.6 -1.4,22.4 v 0 0 c -5.3,5.9 -13.1,4.7 -15.4,4.2 -0.4,-0.1 -0.6,-0.1 -0.6,-0.2 1.5,0.3 10.1,-5.8 10.1,-16 0,-2.1 -0.3,-4.2 -0.7,-6.2 -0.4,-1.9 -1,-3.7 -1.6,-5 -0.1,-0.1 -0.1,-0.3 -0.2,-0.4 -0.4,-0.8 -0.8,-1.5 -1.2,-2.2 -9.9,-17.2 -25.5,-11.7 -33.7,-18.6 -0.6,0.9 -1.3,1.7 -2.3,2.3 -0.9,0.6 -2,1 -3.1,1.1 3.4,4.6 18.5,4.3 26.2,8.1 8,4 13.1,12.3 11.4,21.2 -1.8,8.3 -7,12.4 -12.6,13.7 -4.2,-0.9 -7.1,-1.6 -10.1,-2.2 -3.1,-0.5 -6.3,-0.9 -11,-1 1.3,-2.8 2.3,-5.7 3,-8.9 0.6,-2.7 0.9,-5.4 0.9,-8.3 0,-5 -0.9,-10 -2.9,-14.9 v 0 c -2.7,-3.4 -5.2,-5.3 -5.5,-7.7 -0.4,2.4 -2.8,4.2 -5.5,7.7 v 0 c -2,4.9 -2.9,9.9 -2.9,14.9 0,2.9 0.3,5.6 0.9,8.3 0.6,3.1 1.7,6.1 3,8.9 -4.7,0.1 -7.9,0.5 -11,1 -3.1,0.5 -6,1.3 -10.1,2.2 -5.6,-1.2 -10.8,-5.3 -12.6,-13.7 -1.7,-8.9 3.4,-17.2 11.4,-21.2 7.6,-3.8 22.7,-3.5 26.2,-8.1 -1.1,-0.1 -2.2,-0.5 -3.1,-1.1 -0.9,-0.6 -1.7,-1.4 -2.2,-2.3 -8.3,6.9 -23.8,1.4 -33.8,18.6 -0.4,0.7 -0.8,1.4 -1.2,2.2 -0.1,0.1 -0.1,0.2 -0.2,0.4 -0.6,1.4 -1.2,3.1 -1.6,5 -0.4,1.9 -0.7,4 -0.7,6.2 0,10.2 8.6,16.3 10.1,16 -0.1,0 -0.2,0.1 -0.6,0.2 -2.3,0.5 -10.1,1.7 -15.4,-4.2 v 0 0 c 0,0 0,0 0,0 -6.9,-6.8 -6.3,-15.3 -1.5,-22.4 2.8,-4.1 7,-7.7 12,-10.2 0,0 0.1,0 0.1,-0.1 -12.7,0.6 -22.8,11 -22.8,23.9 0,4 1,7.7 2.7,11.1 0.3,0.6 0.6,1.1 1,1.7 0.4,0 0.9,-0.1 1.3,-0.1 4.3,0 8.3,1.1 11.8,3.1 0,0 0,0 0,0 8,0.2 29.9,-2.7 44.9,11.4 v 0 c 15,-14.2 36.9,-11.2 44.9,-11.4 v 0 c 3.5,-2 7.5,-3.1 11.8,-3.1 0.4,0 0.9,0 1.3,0.1 0.3,-0.5 0.7,-1.1 1,-1.7 1.7,-3.3 2.7,-7.1 2.7,-11.1 -0.5,-12.8 -10.6,-23.3 -23.3,-23.9"
       id="path137" /><path
       class="st30"
       d="m 544.9,218.2 v 0 0 c -6.1,3.6 -11.2,10.9 -12,18.2 -10.8,-4.2 -18.4,-6.7 -32.9,-6.7 15,-14.2 36.9,-11.3 44.9,-11.5"
       id="path138" /><path
       class="st28"
       d="m 680.6,364.8 c 0,0 -0.5,0.5 -0.5,0.5 -13.7,14.5 -34,9.4 -73.2,7.8 -18.4,-0.7 -30.5,12.9 -39.9,28.5 -0.4,-2.5 -0.9,-4.9 -1.6,-7.3 -0.7,-2.5 -1.6,-5 -2.7,-7.3 -0.4,-0.9 -0.8,-1.8 -1.3,-2.7 0,0 0,-0.1 0,-0.1 0,0 -0.1,-0.1 -0.1,-0.2 -1.3,-2.4 -2.7,-4.8 -4.3,-7 l 58.8,-30 c 11.6,-5.9 20.6,-14.8 26.7,-25.2 0,0 -17.1,8.6 -17.1,8.6 -0.3,0.2 -8,4.4 -5.9,10.5 0,0 -6.6,-7.5 -7.2,-12.7 0,0 -0.3,4.3 0.2,8.8 L 550,368.8 c -2.8,-2.7 -5.7,-5.1 -8.6,-7 l 20.3,-26.1 -1,-8.2 45.9,-59.2 -2,-1.6 -42.4,54.6 -27.9,36.2 c -0.1,0 -0.2,-0.1 -0.2,-0.1 v 0 c -5.9,-3.1 -10.8,-4.9 -16.2,-6.3 v -26.9 c 0,-1.7 -0.9,-3.2 -2.4,-4 0,0 -5.5,-3.1 -5.5,-3.1 v -41.7 c 0,-6.9 -1.3,-13.8 -3.7,-20.2 l -6.2,-17.4 v 26 c 0,3.8 0.4,6 7.5,6.3 -1.5,0.4 -3.8,0.7 -7.5,1 -2.3,0.1 -4.7,0.2 -8.1,0.2 0,0 0.1,0.9 1.6,1.8 0.9,0.6 2.3,1.1 4.6,1.5 0.6,0.1 1.2,0.2 1.9,0.2 v 74.3 c -6.2,0 -12.2,0.7 -17.9,2.2 -5.7,1.4 -12.5,4.1 -16.5,6.3 l -70.2,-90.7 -2,1.5 45.9,59.2 -1,8.2 20.3,26.1 c -1.8,1.2 -3.4,2.5 -4.9,3.7 -1.5,1.3 -3,2.7 -4.4,4.1 L 412.3,351 c -3.5,-1.8 -7.5,-2.3 -11.4,-1.4 l -2,0.5 2.4,-4.8 -38,-19.2 c -5.2,-2.6 -11.5,-0.5 -14.2,4.6 l -0.2,0.3 45.9,21.1 c 1.3,0.6 2.9,0.1 3.5,-1.2 l 0.5,-1 46.8,23.7 c -4.9,6.1 -8.7,13.1 -10.9,20.7 -0.7,2.4 -1.2,4.8 -1.6,7.3 -9.4,-15.6 -21.5,-29.2 -39.9,-28.5 -39.2,1.5 -59.5,6.7 -73.2,-7.8 l -0.5,-0.5 c -0.4,1.1 0.9,3.3 1.6,4.3 3.6,5.3 13.6,13.1 35.1,11.9 21.9,-1.2 33.2,-2 40.6,-1.3 5,0.5 9.2,2.1 12.9,4.5 3.4,1.7 6.5,4.7 9.8,9.7 0.2,0.3 0.4,0.6 0.6,0.9 0.5,0.2 0.9,0.4 1.4,0.6 6.1,2.9 9.6,7.4 10.6,11.1 0,0.2 0.1,0.3 0.1,0.5 0,0 0.1,1 0.4,6 -7.4,-2.6 -15.3,-1.8 -19.1,-1.7 -11.9,0.4 -22.1,0.5 -23.4,-1 0,0 0,0 0,0 1.2,1.6 8.2,4.8 20.1,4.6 5.4,-0.1 14.1,0 21.9,2.5 0.3,0.1 0.6,0.2 0.9,0.3 0.5,3.3 1.2,6.4 2.3,9.5 l -96.1,48.9 c -5.2,2.7 -7.3,9 -4.6,14.2 l 8.1,-4.1 45.8,-23.3 -43.9,27 6.7,13.1 4.2,-2.1 c 10.8,-5.5 20.9,-12.5 29.8,-20.6 l 31.4,-31.7 22.9,-11.7 c 0,0 0,0 0,0 1.8,3.2 3.9,6.2 6.2,9.1 7.6,9.2 18,16.4 30,20.5 5.6,1.9 11.6,3.2 17.8,3.8 2.1,0.2 4.3,0.3 6.5,0.3 v 76.9 c 0,0 4,-3.7 16.3,-7.5 -2.4,-0.5 -4.6,-1.6 -6.2,-3.3 -2.4,-2.5 -3.6,-6 -3.6,-10.5 v -55.9 c 6.2,-0.5 12.1,-1.8 17.7,-3.8 11.9,-4.1 22.4,-11.3 30,-20.5 1.9,-2.3 3.6,-4.6 5.1,-7.1 v 0 c 1.1,-1.9 2.2,-3.8 3.1,-5.8 v 0 l 22.5,11.4 c -2.9,-5.9 -7.5,-10.4 -12.9,-13.1 l -7.5,-3.8 c 1.2,-3.6 2,-6.7 2.4,-9.9 0.3,-0.1 0.6,-0.2 0.9,-0.3 7.9,-2.5 16.6,-2.6 21.9,-2.5 11.9,0.3 18.9,-3 20.1,-4.6 v 0 c -1.3,1.5 -11.4,1.4 -23.3,1 -3.8,-0.1 -11.7,-0.9 -19.1,1.7 0.3,-5 0.4,-5.9 0.5,-6 0,-0.2 0.1,-0.3 0.1,-0.5 1,-3.7 4.5,-8.3 10.6,-11.1 0.5,-0.2 0.9,-0.4 1.4,-0.6 0.2,-0.3 0.4,-0.6 0.6,-0.9 3.2,-5 6.4,-8 9.8,-9.7 3.7,-2.5 7.9,-4.1 12.9,-4.5 7.3,-0.7 18.6,0.1 40.6,1.3 21.6,1.2 31.6,-6.6 35.1,-11.9 0.6,-1.1 1.8,-3.3 1.5,-4.3 M 500,469.3 c -32.9,0 -59.6,-26.7 -59.6,-59.6 0,-32.9 26.7,-59.6 59.6,-59.6 32.9,0 59.6,26.7 59.6,59.6 0,32.9 -26.7,59.6 -59.6,59.6"
       id="path139" /><path
       class="st36"
       d="m 659.8,445.4 -2.7,4.3 -9,-4.1 -14,-6.4 -2.2,-1 -7.8,-3.6 -13,0.1 h -2.4 l -18.6,0.2 -2.8,-1.4 -2.1,-1.1 -19.3,-9.9 c -0.4,1.6 -0.9,3.3 -1.5,5 l 7.5,3.7 c 5.3,2.7 10,7.1 12.8,13 0,0.1 0.1,0.1 0.1,0.1 6.6,-0.9 13.3,0.2 19.1,3.3 l 42.8,22.3 c 8.7,4.5 12.1,15.2 7.6,23.9 l -4.6,9 25.4,-49.8 z"
       id="path140" /><path
       class="st36"
       d="m 534.1,357.3 v 0 c 0.1,0 0.2,0.1 0.3,0.1 l 27.9,-36.2 42.4,-54.6 -3.6,-2.8 -35.4,45.4 -29.7,38.5 25.5,-41.8 -26.2,32 -4,0.1 -11.2,13.6 c 4.5,1.3 8.9,3 14,5.7"
       id="path141" /><path
       class="st28"
       d="m 561.5,305.9 v 0 l -3.5,-2.8 c -0.9,-0.6 -1.6,-1.2 -2.5,-1.4 -2.6,-0.6 -4.7,1.4 -5.2,6.2 l -2.1,14.2 z"
       id="path142" /><path
       class="st31"
       d="m 550.3,308 c 0,-0.4 0.1,-0.8 0.2,-1.2 -11.9,-5.9 -24.9,-9.8 -38.6,-11.2 v 2.3 c 13.6,1.4 26.5,5.3 38.2,11.2 z"
       id="path143" /><polygon
       class="st28"
       points="565.6,309.3 561.5,305.9 535.9,347.7 "
       id="polygon143" /><path
       class="st36"
       d="m 395.4,266.7 70.2,90.7 c 3.5,-2 9.1,-4.3 14.3,-5.8 l -11.2,-13.5 -4,-0.1 -26.2,-32 -4.2,3.3 -35.4,-45.4 z"
       id="path144" /><path
       class="st28"
       d="M 451.9,322.2 449.8,308 c -0.6,-4.8 -2.7,-6.8 -5.2,-6.2 -0.9,0.2 -1.6,0.8 -2.5,1.4 l -3.5,2.8 z"
       id="path145" /><path
       class="st31"
       d="m 449.5,306.8 c 0.1,0.4 0.1,0.8 0.2,1.2 l 0.2,1.3 c 11.7,-5.9 24.6,-9.8 38.3,-11.3 v -2.3 c -13.8,1.3 -26.8,5.2 -38.7,11.1"
       id="path146" /><polygon
       class="st28"
       points="464,347.7 438.5,305.9 434.3,309.2 "
       id="polygon146" /><path
       class="st36"
       d="m 588.9,337.6 -2.1,1.1 -19.3,9.8 -2.2,1.1 -23.9,12.2 c 2.9,2 5.8,4.3 8.5,7 l 62.5,-31.7 c -0.5,-4.5 -0.2,-8.8 -0.2,-8.8 0.1,0.6 0.2,1.3 0.5,2 0.5,1.4 1.2,2.9 2.1,4.3 0.4,0.7 0.9,1.4 1.3,2 1.7,2.5 3.4,4.4 3.4,4.4 -0.3,-0.8 -0.4,-1.5 -0.4,-2.2 0,-3.2 2.5,-5.6 4.3,-7 0.6,-0.5 1.1,-0.8 1.5,-1 0.6,-0.3 -1.1,0.5 17.6,-8.9 -2.6,-0.3 -5.2,-0.4 -7.5,-0.3 -0.9,0 -1.8,0 -2.7,0.1 -7.4,0.4 -14.9,2.1 -22,5.2 -0.7,0.3 -1.5,0.6 -2.2,1 -0.5,0.2 -1,0.5 -1.6,0.8 z"
       id="path147" /><path
       class="st31"
       d="m 572.5,463.8 c 4,-5.2 7.4,-10.9 10.1,-16.9 l -2.1,-1.1 c -2.6,5.7 -5.9,11.1 -9.6,16.1 z"
       id="path148" /><path
       class="st31"
       d="m 585.5,484.8 c 4.8,-5.4 9.1,-11.3 12.8,-17.5 l -1.5,-2.1 c -3.7,6.4 -8,12.3 -12.8,17.8 z"
       id="path149" /><path
       class="st31"
       d="m 600.4,503 c 5.9,-6.3 11.3,-13.2 15.9,-20.6 l -2,-1.1 c -4.5,7.1 -9.7,13.8 -15.4,19.9 z"
       id="path150" /><path
       class="st31"
       d="m 615.4,521.2 c 6.8,-7 13,-14.7 18.5,-22.9 l -1.4,-2.1 c -5.5,8.3 -11.7,16 -18.6,23.1 z"
       id="path151" /><path
       class="st31"
       d="m 386.1,519.4 c -6.7,-6.9 -12.8,-14.4 -18.1,-22.5 -0.6,0.4 -1.3,0.8 -2,1.2 v 0 c 5.5,8.3 11.7,16 18.6,23.1 z"
       id="path152" /><path
       class="st31"
       d="m 401.1,501.2 c -5.4,-5.8 -10.3,-12 -14.6,-18.6 0,0 -1.5,1.4 -1.7,1.6 4.4,6.7 9.4,13 14.8,18.8 z"
       id="path153" /><path
       class="st31"
       d="m 416,482.9 c -4.7,-5.4 -9,-11.2 -12.6,-17.4 l -1.7,1.7 c 3.7,6.3 8,12.1 12.8,17.5 z"
       id="path154" /><path
       class="st31"
       d="m 429,461.9 c -2.9,-3.9 -5.6,-8.1 -7.8,-12.5 l -2.1,1.1 c 2.4,4.7 5.2,9.1 8.4,13.3 z"
       id="path155" /><path
       class="st31"
       d="m 410.4,275.6 c -0.5,0.3 -1,0.7 -1.5,1 l 1.4,1.8 c 0.5,-0.4 1,-0.7 1.5,-1 23.5,-15.8 51.5,-25.4 81.6,-26.6 l 0.9,-2.4 c -31,1.1 -59.8,11 -83.9,27.2"
       id="path156" /><path
       class="st31"
       d="m 458.9,330.8 c 0.7,-0.4 1.4,-0.7 2.1,-1.1 6.1,-3 12.5,-5.3 19.3,-6.8 0.2,-1 0.6,-1.9 1.2,-2.6 -7.7,1.6 -15.1,4.2 -22,7.6 -0.7,0.4 -1.4,0.7 -2.1,1.1 z"
       id="path157" /><path
       class="st31"
       d="m 538.9,329.7 c 0.7,0.4 1.4,0.7 2.1,1.1 l 1.5,-1.8 c -0.7,-0.4 -1.4,-0.7 -2.1,-1.1 -6.9,-3.4 -14.2,-6 -22,-7.6 0.6,0.8 1,1.7 1.2,2.6 6.8,1.5 13.3,3.9 19.3,6.8"
       id="path158" /><path
       class="st31"
       d="m 575.3,296.9 1.4,-1.9 c -0.7,-0.4 -1.3,-0.9 -2,-1.3 -18.5,-12 -40,-19.7 -63.2,-21.6 0,0.8 0.1,1.6 0.1,2.3 22.6,1.9 43.5,9.4 61.6,21.1 0.7,0.5 1.4,1 2.1,1.4"
       id="path159" /><path
       class="st31"
       d="m 589.7,278.5 1.4,-1.8 c -0.5,-0.3 -1,-0.7 -1.5,-1 -24.2,-16.3 -53,-26.1 -83.9,-27.2 l 0.8,2.4 c 30.1,1.2 58.1,10.8 81.6,26.6 0.6,0.3 1.1,0.6 1.6,1"
       id="path160" /><path
       class="st28"
       d="m 486.1,324.6 4,23.1 V 317 l -1.6,0.9 c -1.9,1.1 -2.9,3.2 -2.5,5.3 z"
       id="path161" /><path
       class="st28"
       d="m 405.7,523.2 c 0,0 2.2,12 4.2,22.2 -0.1,-0.3 -0.3,-0.6 -0.4,-1 -0.1,-0.3 -1.1,-2.8 -3.3,-6.1 -1.8,-2.7 -3.5,-4.9 -6,-7.5 v 0 c -9.6,-1.3 -24,0 -29.9,7.7 -0.4,0.5 -0.6,1 -0.6,1.5 0,0.8 0.4,1.6 1.1,2.1 l 38.7,19.2 c 0.4,0.2 0.8,0.4 1.3,0.4 0.6,0 1.3,-0.2 1.7,-0.7 0.5,-0.5 0.8,-1.3 1,-2.6 9.1,-35.5 28.2,-65 32.9,-71.9 0.8,-1.1 1,-1.4 1.2,-1.5 0.5,-0.5 1.2,-0.8 2,-0.8 0.8,0 1.5,0.3 2,0.8 0.5,0.6 0.7,1 1.3,2.9 0,0.1 5.8,17.7 6,18.4 0.3,1 0.7,1.7 1.2,2.2 0.6,0.6 1.4,0.9 2.2,0.9 1,0 2.1,-0.4 3.2,-1.2 l 10.4,-7.3 c 0,0 0.3,-0.2 0.4,-0.3 0.3,-0.3 0.6,-0.8 0.6,-1.3 0,-0.5 -0.2,-1 -0.6,-1.3 -0.3,-0.3 -0.8,-0.5 -0.9,-0.5 l -10.9,-2 c 0,0 0,0 0,0.1 -1.4,2.5 -3,5.8 -4,8.7 0.1,-2.2 0.8,-12.5 1,-16.4 l -6.2,-15.8 c -0.7,-1.7 -2.2,-3 -4.1,-3.3 -1.9,-0.3 -3.7,0.5 -4.9,2 z"
       id="path162" /><path
       class="st31"
       d="m 370.3,538.7 c 0,0 0,0 0,0 0,-0.1 0.1,-0.1 0.1,-0.1 0,0 0.1,-0.1 0.1,-0.1 0.2,-0.3 0.5,-0.6 0.8,-0.9 C 357.9,524.2 346.8,509 338.1,492 l -2.1,1 c 8.9,17.3 20.4,33 34.1,46.7 -0.1,-0.4 0,-0.7 0.2,-1"
       id="path163" /><path
       class="st36"
       d="m 370.9,537.9 c 6.2,-7.2 20.1,-8.4 29.5,-7.2 2.5,2.6 4.2,4.8 6,7.5 2.2,3.4 3.1,5.8 3.3,6.1 0.2,0.3 0.3,0.6 0.4,1 -2.1,-10.2 -4.2,-22.2 -4.2,-22.2 l 40.8,-52.6 c 1.1,-1.5 3,-2.2 4.9,-2 1.8,0.3 3.4,1.5 4.1,3.3 l 6.2,15.8 c -0.2,3.8 -0.9,14.1 -1,16.3 1,-2.9 2.6,-6.2 4,-8.7 0,0 0,0 0,0 l 10.9,2 c 0.1,0 0.6,0.2 0.9,0.5 0.2,0.2 0.3,0.4 0.4,0.6 -0.8,-2.7 -10.8,-36.1 -10.8,-36.1 -7.9,-4.1 -14.9,-9.7 -20.4,-16.3 -0.6,-0.7 -1.2,-1.5 -1.8,-2.2 l -26,31.6 0.3,4.9 z"
       id="path164" /><path
       class="st28"
       d="m 594.3,523.2 c 0,0 -2.2,12 -4.2,22.2 0.1,-0.3 0.3,-0.6 0.4,-1 0.1,-0.3 1,-2.8 3.2,-6.1 1.8,-2.7 3.5,-4.9 6,-7.5 v 0 c 9.6,-1.3 24,0 29.9,7.7 0.4,0.5 0.6,1 0.6,1.5 0,0.8 -0.4,1.6 -1.1,2.1 l -38.7,19.2 c -0.4,0.2 -0.8,0.4 -1.3,0.4 -0.6,0 -1.3,-0.2 -1.7,-0.7 -0.5,-0.5 -0.8,-1.3 -1,-2.6 -9,-35.5 -28.2,-65 -32.9,-71.9 -0.7,-1.1 -1,-1.4 -1.2,-1.5 -0.5,-0.5 -1.2,-0.8 -2,-0.8 -0.8,0 -1.5,0.3 -2,0.8 -0.5,0.6 -0.7,1 -1.3,2.9 0,0.1 -5.8,17.7 -6,18.4 -0.3,1 -0.7,1.7 -1.2,2.2 -0.6,0.6 -1.4,0.9 -2.2,0.9 -1,0 -2.1,-0.4 -3.2,-1.2 L 524,500.9 c 0,0 -0.3,-0.2 -0.4,-0.3 -0.3,-0.3 -0.6,-0.8 -0.6,-1.3 0,-0.5 0.2,-1 0.6,-1.3 0.3,-0.3 0.8,-0.5 0.9,-0.5 l 10.9,-2 c 0,0 0,0 0,0.1 1.4,2.5 3,5.8 4,8.7 -0.1,-2.2 -0.8,-12.5 -1,-16.4 l 6.2,-15.8 c 0.7,-1.7 2.2,-3 4.1,-3.3 1.8,-0.3 3.7,0.5 4.9,2 z"
       id="path165" /><path
       class="st31"
       d="m 684.3,409.2 c 0,-13 -1.4,-25.7 -3.9,-38 -0.5,0.8 -1.3,2 -1.8,2.6 2.3,11.4 3.4,23.3 3.4,35.4 0,30.7 -7.6,59.6 -21.1,85 -8.5,16.1 -19.2,30.6 -32,43.3 0.3,0.3 0.5,0.6 0.7,0.9 0,0 0,0 0,0 0,0 0.1,0.1 0.1,0.1 0,0 0,0.1 0.1,0.1 0,0 0,0 0,0 0.2,0.3 0.3,0.6 0.3,0.9 13,-13 24.1,-27.9 32.8,-44.3 13.7,-25.6 21.4,-54.9 21.4,-86"
       id="path166" /><path
       class="st36"
       d="m 629.1,537.9 c -6.2,-7.2 -20.1,-8.4 -29.5,-7.2 v 0 c -2.5,2.6 -4.2,4.8 -6,7.5 -2.2,3.4 -3.1,5.8 -3.2,6.1 -0.1,0.3 -0.3,0.6 -0.4,1 2,-10.2 4.2,-22.2 4.2,-22.2 l -40.8,-52.6 c -1.1,-1.5 -3,-2.2 -4.9,-2 -1.9,0.3 -3.4,1.5 -4.1,3.3 l -6.2,15.8 c 0.2,3.8 0.9,14.1 1,16.3 -1,-2.9 -2.6,-6.2 -4,-8.7 0,0 0,0 0,0 l -10.9,2 c -0.1,0 -0.6,0.2 -0.9,0.5 -0.2,0.2 -0.3,0.4 -0.4,0.6 0.8,-2.7 10.8,-36.1 10.8,-36.1 7.9,-4.1 14.9,-9.7 20.4,-16.3 0.6,-0.7 1.2,-1.5 1.8,-2.2 l 26,31.6 -0.3,4.9 z"
       id="path167" /></g></g>
</svg>
