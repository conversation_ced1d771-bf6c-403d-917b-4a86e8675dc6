<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام الأرشفة الإلكترونية</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- PDF.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
    <link href="assets/css/login-enhanced.css" rel="stylesheet">
    <!-- Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo-container">
                <i class="fas fa-archive logo-icon"></i>
                <span class="logo-text">نظام الأرشفة</span>
            </div>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <div class="sidebar-menu">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="#dashboard" onclick="showSection('dashboard')">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#files" onclick="showSection('files')">
                        <i class="fas fa-folder-open"></i>
                        <span>إدارة الملفات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#add-file" onclick="showSection('add-file')">
                        <i class="fas fa-plus-circle"></i>
                        <span>إضافة ملف</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#scanner" onclick="showSection('scanner')">
                        <i class="fas fa-scanner"></i>
                        <span>الماسح الضوئي</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#search" onclick="showSection('search')">
                        <i class="fas fa-search"></i>
                        <span>البحث المتقدم</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#users" onclick="showSection('users')">
                        <i class="fas fa-users"></i>
                        <span>إدارة المستخدمين</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#logs" onclick="showSection('logs')">
                        <i class="fas fa-history"></i>
                        <span>سجل العمليات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#about" onclick="showSection('about')">
                        <i class="fas fa-info-circle"></i>
                        <span>حول النظام</span>
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-details">
                    <span class="user-name" id="currentUserName">المدير</span>
                    <span class="user-role" id="currentUserRole">مدير النظام</span>
                </div>
            </div>
            <button class="btn btn-outline-danger btn-sm logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </button>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="main-content" id="mainContent">
        <!-- Top Navigation -->
        <nav class="top-nav">
            <div class="nav-left">
                <button class="btn btn-outline-secondary" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title" id="pageTitle">لوحة التحكم</h1>
            </div>
            <div class="nav-right">
                <div class="nav-controls">
                    <button class="btn btn-outline-secondary" onclick="toggleTheme()">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <button class="btn btn-outline-secondary" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <span class="badge bg-danger">3</span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#">إشعار جديد 1</a></li>
                            <li><a class="dropdown-item" href="#">إشعار جديد 2</a></li>
                            <li><a class="dropdown-item" href="#">إشعار جديد 3</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Content Area -->
        <div class="content-area">
            <!-- Dashboard Section -->
            <section id="dashboard-section" class="content-section active">
                <div class="row">
                    <div class="col-md-3 mb-4">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalFiles">0</h3>
                                <p>إجمالي الملفات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-folder"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalFolders">0</h3>
                                <p>إجمالي المجلدات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalUsers">0</h3>
                                <p>المستخدمين</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="todayActivity">0</h3>
                                <p>نشاط اليوم</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5>الملفات الحديثة</h5>
                            </div>
                            <div class="card-body">
                                <div id="recentFiles" class="recent-files-list">
                                    <!-- Recent files will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5>الإحصائيات</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="statsChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Other sections will be loaded dynamically -->
            <section id="files-section" class="content-section">
                <!-- Files management content -->
            </section>
            
            <section id="add-file-section" class="content-section">
                <!-- Add file content -->
            </section>
            
            <section id="scanner-section" class="content-section">
                <!-- Scanner content -->
            </section>
            
            <section id="search-section" class="content-section">
                <!-- Search content -->
            </section>
            
            <section id="users-section" class="content-section">
                <!-- Users management content -->
            </section>
            
            <section id="logs-section" class="content-section">
                <!-- Logs content -->
            </section>
            
            <section id="about-section" class="content-section">
                <!-- About content -->
            </section>
        </div>
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/database.js"></script>
    <script>
        // Ensure database is loaded before other scripts
        console.log('Database script loaded, localDB available:', !!window.localDB);
    </script>
    <script src="assets/js/file-manager.js"></script>
    <script src="assets/js/search-system.js"></script>
    <script src="assets/js/user-management.js"></script>
    <script src="assets/js/scanner-integration.js"></script>
    <script src="assets/js/about-system.js"></script>
    <script src="assets/js/dashboard.js"></script>
    <script src="assets/js/theme.js"></script>
    <script src="assets/js/language.js"></script>
</body>
</html>
