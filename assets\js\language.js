// ===== Language Management System =====

class LanguageManager {
    constructor() {
        this.currentLanguage = this.getStoredLanguage() || 'ar';
        this.translations = {};
        this.loadTranslations();
        this.initializeLanguage();
    }

    // Get stored language from localStorage
    getStoredLanguage() {
        return localStorage.getItem('archive_language');
    }

    // Store language in localStorage
    setStoredLanguage(language) {
        localStorage.setItem('archive_language', language);
    }

    // Load translations
    loadTranslations() {
        this.translations = {
            ar: {
                // Login Page
                'login_title': 'نظام الأرشفة الإلكترونية',
                'brand_description': 'نظام متطور لإدارة وأرشفة المستندات الرقمية بأحدث التقنيات',
                'secure_system': 'نظام آمن ومحمي',
                'advanced_search': 'بحث متقدم وذكي',
                'cloud_ready': 'جاهز للسحابة',
                'welcome_back': 'مرحباً بعودتك',
                'login_subtitle': 'يرجى تسجيل الدخول للمتابعة',
                'username': 'اسم المستخدم',
                'username_placeholder': 'أدخل اسم المستخدم',
                'password': 'كلمة المرور',
                'password_placeholder': 'أدخل كلمة المرور',
                'remember_me': 'تذكرني',
                'forgot_password': 'نسيت كلمة المرور؟',
                'login_button': 'تسجيل الدخول',
                'demo_accounts': 'حسابات تجريبية:',
                'admin_account': 'مدير النظام',
                'employee_account': 'موظف',
                'logging_in': 'جاري تسجيل الدخول...',
                'login_success': 'تم تسجيل الدخول بنجاح!',
                'dark_mode': 'الوضع الداكن',
                'light_mode': 'الوضع الفاتح',

                // Course Information
                'course_code': 'رمز الدورة',
                'course_number': 'رقم الدورة',
                'course_code_placeholder': 'أدخل رمز الدورة',
                'course_number_placeholder': 'أدخل رقم الدورة',
                
                // Dashboard
                'dashboard': 'لوحة التحكم',
                'file_management': 'إدارة الملفات',
                'add_file': 'إضافة ملف',
                'scanner': 'الماسح الضوئي',
                'advanced_search': 'البحث المتقدم',
                'user_management': 'إدارة المستخدمين',
                'activity_log': 'سجل العمليات',
                'about_system': 'حول النظام',
                'logout': 'تسجيل الخروج',
                
                // Statistics
                'total_files': 'إجمالي الملفات',
                'total_folders': 'إجمالي المجلدات',
                'users': 'المستخدمين',
                'today_activity': 'نشاط اليوم',
                'recent_files': 'الملفات الحديثة',
                'statistics': 'الإحصائيات',
                
                // File Types
                'violation_diary': 'يومية مخالفات',
                'certificate': 'إفادة',
                'leave_document': 'مستند إجازة',
                'admin_license': 'رخصة إدارية',
                
                // Actions
                'view': 'عرض',
                'edit': 'تعديل',
                'delete': 'حذف',
                'download': 'تحميل',
                'print': 'طباعة',
                'export': 'تصدير',
                'import': 'استيراد',
                'save': 'حفظ',
                'cancel': 'إلغاء',
                'confirm': 'تأكيد',
                'close': 'إغلاق',
                
                // Messages
                'loading': 'جاري التحميل...',
                'success': 'تم بنجاح',
                'error': 'حدث خطأ',
                'warning': 'تحذير',
                'info': 'معلومات',
                'no_data': 'لا توجد بيانات',
                'confirm_delete': 'هل أنت متأكد من الحذف؟',
                
                // User Roles
                'admin': 'مدير النظام',
                'employee': 'موظف',
                
                // Date/Time
                'today': 'اليوم',
                'yesterday': 'أمس',
                'this_week': 'هذا الأسبوع',
                'this_month': 'هذا الشهر',
                'this_year': 'هذا العام',
                
                // Search
                'search': 'بحث',
                'search_text': 'البحث في النص',
                'search_text_placeholder': 'ابحث في اسم الملف، الوصف، أو الكلمات المفتاحية',
                'all_categories': 'جميع التصنيفات',
                'course_code_search_placeholder': 'أدخل رمز الدورة للبحث',
                'course_number_search_placeholder': 'أدخل رقم الدورة للبحث',
                'search_placeholder': 'ابحث في الملفات...',
                'filter_by_date': 'تصفية حسب التاريخ',
                'filter_by_type': 'تصفية حسب النوع',
                'filter_by_user': 'تصفية حسب المستخدم',

                // File Form
                'file_name': 'اسم الملف',
                'category': 'التصنيف',
                'issue_date': 'تاريخ الإصدار',
                'keywords': 'الكلمات المفتاحية',
                'keywords_placeholder': 'افصل الكلمات بفاصلة',
                'description': 'الوصف',
                'description_placeholder': 'وصف اختياري للملف',
                'file_size': 'الحجم',
                'created_date': 'تاريخ الإنشاء',
                'created_by': 'المنشئ',
                
                // File Upload
                'upload_file': 'رفع ملف',
                'drag_drop': 'اسحب وأفلت الملفات هنا',
                'select_files': 'اختر الملفات',
                'supported_formats': 'الصيغ المدعومة',
                'max_file_size': 'الحد الأقصى لحجم الملف'
            },
            
            en: {
                // Login Page
                'login_title': 'Electronic Archive System',
                'brand_description': 'Advanced system for managing and archiving digital documents with latest technologies',
                'secure_system': 'Secure & Protected System',
                'advanced_search': 'Advanced & Smart Search',
                'cloud_ready': 'Cloud Ready',
                'welcome_back': 'Welcome Back',
                'login_subtitle': 'Please login to continue',
                'username': 'Username',
                'username_placeholder': 'Enter username',
                'password': 'Password',
                'password_placeholder': 'Enter password',
                'remember_me': 'Remember Me',
                'forgot_password': 'Forgot Password?',
                'login_button': 'Login',
                'demo_accounts': 'Demo Accounts:',
                'admin_account': 'System Admin',
                'employee_account': 'Employee',
                'logging_in': 'Logging in...',
                'login_success': 'Login successful!',
                'dark_mode': 'Dark Mode',
                'light_mode': 'Light Mode',

                // Course Information
                'course_code': 'Course Code',
                'course_number': 'Course Number',
                'course_code_placeholder': 'Enter course code',
                'course_number_placeholder': 'Enter course number',
                
                // Dashboard
                'dashboard': 'Dashboard',
                'file_management': 'File Management',
                'add_file': 'Add File',
                'scanner': 'Scanner',
                'advanced_search': 'Advanced Search',
                'user_management': 'User Management',
                'activity_log': 'Activity Log',
                'about_system': 'About System',
                'logout': 'Logout',
                
                // Statistics
                'total_files': 'Total Files',
                'total_folders': 'Total Folders',
                'users': 'Users',
                'today_activity': 'Today\'s Activity',
                'recent_files': 'Recent Files',
                'statistics': 'Statistics',
                
                // File Types
                'violation_diary': 'Violation Diary',
                'certificate': 'Certificate',
                'leave_document': 'Leave Document',
                'admin_license': 'Administrative License',
                
                // Actions
                'view': 'View',
                'edit': 'Edit',
                'delete': 'Delete',
                'download': 'Download',
                'print': 'Print',
                'export': 'Export',
                'import': 'Import',
                'save': 'Save',
                'cancel': 'Cancel',
                'confirm': 'Confirm',
                'close': 'Close',
                
                // Messages
                'loading': 'Loading...',
                'success': 'Success',
                'error': 'Error',
                'warning': 'Warning',
                'info': 'Information',
                'no_data': 'No Data',
                'confirm_delete': 'Are you sure you want to delete?',
                
                // User Roles
                'admin': 'System Administrator',
                'employee': 'Employee',
                
                // Date/Time
                'today': 'Today',
                'yesterday': 'Yesterday',
                'this_week': 'This Week',
                'this_month': 'This Month',
                'this_year': 'This Year',
                
                // Search
                'search': 'Search',
                'search_text': 'Search Text',
                'search_text_placeholder': 'Search in file name, description, or keywords',
                'all_categories': 'All Categories',
                'course_code_search_placeholder': 'Enter course code to search',
                'course_number_search_placeholder': 'Enter course number to search',
                'search_placeholder': 'Search files...',
                'filter_by_date': 'Filter by Date',
                'filter_by_type': 'Filter by Type',
                'filter_by_user': 'Filter by User',

                // File Form
                'file_name': 'File Name',
                'category': 'Category',
                'issue_date': 'Issue Date',
                'keywords': 'Keywords',
                'keywords_placeholder': 'Separate keywords with comma',
                'description': 'Description',
                'description_placeholder': 'Optional file description',
                'file_size': 'File Size',
                'created_date': 'Created Date',
                'created_by': 'Created By',
                
                // File Upload
                'upload_file': 'Upload File',
                'drag_drop': 'Drag and drop files here',
                'select_files': 'Select Files',
                'supported_formats': 'Supported Formats',
                'max_file_size': 'Maximum File Size'
            }
        };
    }

    // Initialize language
    initializeLanguage() {
        this.setLanguage(this.currentLanguage);
        this.setupLanguageToggle();
    }

    // Set language
    setLanguage(language) {
        this.currentLanguage = language;
        this.setStoredLanguage(language);
        
        // Update HTML attributes
        document.documentElement.lang = language;
        document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
        
        // Update page content
        this.updatePageContent();
        
        // Update language buttons
        this.updateLanguageButtons();
        
        // Log language change
        if (window.authSystem && window.authSystem.currentUser) {
            window.authSystem.logActivity('language_change', `تغيير اللغة إلى: ${language === 'ar' ? 'العربية' : 'English'}`);
        }
    }

    // Update page content with translations
    updatePageContent() {
        // Translate text content
        const elements = document.querySelectorAll('[data-translate]');
        elements.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.getTranslation(key);
            if (translation) {
                if (element.tagName === 'INPUT' && element.type === 'text') {
                    element.placeholder = translation;
                } else {
                    element.textContent = translation;
                }
            }
        });

        // Translate placeholders
        const placeholderElements = document.querySelectorAll('[data-translate-placeholder]');
        placeholderElements.forEach(element => {
            const key = element.getAttribute('data-translate-placeholder');
            const translation = this.getTranslation(key);
            if (translation) {
                element.placeholder = translation;
            }
        });
    }

    // Get translation for key
    getTranslation(key) {
        return this.translations[this.currentLanguage]?.[key] || 
               this.translations['ar']?.[key] || 
               key;
    }

    // Setup language toggle
    setupLanguageToggle() {
        document.addEventListener('click', (e) => {
            if (e.target.closest('[onclick*="switchLanguage"]')) {
                e.preventDefault();
                const button = e.target.closest('[onclick*="switchLanguage"]');
                const match = button.getAttribute('onclick').match(/switchLanguage\('(\w+)'\)/);
                if (match) {
                    this.setLanguage(match[1]);
                }
            }
            
            if (e.target.closest('[onclick*="toggleLanguage"]')) {
                e.preventDefault();
                this.toggleLanguage();
            }
        });
    }

    // Toggle between Arabic and English
    toggleLanguage() {
        const newLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';
        this.setLanguage(newLanguage);
    }

    // Update language buttons
    updateLanguageButtons() {
        const languageButtons = document.querySelectorAll('.language-switcher button');
        languageButtons.forEach(button => {
            const match = button.getAttribute('onclick')?.match(/switchLanguage\('(\w+)'\)/);
            if (match) {
                const buttonLang = match[1];
                button.classList.toggle('active', buttonLang === this.currentLanguage);
            }
        });
    }

    // Format date according to current language
    formatDate(date, options = {}) {
        const dateObj = new Date(date);
        const locale = this.currentLanguage === 'ar' ? 'ar-SA' : 'en-US';
        
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        return dateObj.toLocaleDateString(locale, { ...defaultOptions, ...options });
    }

    // Format time according to current language
    formatTime(date, options = {}) {
        const dateObj = new Date(date);
        const locale = this.currentLanguage === 'ar' ? 'ar-SA' : 'en-US';
        
        const defaultOptions = {
            hour: '2-digit',
            minute: '2-digit'
        };
        
        return dateObj.toLocaleTimeString(locale, { ...defaultOptions, ...options });
    }

    // Format number according to current language
    formatNumber(number) {
        const locale = this.currentLanguage === 'ar' ? 'ar-SA' : 'en-US';
        return new Intl.NumberFormat(locale).format(number);
    }

    // Get current language
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    // Check if current language is RTL
    isRTL() {
        return this.currentLanguage === 'ar';
    }

    // Add translation
    addTranslation(language, key, value) {
        if (!this.translations[language]) {
            this.translations[language] = {};
        }
        this.translations[language][key] = value;
    }

    // Get all translations for current language
    getAllTranslations() {
        return this.translations[this.currentLanguage] || {};
    }

    // Export language settings
    exportLanguageSettings() {
        return {
            currentLanguage: this.currentLanguage,
            translations: this.translations
        };
    }

    // Import language settings
    importLanguageSettings(settings) {
        if (settings.currentLanguage) {
            this.setLanguage(settings.currentLanguage);
        }
        if (settings.translations) {
            this.translations = { ...this.translations, ...settings.translations };
        }
    }
}

// Global language functions
function switchLanguage(language) {
    if (window.languageManager) {
        window.languageManager.setLanguage(language);
    }
}

function toggleLanguage() {
    if (window.languageManager) {
        window.languageManager.toggleLanguage();
    }
}

// Initialize language manager
const languageManager = new LanguageManager();

// Make language manager globally available
window.languageManager = languageManager;
window.switchLanguage = switchLanguage;
window.toggleLanguage = toggleLanguage;

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LanguageManager;
}
