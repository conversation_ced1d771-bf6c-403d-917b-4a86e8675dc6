// ===== Authentication System =====

class AuthSystem {
    constructor() {
        this.users = this.loadUsers();
        this.currentUser = this.getCurrentUser();
        this.initializeAuth();
    }

    // Load users from localStorage (simulating database)
    loadUsers() {
        const defaultUsers = [
            {
                id: 1,
                username: 'admin',
                password: this.hashPassword('admin123'),
                role: 'admin',
                name: 'مدير النظام',
                email: '<EMAIL>',
                createdAt: new Date().toISOString(),
                lastLogin: null,
                isActive: true
            },
            {
                id: 2,
                username: 'employee',
                password: this.hashPassword('emp123'),
                role: 'employee',
                name: 'موظف الأرشيف',
                email: '<EMAIL>',
                createdAt: new Date().toISOString(),
                lastLogin: null,
                isActive: true
            }
        ];

        const storedUsers = localStorage.getItem('archive_users');
        if (!storedUsers) {
            localStorage.setItem('archive_users', JSON.stringify(defaultUsers));
            return defaultUsers;
        }
        return JSON.parse(storedUsers);
    }

    // Simple password hashing (in production, use proper hashing)
    hashPassword(password) {
        return btoa(password + 'archive_salt');
    }

    // Verify password
    verifyPassword(password, hashedPassword) {
        return this.hashPassword(password) === hashedPassword;
    }

    // Get current user from session
    getCurrentUser() {
        const userSession = sessionStorage.getItem('archive_current_user');
        return userSession ? JSON.parse(userSession) : null;
    }

    // Initialize authentication
    initializeAuth() {
        // Check if user is already logged in
        if (this.currentUser && window.location.pathname.includes('index.html')) {
            window.location.href = 'dashboard.html';
        }

        // Check if user needs to login
        if (!this.currentUser && window.location.pathname.includes('dashboard.html')) {
            window.location.href = 'index.html';
        }

        // Setup login form
        this.setupLoginForm();
    }

    // Setup login form event listeners
    setupLoginForm() {
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }
    }

    // Handle login form submission
    async handleLogin(event) {
        event.preventDefault();
        
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        // Show loading
        this.showLoading(true);

        try {
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Find user
            const user = this.users.find(u => u.username === username && u.isActive);
            
            if (!user) {
                throw new Error('اسم المستخدم غير موجود');
            }

            if (!this.verifyPassword(password, user.password)) {
                throw new Error('كلمة المرور غير صحيحة');
            }

            // Update last login
            user.lastLogin = new Date().toISOString();
            this.updateUser(user);

            // Create session
            const sessionData = {
                id: user.id,
                username: user.username,
                name: user.name,
                role: user.role,
                email: user.email,
                loginTime: new Date().toISOString()
            };

            // Store session
            sessionStorage.setItem('archive_current_user', JSON.stringify(sessionData));
            
            if (rememberMe) {
                localStorage.setItem('archive_remember_user', username);
            }

            // Log the login activity
            this.logActivity('login', `تسجيل دخول المستخدم: ${user.name}`);

            // Show success message
            this.showAlert('success', 'تم تسجيل الدخول بنجاح');

            // Redirect to dashboard
            setTimeout(() => {
                window.location.href = 'dashboard.html';
            }, 1000);

        } catch (error) {
            this.showAlert('error', error.message);
        } finally {
            this.showLoading(false);
        }
    }

    // Update user data
    updateUser(user) {
        const userIndex = this.users.findIndex(u => u.id === user.id);
        if (userIndex !== -1) {
            this.users[userIndex] = user;
            localStorage.setItem('archive_users', JSON.stringify(this.users));
        }
    }

    // Logout function
    logout() {
        if (this.currentUser) {
            this.logActivity('logout', `تسجيل خروج المستخدم: ${this.currentUser.name}`);
        }
        
        sessionStorage.removeItem('archive_current_user');
        localStorage.removeItem('archive_remember_user');
        window.location.href = 'index.html';
    }

    // Show/hide loading overlay
    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.toggle('d-none', !show);
        }
    }

    // Show alert messages
    showAlert(type, message) {
        // Remove existing alerts
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        // Create new alert
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert alert
        const form = document.getElementById('loginForm');
        if (form) {
            form.parentNode.insertBefore(alertDiv, form);
        }

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    // Log user activities
    logActivity(action, description) {
        const logs = JSON.parse(localStorage.getItem('archive_activity_logs') || '[]');
        const logEntry = {
            id: Date.now(),
            userId: this.currentUser ? this.currentUser.id : null,
            username: this.currentUser ? this.currentUser.username : 'غير معروف',
            action: action,
            description: description,
            timestamp: new Date().toISOString(),
            ip: 'localhost', // In real app, get actual IP
            userAgent: navigator.userAgent
        };

        logs.unshift(logEntry);
        
        // Keep only last 1000 logs
        if (logs.length > 1000) {
            logs.splice(1000);
        }

        localStorage.setItem('archive_activity_logs', JSON.stringify(logs));
    }

    // Check user permissions
    hasPermission(permission) {
        if (!this.currentUser) return false;
        
        const permissions = {
            admin: ['read', 'write', 'delete', 'manage_users', 'view_logs', 'export', 'scan'],
            employee: ['read', 'write', 'scan']
        };

        return permissions[this.currentUser.role]?.includes(permission) || false;
    }

    // Get user role display name
    getRoleDisplayName(role) {
        const roleNames = {
            admin: 'مدير النظام',
            employee: 'موظف'
        };
        return roleNames[role] || role;
    }
}

// Password toggle function
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('passwordToggleIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Initialize authentication system
const authSystem = new AuthSystem();

// Make logout function globally available
window.logout = () => authSystem.logout();

// Auto-fill remembered username
document.addEventListener('DOMContentLoaded', () => {
    const rememberedUser = localStorage.getItem('archive_remember_user');
    if (rememberedUser) {
        const usernameInput = document.getElementById('username');
        if (usernameInput) {
            usernameInput.value = rememberedUser;
            document.getElementById('rememberMe').checked = true;
        }
    }
});

// Export for use in other modules
window.AuthSystem = AuthSystem;
window.authSystem = authSystem;
