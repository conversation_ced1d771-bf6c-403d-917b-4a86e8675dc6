// ===== Advanced Search System =====

class SearchSystem {
    constructor() {
        this.searchResults = [];
        this.searchHistory = this.loadSearchHistory();
        this.savedSearches = this.loadSavedSearches();
        this.initializeSearch();
    }

    // Initialize search system
    initializeSearch() {
        this.createSearchInterface();
        this.setupSearchEvents();
    }

    // Create search interface
    createSearchInterface() {
        const searchHTML = `
            <div class="advanced-search-container">
                <div class="search-header">
                    <h4><i class="fas fa-search me-2"></i>البحث المتقدم</h4>
                    <p class="text-muted">ابحث في جميع الملفات والمستندات المحفوظة</p>
                </div>
                
                <div class="search-form-container">
                    <div class="card">
                        <div class="card-header">
                            <h6>معايير البحث</h6>
                        </div>
                        <div class="card-body">
                            <form id="advancedSearchForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label" data-translate="search_text">البحث في النص</label>
                                        <input type="text" class="form-control" id="searchText"
                                               placeholder="" data-translate-placeholder="search_text_placeholder">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label" data-translate="category">التصنيف</label>
                                        <select class="form-control" id="searchCategory">
                                            <option value="" data-translate="all_categories">جميع التصنيفات</option>
                                            <option value="يومية مخالفات" data-translate="violation_diary">يومية مخالفات</option>
                                            <option value="إفادة" data-translate="certificate">إفادة</option>
                                            <option value="مستند إجازة" data-translate="leave_document">مستند إجازة</option>
                                            <option value="رخصة إدارية" data-translate="admin_license">رخصة إدارية</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label" data-translate="course_code">رمز الدورة</label>
                                        <input type="text" class="form-control" id="searchCourseCode"
                                               placeholder="" data-translate-placeholder="course_code_search_placeholder">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label" data-translate="course_number">رقم الدورة</label>
                                        <input type="number" class="form-control" id="searchCourseNumber"
                                               placeholder="" data-translate-placeholder="course_number_search_placeholder" min="1">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">من تاريخ</label>
                                        <input type="date" class="form-control" id="searchDateFrom">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">إلى تاريخ</label>
                                        <input type="date" class="form-control" id="searchDateTo">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">تاريخ الإصدار من</label>
                                        <input type="date" class="form-control" id="searchIssueDateFrom">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">تاريخ الإصدار إلى</label>
                                        <input type="date" class="form-control" id="searchIssueDateTo">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">نوع الملف</label>
                                        <select class="form-control" id="searchFileType">
                                            <option value="">جميع الأنواع</option>
                                            <option value="pdf">PDF</option>
                                            <option value="word">Word</option>
                                            <option value="image">صورة</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">حجم الملف</label>
                                        <select class="form-control" id="searchFileSize">
                                            <option value="">أي حجم</option>
                                            <option value="small">صغير (أقل من 1MB)</option>
                                            <option value="medium">متوسط (1-5MB)</option>
                                            <option value="large">كبير (أكثر من 5MB)</option>
                                        </select>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">الكلمات المفتاحية</label>
                                        <input type="text" class="form-control" id="searchTags" 
                                               placeholder="افصل الكلمات بفاصلة">
                                    </div>
                                </div>
                                
                                <div class="search-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>بحث
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="searchSystem.clearSearch()">
                                        <i class="fas fa-times me-2"></i>مسح
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="searchSystem.saveCurrentSearch()">
                                        <i class="fas fa-bookmark me-2"></i>حفظ البحث
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="search-shortcuts mt-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>عمليات بحث محفوظة</h6>
                                </div>
                                <div class="card-body">
                                    <div id="savedSearchesList">
                                        ${this.renderSavedSearches()}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>عمليات بحث حديثة</h6>
                                </div>
                                <div class="card-body">
                                    <div id="searchHistoryList">
                                        ${this.renderSearchHistory()}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="search-results mt-4" id="searchResultsContainer" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6>نتائج البحث</h6>
                                <div class="results-actions">
                                    <button class="btn btn-sm btn-outline-primary" onclick="searchSystem.exportResults()">
                                        <i class="fas fa-download me-1"></i>تصدير
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="searchSystem.printResults()">
                                        <i class="fas fa-print me-1"></i>طباعة
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="searchResultsList"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const searchSection = document.getElementById('search-section');
        if (searchSection) {
            console.log('Search section found, inserting HTML...');
            searchSection.innerHTML = searchHTML;

            // Setup events after DOM insertion
            setTimeout(() => {
                this.setupSearchEvents();
            }, 100);

            console.log('Search interface created successfully');
        } else {
            console.error('Search section not found in DOM');
        }
    }

    // Setup search events
    setupSearchEvents() {
        const searchForm = document.getElementById('advancedSearchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performSearch();
            });
        }

        // Real-time search on text input
        const searchText = document.getElementById('searchText');
        if (searchText) {
            searchText.addEventListener('input', () => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    if (searchText.value.length >= 3) {
                        this.performQuickSearch(searchText.value);
                    }
                }, 500);
            });
        }
    }

    // Perform advanced search
    async performSearch() {
        const criteria = this.getSearchCriteria();
        
        try {
            // Show loading
            this.showSearchLoading(true);
            
            // Get all files
            const allFiles = await window.localDB.getFiles();
            
            // Apply search filters
            this.searchResults = this.filterFiles(allFiles, criteria);
            
            // Save to search history
            this.addToSearchHistory(criteria);
            
            // Display results
            this.displaySearchResults();
            
            // Log search activity
            if (window.authSystem) {
                window.authSystem.logActivity('search', `بحث متقدم: ${JSON.stringify(criteria)}`);
            }
            
        } catch (error) {
            console.error('Search error:', error);
            this.showAlert('error', 'حدث خطأ أثناء البحث');
        } finally {
            this.showSearchLoading(false);
        }
    }

    // Get search criteria from form
    getSearchCriteria() {
        return {
            text: document.getElementById('searchText')?.value || '',
            category: document.getElementById('searchCategory')?.value || '',
            courseCode: document.getElementById('searchCourseCode')?.value || '',
            courseNumber: document.getElementById('searchCourseNumber')?.value || '',
            dateFrom: document.getElementById('searchDateFrom')?.value || '',
            dateTo: document.getElementById('searchDateTo')?.value || '',
            issueDateFrom: document.getElementById('searchIssueDateFrom')?.value || '',
            issueDateTo: document.getElementById('searchIssueDateTo')?.value || '',
            fileType: document.getElementById('searchFileType')?.value || '',
            fileSize: document.getElementById('searchFileSize')?.value || '',
            tags: document.getElementById('searchTags')?.value || ''
        };
    }

    // Filter files based on criteria
    filterFiles(files, criteria) {
        return files.filter(file => {
            // Text search
            if (criteria.text) {
                const searchText = criteria.text.toLowerCase();
                const fileName = file.name || '';
                const fileDescription = file.description || '';
                const fileTags = (file.tags && Array.isArray(file.tags)) ? file.tags.join(' ') : '';
                const fileText = `${fileName} ${fileDescription} ${fileTags}`.toLowerCase();
                if (!fileText.includes(searchText)) {
                    return false;
                }
            }

            // Category filter
            if (criteria.category && file.category !== criteria.category) {
                return false;
            }

            // Course code filter
            if (criteria.courseCode) {
                if (!file.courseCode || !file.courseCode.toLowerCase().includes(criteria.courseCode.toLowerCase())) {
                    return false;
                }
            }

            // Course number filter
            if (criteria.courseNumber) {
                if (!file.courseNumber || file.courseNumber !== parseInt(criteria.courseNumber)) {
                    return false;
                }
            }

            // Date range filter (creation date)
            if (criteria.dateFrom) {
                if (new Date(file.createdAt) < new Date(criteria.dateFrom)) {
                    return false;
                }
            }
            if (criteria.dateTo) {
                if (new Date(file.createdAt) > new Date(criteria.dateTo)) {
                    return false;
                }
            }

            // Issue date range filter
            if (criteria.issueDateFrom) {
                if (new Date(file.issueDate) < new Date(criteria.issueDateFrom)) {
                    return false;
                }
            }
            if (criteria.issueDateTo) {
                if (new Date(file.issueDate) > new Date(criteria.issueDateTo)) {
                    return false;
                }
            }

            // File type filter
            if (criteria.fileType) {
                if (criteria.fileType === 'pdf' && !file.type.includes('pdf')) {
                    return false;
                }
                if (criteria.fileType === 'word' && !file.type.includes('word') && !file.type.includes('document')) {
                    return false;
                }
                if (criteria.fileType === 'image' && !file.type.includes('image')) {
                    return false;
                }
            }

            // File size filter
            if (criteria.fileSize) {
                const sizeMB = file.size / (1024 * 1024);
                if (criteria.fileSize === 'small' && sizeMB >= 1) {
                    return false;
                }
                if (criteria.fileSize === 'medium' && (sizeMB < 1 || sizeMB > 5)) {
                    return false;
                }
                if (criteria.fileSize === 'large' && sizeMB <= 5) {
                    return false;
                }
            }

            // Tags filter
            if (criteria.tags) {
                const searchTags = criteria.tags.split(',').map(tag => tag.trim().toLowerCase());
                const fileTags = file.tags.map(tag => tag.toLowerCase());
                if (!searchTags.some(tag => fileTags.includes(tag))) {
                    return false;
                }
            }

            return true;
        });
    }

    // Display search results
    displaySearchResults() {
        const container = document.getElementById('searchResultsContainer');
        const resultsList = document.getElementById('searchResultsList');
        
        if (!container || !resultsList) return;

        container.style.display = 'block';

        if (this.searchResults.length === 0) {
            resultsList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-search"></i>
                    <h4>لا توجد نتائج</h4>
                    <p>لم يتم العثور على ملفات تطابق معايير البحث</p>
                </div>
            `;
            return;
        }

        resultsList.innerHTML = `
            <div class="results-summary mb-3">
                <p class="text-muted">تم العثور على ${this.searchResults.length} ملف</p>
            </div>
            <div class="results-grid">
                ${this.searchResults.map(file => this.renderSearchResultItem(file)).join('')}
            </div>
        `;
    }

    // Render search result item
    renderSearchResultItem(file) {
        const fileIcon = this.getFileIcon(file.type);
        const fileSize = this.formatFileSize(file.size);
        const createdDate = this.formatDate(file.createdAt);
        
        return `
            <div class="search-result-item" onclick="viewFile('${file.id}')">
                <div class="result-icon">
                    <i class="${fileIcon}"></i>
                </div>
                <div class="result-info">
                    <h6 class="result-title">${file.name}</h6>
                    <div class="result-meta">
                        <span class="result-category">${file.category}</span>
                        <span class="result-date">${createdDate}</span>
                        <span class="result-size">${fileSize}</span>
                    </div>
                    ${file.description ? `<p class="result-description">${file.description}</p>` : ''}
                    ${file.tags && file.tags.length > 0 ? `
                        <div class="result-tags">
                            ${file.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                        </div>
                    ` : ''}
                </div>
                <div class="result-actions">
                    <button class="btn btn-sm btn-primary" onclick="event.stopPropagation(); viewFile('${file.id}')" title="عرض الملف">
                        <i class="fas fa-eye me-1"></i>عرض
                    </button>
                    <button class="btn btn-sm btn-success" onclick="event.stopPropagation(); downloadFile('${file.id}')" title="تحميل الملف">
                        <i class="fas fa-download me-1"></i>تحميل
                    </button>
                </div>
            </div>
        `;
    }

    // Quick search function
    async performQuickSearch(searchText) {
        try {
            const allFiles = await window.localDB.getFiles();
            const results = allFiles.filter(file => {
                const fileText = `${file.name} ${file.description} ${file.tags.join(' ')}`.toLowerCase();
                return fileText.includes(searchText.toLowerCase());
            }).slice(0, 5); // Limit to 5 results for quick search

            this.showQuickSearchResults(results, searchText);
        } catch (error) {
            console.error('Quick search error:', error);
        }
    }

    // Show quick search results
    showQuickSearchResults(results, searchText) {
        // This would show a dropdown with quick results
        // Implementation depends on UI requirements
    }

    // Clear search form
    clearSearch() {
        document.getElementById('advancedSearchForm').reset();
        document.getElementById('searchResultsContainer').style.display = 'none';
    }

    // Save current search
    saveCurrentSearch() {
        const criteria = this.getSearchCriteria();
        const name = prompt('اسم البحث المحفوظ:');
        
        if (name && name.trim()) {
            const savedSearch = {
                id: Date.now(),
                name: name.trim(),
                criteria: criteria,
                createdAt: new Date().toISOString()
            };
            
            this.savedSearches.unshift(savedSearch);
            this.saveSavedSearches();
            this.updateSavedSearchesList();
            
            this.showAlert('success', 'تم حفظ البحث بنجاح');
        }
    }

    // Load saved search
    loadSavedSearch(searchId) {
        const savedSearch = this.savedSearches.find(s => s.id === searchId);
        if (savedSearch) {
            this.applyCriteria(savedSearch.criteria);
            this.performSearch();
        }
    }

    // Apply criteria to form
    applyCriteria(criteria) {
        Object.keys(criteria).forEach(key => {
            const element = document.getElementById(`search${key.charAt(0).toUpperCase() + key.slice(1)}`);
            if (element) {
                element.value = criteria[key];
            }
        });
    }

    // Add to search history
    addToSearchHistory(criteria) {
        const historyItem = {
            id: Date.now(),
            criteria: criteria,
            timestamp: new Date().toISOString(),
            resultsCount: this.searchResults.length
        };
        
        this.searchHistory.unshift(historyItem);
        
        // Keep only last 10 searches
        if (this.searchHistory.length > 10) {
            this.searchHistory = this.searchHistory.slice(0, 10);
        }
        
        this.saveSearchHistory();
        this.updateSearchHistoryList();
    }

    // Render saved searches
    renderSavedSearches() {
        if (this.savedSearches.length === 0) {
            return '<p class="text-muted">لا توجد عمليات بحث محفوظة</p>';
        }
        
        return this.savedSearches.map(search => `
            <div class="saved-search-item">
                <div class="search-name">${search.name}</div>
                <div class="search-date">${this.formatDate(search.createdAt)}</div>
                <div class="search-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="searchSystem.loadSavedSearch(${search.id})">
                        تطبيق
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="searchSystem.deleteSavedSearch(${search.id})">
                        حذف
                    </button>
                </div>
            </div>
        `).join('');
    }

    // Render search history
    renderSearchHistory() {
        if (this.searchHistory.length === 0) {
            return '<p class="text-muted">لا توجد عمليات بحث حديثة</p>';
        }
        
        return this.searchHistory.map(item => `
            <div class="history-item">
                <div class="history-summary">${this.getSearchSummary(item.criteria)}</div>
                <div class="history-meta">
                    ${this.formatDate(item.timestamp)} • ${item.resultsCount} نتيجة
                </div>
                <button class="btn btn-sm btn-outline-primary" onclick="searchSystem.loadHistorySearch(${item.id})">
                    إعادة تطبيق
                </button>
            </div>
        `).join('');
    }

    // Get search summary
    getSearchSummary(criteria) {
        const parts = [];
        if (criteria.text) parts.push(`"${criteria.text}"`);
        if (criteria.category) parts.push(criteria.category);
        if (criteria.dateFrom || criteria.dateTo) parts.push('تصفية بالتاريخ');
        
        return parts.length > 0 ? parts.join(' • ') : 'بحث عام';
    }

    // Helper functions
    getFileIcon(fileType) {
        if (fileType.includes('pdf')) return 'fas fa-file-pdf';
        if (fileType.includes('word') || fileType.includes('document')) return 'fas fa-file-word';
        if (fileType.includes('image')) return 'fas fa-file-image';
        return 'fas fa-file';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    showSearchLoading(show) {
        // Implementation for loading indicator
    }

    showAlert(type, message) {
        // Use the same alert system as file manager
        if (window.fileManager) {
            window.fileManager.showAlert(type, message);
        }
    }

    // Storage functions
    loadSearchHistory() {
        return JSON.parse(localStorage.getItem('archive_search_history') || '[]');
    }

    saveSearchHistory() {
        localStorage.setItem('archive_search_history', JSON.stringify(this.searchHistory));
    }

    loadSavedSearches() {
        return JSON.parse(localStorage.getItem('archive_saved_searches') || '[]');
    }

    saveSavedSearches() {
        localStorage.setItem('archive_saved_searches', JSON.stringify(this.savedSearches));
    }

    updateSavedSearchesList() {
        const container = document.getElementById('savedSearchesList');
        if (container) {
            container.innerHTML = this.renderSavedSearches();
        }
    }

    updateSearchHistoryList() {
        const container = document.getElementById('searchHistoryList');
        if (container) {
            container.innerHTML = this.renderSearchHistory();
        }
    }

    deleteSavedSearch(searchId) {
        this.savedSearches = this.savedSearches.filter(s => s.id !== searchId);
        this.saveSavedSearches();
        this.updateSavedSearchesList();
    }

    loadHistorySearch(itemId) {
        const historyItem = this.searchHistory.find(h => h.id === itemId);
        if (historyItem) {
            this.applyCriteria(historyItem.criteria);
            this.performSearch();
        }
    }

    // Export and print functions
    exportResults() {
        if (this.searchResults.length === 0) {
            this.showAlert('warning', 'لا توجد نتائج للتصدير');
            return;
        }

        const csvContent = this.generateCSV();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `search_results_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
    }

    generateCSV() {
        const headers = ['اسم الملف', 'التصنيف', 'تاريخ الإنشاء', 'تاريخ الإصدار', 'الحجم', 'الوصف'];
        const rows = this.searchResults.map(file => [
            file.name,
            file.category,
            this.formatDate(file.createdAt),
            file.issueDate,
            this.formatFileSize(file.size),
            file.description || ''
        ]);

        return [headers, ...rows].map(row => 
            row.map(field => `"${field}"`).join(',')
        ).join('\n');
    }

    printResults() {
        if (this.searchResults.length === 0) {
            this.showAlert('warning', 'لا توجد نتائج للطباعة');
            return;
        }

        const printWindow = window.open('', '_blank');
        printWindow.document.write(this.generatePrintHTML());
        printWindow.document.close();
        setTimeout(() => printWindow.print(), 1000);
    }

    generatePrintHTML() {
        return `
            <html>
                <head>
                    <title>نتائج البحث</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 10px; }
                        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background-color: #f2f2f2; font-weight: bold; }
                        .summary { margin-bottom: 20px; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>نتائج البحث</h2>
                        <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                    </div>
                    <div class="summary">
                        <p><strong>عدد النتائج:</strong> ${this.searchResults.length} ملف</p>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>اسم الملف</th>
                                <th>التصنيف</th>
                                <th>تاريخ الإنشاء</th>
                                <th>تاريخ الإصدار</th>
                                <th>الحجم</th>
                                <th>الوصف</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${this.searchResults.map(file => `
                                <tr>
                                    <td>${file.name}</td>
                                    <td>${file.category}</td>
                                    <td>${this.formatDate(file.createdAt)}</td>
                                    <td>${file.issueDate}</td>
                                    <td>${this.formatFileSize(file.size)}</td>
                                    <td>${file.description || ''}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </body>
            </html>
        `;
    }
}

// Initialize search system
let searchSystem;

// Initialize immediately if DOM is ready, otherwise wait
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeSearchSystem);
} else {
    initializeSearchSystem();
}

function initializeSearchSystem() {
    console.log('Initializing SearchSystem...');
    try {
        searchSystem = new SearchSystem();
        window.searchSystem = searchSystem;
        console.log('SearchSystem initialized successfully');
    } catch (error) {
        console.error('Error initializing SearchSystem:', error);
    }
}

// Export for use in other modules
window.SearchSystem = SearchSystem;
