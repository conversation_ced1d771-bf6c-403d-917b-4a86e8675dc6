<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار معاينة الملفات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .preview-area {
            border: 2px dashed #ddd;
            padding: 20px;
            text-align: center;
            margin: 10px 0;
            min-height: 200px;
        }
        .file-input {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 100%;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            max-height: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار معاينة الملفات</h1>
        
        <div class="test-section">
            <h3>رفع ملف للاختبار</h3>
            <input type="file" id="testFile" class="file-input" accept="image/*,.pdf">
            <button onclick="testFileUpload()">اختبار رفع الملف</button>
        </div>
        
        <div class="test-section">
            <h3>معاينة الملف</h3>
            <div id="previewArea" class="preview-area">
                اختر ملفاً لمعاينته
            </div>
        </div>
        
        <div class="test-section">
            <h3>معلومات الملف</h3>
            <pre id="fileInfo">لا توجد معلومات</pre>
        </div>
        
        <div class="test-section">
            <h3>اختبار الملفات المحفوظة</h3>
            <button onclick="testSavedFiles()">اختبار الملفات المحفوظة</button>
            <div id="savedFilesInfo"></div>
        </div>
    </div>

    <script src="assets/js/database.js"></script>
    <script>
        function log(message) {
            console.log(message);
        }

        async function testFileUpload() {
            const fileInput = document.getElementById('testFile');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('يرجى اختيار ملف');
                return;
            }
            
            log('Testing file: ' + file.name);
            
            try {
                // Convert to base64
                const base64Content = await fileToBase64(file);
                
                // Display file info
                const fileInfo = {
                    name: file.name,
                    type: file.type,
                    size: file.size,
                    base64Length: base64Content.length,
                    base64Preview: base64Content.substring(0, 100) + '...'
                };
                
                document.getElementById('fileInfo').textContent = JSON.stringify(fileInfo, null, 2);
                
                // Test preview
                testPreview(file, base64Content);
                
            } catch (error) {
                console.error('Error testing file:', error);
                alert('خطأ في اختبار الملف: ' + error.message);
            }
        }

        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        }

        function testPreview(file, base64Content) {
            const previewArea = document.getElementById('previewArea');
            
            if (file.type.includes('image')) {
                previewArea.innerHTML = `
                    <h4>معاينة الصورة:</h4>
                    <img src="${base64Content}" style="max-width: 100%; max-height: 400px;" 
                         onload="console.log('✅ Image loaded successfully')"
                         onerror="console.error('❌ Image failed to load'); alert('فشل في تحميل الصورة');">
                `;
            } else if (file.type.includes('pdf')) {
                previewArea.innerHTML = `
                    <h4>معاينة PDF:</h4>
                    <div class="pdf-test-controls mb-3">
                        <button onclick="testPdfBlob('${base64Content}', '${file.name}')" class="btn btn-sm btn-primary me-2">
                            اختبار Blob URL
                        </button>
                        <button onclick="testPdfDownload('${base64Content}', '${file.name}')" class="btn btn-sm btn-secondary me-2">
                            اختبار التحميل
                        </button>
                        <button onclick="testPdfInline('${base64Content}', '${file.name}')" class="btn btn-sm btn-success">
                            عرض مضمن
                        </button>
                    </div>
                    <div id="pdf-test-area" style="border: 1px solid #ddd; min-height: 400px; padding: 20px; text-align: center;">
                        <p>انقر على أحد الأزرار أعلاه لاختبار عرض PDF</p>
                    </div>
                `;
            } else {
                previewArea.innerHTML = `
                    <h4>نوع الملف غير مدعوم للمعاينة</h4>
                    <p>نوع الملف: ${file.type}</p>
                `;
            }
        }

        async function testSavedFiles() {
            try {
                if (!window.localDB) {
                    alert('قاعدة البيانات غير متوفرة');
                    return;
                }
                
                const files = await window.localDB.getFiles();
                const infoDiv = document.getElementById('savedFilesInfo');
                
                if (files.length === 0) {
                    infoDiv.innerHTML = '<p>لا توجد ملفات محفوظة</p>';
                    return;
                }
                
                let html = '<h4>الملفات المحفوظة:</h4>';
                files.forEach((file, index) => {
                    html += `
                        <div style="border: 1px solid #ddd; margin: 10px 0; padding: 10px;">
                            <strong>${file.name}</strong> (${file.type})<br>
                            <small>المحتوى: ${file.content ? 'متوفر' : 'غير متوفر'}</small><br>
                            <button onclick="testSavedFilePreview('${file.id}')">اختبار المعاينة</button>
                        </div>
                    `;
                });
                
                infoDiv.innerHTML = html;
                
            } catch (error) {
                console.error('Error testing saved files:', error);
                alert('خطأ في اختبار الملفات المحفوظة: ' + error.message);
            }
        }

        async function testSavedFilePreview(fileId) {
            try {
                const file = await window.localDB.getFileById(fileId);
                if (!file) {
                    alert('الملف غير موجود');
                    return;
                }
                
                console.log('Testing saved file preview:', file);
                
                const previewArea = document.getElementById('previewArea');
                
                if (!file.content) {
                    previewArea.innerHTML = '<p style="color: red;">الملف لا يحتوي على محتوى</p>';
                    return;
                }
                
                let contentUrl = file.content;
                if (!file.content.startsWith('data:') && file.content.length > 100) {
                    contentUrl = `data:${file.type};base64,${file.content}`;
                }
                
                if (file.type.includes('image')) {
                    previewArea.innerHTML = `
                        <h4>معاينة الصورة المحفوظة:</h4>
                        <img src="${contentUrl}" style="max-width: 100%; max-height: 400px;" 
                             onload="console.log('✅ Saved image loaded successfully')"
                             onerror="console.error('❌ Saved image failed to load'); alert('فشل في تحميل الصورة المحفوظة');">
                    `;
                } else if (file.type.includes('pdf')) {
                    previewArea.innerHTML = `
                        <h4>معاينة PDF المحفوظ:</h4>
                        <iframe src="${contentUrl}" width="100%" height="400px" style="border: 1px solid #ddd;"
                                onload="console.log('✅ Saved PDF loaded successfully')"
                                onerror="console.error('❌ Saved PDF failed to load'); alert('فشل في تحميل PDF المحفوظ');">
                        </iframe>
                    `;
                }
                
            } catch (error) {
                console.error('Error testing saved file preview:', error);
                alert('خطأ في اختبار معاينة الملف المحفوظ: ' + error.message);
            }
        }

        // PDF testing functions using Blob URLs
        function createPdfBlob(base64Content) {
            try {
                console.log('Creating PDF blob from base64...');

                // Remove data URL prefix if present
                let base64Data = base64Content;
                if (base64Data.startsWith('data:')) {
                    base64Data = base64Data.split(',')[1];
                }

                // Convert base64 to binary
                const binaryString = atob(base64Data);
                const bytes = new Uint8Array(binaryString.length);

                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }

                // Create blob
                const blob = new Blob([bytes], { type: 'application/pdf' });
                const blobUrl = URL.createObjectURL(blob);

                console.log('✅ PDF Blob created successfully:', blobUrl);
                return blobUrl;

            } catch (error) {
                console.error('❌ Error creating PDF blob:', error);
                return null;
            }
        }

        function testPdfBlob(base64Content, fileName) {
            const testArea = document.getElementById('pdf-test-area');
            const blobUrl = createPdfBlob(base64Content);

            if (blobUrl) {
                // Open in new tab
                const newWindow = window.open(blobUrl, '_blank');

                if (newWindow) {
                    testArea.innerHTML = `
                        <div class="alert alert-success">
                            <h5>✅ تم فتح PDF باستخدام Blob URL</h5>
                            <p>تحقق من التبويب الجديد لرؤية PDF</p>
                            <small>Blob URL: ${blobUrl.substring(0, 50)}...</small>
                        </div>
                    `;

                    // Clean up after 5 minutes
                    setTimeout(() => {
                        URL.revokeObjectURL(blobUrl);
                        console.log('Blob URL cleaned up');
                    }, 300000);
                } else {
                    testArea.innerHTML = `
                        <div class="alert alert-danger">
                            <h5>❌ لا يمكن فتح نافذة جديدة</h5>
                            <p>تحقق من إعدادات المتصفح</p>
                        </div>
                    `;
                    URL.revokeObjectURL(blobUrl);
                }
            } else {
                testArea.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>❌ فشل في إنشاء Blob URL</h5>
                        <p>تحقق من Console للتفاصيل</p>
                    </div>
                `;
            }
        }

        function testPdfDownload(base64Content, fileName) {
            const testArea = document.getElementById('pdf-test-area');
            const blobUrl = createPdfBlob(base64Content);

            if (blobUrl) {
                const link = document.createElement('a');
                link.href = blobUrl;
                link.download = fileName;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                testArea.innerHTML = `
                    <div class="alert alert-success">
                        <h5>✅ تم بدء تحميل PDF</h5>
                        <p>تحقق من مجلد التحميلات</p>
                    </div>
                `;

                // Clean up
                setTimeout(() => {
                    URL.revokeObjectURL(blobUrl);
                }, 1000);
            } else {
                testArea.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>❌ فشل في تحميل PDF</h5>
                        <p>تحقق من Console للتفاصيل</p>
                    </div>
                `;
            }
        }

        function testPdfInline(base64Content, fileName) {
            const testArea = document.getElementById('pdf-test-area');
            const blobUrl = createPdfBlob(base64Content);

            if (blobUrl) {
                testArea.innerHTML = `
                    <h5>عرض PDF مضمن:</h5>
                    <iframe src="${blobUrl}" width="100%" height="350px" style="border: 1px solid #ccc;"
                            onload="console.log('✅ PDF loaded inline'); document.getElementById('inline-status').innerHTML='✅ تم التحميل بنجاح'"
                            onerror="console.error('❌ PDF failed to load inline'); document.getElementById('inline-status').innerHTML='❌ فشل التحميل'">
                    </iframe>
                    <div id="inline-status" class="mt-2">جاري التحميل...</div>
                `;

                // Clean up after 10 minutes
                setTimeout(() => {
                    URL.revokeObjectURL(blobUrl);
                    console.log('Inline PDF blob URL cleaned up');
                }, 600000);
            } else {
                testArea.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>❌ فشل في عرض PDF</h5>
                        <p>تحقق من Console للتفاصيل</p>
                    </div>
                `;
            }
        }

        // Auto-test on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (window.localDB) {
                    testSavedFiles();
                }
            }, 1000);
        });
    </script>
</body>
</html>
