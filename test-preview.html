<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار معاينة الملفات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .preview-area {
            border: 2px dashed #ddd;
            padding: 20px;
            text-align: center;
            margin: 10px 0;
            min-height: 200px;
        }
        .file-input {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 100%;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            max-height: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار معاينة الملفات</h1>
        
        <div class="test-section">
            <h3>رفع ملف للاختبار</h3>
            <input type="file" id="testFile" class="file-input" accept="image/*,.pdf">
            <button onclick="testFileUpload()">اختبار رفع الملف</button>
        </div>
        
        <div class="test-section">
            <h3>معاينة الملف</h3>
            <div id="previewArea" class="preview-area">
                اختر ملفاً لمعاينته
            </div>
        </div>
        
        <div class="test-section">
            <h3>معلومات الملف</h3>
            <pre id="fileInfo">لا توجد معلومات</pre>
        </div>
        
        <div class="test-section">
            <h3>اختبار الملفات المحفوظة</h3>
            <button onclick="testSavedFiles()">اختبار الملفات المحفوظة</button>
            <div id="savedFilesInfo"></div>
        </div>
    </div>

    <script src="assets/js/database.js"></script>
    <script>
        function log(message) {
            console.log(message);
        }

        async function testFileUpload() {
            const fileInput = document.getElementById('testFile');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('يرجى اختيار ملف');
                return;
            }
            
            log('Testing file: ' + file.name);
            
            try {
                // Convert to base64
                const base64Content = await fileToBase64(file);
                
                // Display file info
                const fileInfo = {
                    name: file.name,
                    type: file.type,
                    size: file.size,
                    base64Length: base64Content.length,
                    base64Preview: base64Content.substring(0, 100) + '...'
                };
                
                document.getElementById('fileInfo').textContent = JSON.stringify(fileInfo, null, 2);
                
                // Test preview
                testPreview(file, base64Content);
                
            } catch (error) {
                console.error('Error testing file:', error);
                alert('خطأ في اختبار الملف: ' + error.message);
            }
        }

        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        }

        function testPreview(file, base64Content) {
            const previewArea = document.getElementById('previewArea');
            
            if (file.type.includes('image')) {
                previewArea.innerHTML = `
                    <h4>معاينة الصورة:</h4>
                    <img src="${base64Content}" style="max-width: 100%; max-height: 400px;" 
                         onload="console.log('✅ Image loaded successfully')"
                         onerror="console.error('❌ Image failed to load'); alert('فشل في تحميل الصورة');">
                `;
            } else if (file.type.includes('pdf')) {
                previewArea.innerHTML = `
                    <h4>معاينة PDF:</h4>
                    <div class="pdf-test-controls mb-3">
                        <button onclick="testPdfIframe('${base64Content}', '${file.name}')" class="btn btn-sm btn-primary me-2">
                            اختبار Iframe
                        </button>
                        <button onclick="testPdfEmbed('${base64Content}', '${file.name}')" class="btn btn-sm btn-secondary me-2">
                            اختبار Embed
                        </button>
                        <button onclick="testPdfNewTab('${base64Content}', '${file.name}')" class="btn btn-sm btn-success">
                            فتح في تبويب جديد
                        </button>
                    </div>
                    <div id="pdf-test-area" style="border: 1px solid #ddd; min-height: 400px; padding: 20px; text-align: center;">
                        <p>انقر على أحد الأزرار أعلاه لاختبار عرض PDF</p>
                    </div>
                `;
            } else {
                previewArea.innerHTML = `
                    <h4>نوع الملف غير مدعوم للمعاينة</h4>
                    <p>نوع الملف: ${file.type}</p>
                `;
            }
        }

        async function testSavedFiles() {
            try {
                if (!window.localDB) {
                    alert('قاعدة البيانات غير متوفرة');
                    return;
                }
                
                const files = await window.localDB.getFiles();
                const infoDiv = document.getElementById('savedFilesInfo');
                
                if (files.length === 0) {
                    infoDiv.innerHTML = '<p>لا توجد ملفات محفوظة</p>';
                    return;
                }
                
                let html = '<h4>الملفات المحفوظة:</h4>';
                files.forEach((file, index) => {
                    html += `
                        <div style="border: 1px solid #ddd; margin: 10px 0; padding: 10px;">
                            <strong>${file.name}</strong> (${file.type})<br>
                            <small>المحتوى: ${file.content ? 'متوفر' : 'غير متوفر'}</small><br>
                            <button onclick="testSavedFilePreview('${file.id}')">اختبار المعاينة</button>
                        </div>
                    `;
                });
                
                infoDiv.innerHTML = html;
                
            } catch (error) {
                console.error('Error testing saved files:', error);
                alert('خطأ في اختبار الملفات المحفوظة: ' + error.message);
            }
        }

        async function testSavedFilePreview(fileId) {
            try {
                const file = await window.localDB.getFileById(fileId);
                if (!file) {
                    alert('الملف غير موجود');
                    return;
                }
                
                console.log('Testing saved file preview:', file);
                
                const previewArea = document.getElementById('previewArea');
                
                if (!file.content) {
                    previewArea.innerHTML = '<p style="color: red;">الملف لا يحتوي على محتوى</p>';
                    return;
                }
                
                let contentUrl = file.content;
                if (!file.content.startsWith('data:') && file.content.length > 100) {
                    contentUrl = `data:${file.type};base64,${file.content}`;
                }
                
                if (file.type.includes('image')) {
                    previewArea.innerHTML = `
                        <h4>معاينة الصورة المحفوظة:</h4>
                        <img src="${contentUrl}" style="max-width: 100%; max-height: 400px;" 
                             onload="console.log('✅ Saved image loaded successfully')"
                             onerror="console.error('❌ Saved image failed to load'); alert('فشل في تحميل الصورة المحفوظة');">
                    `;
                } else if (file.type.includes('pdf')) {
                    previewArea.innerHTML = `
                        <h4>معاينة PDF المحفوظ:</h4>
                        <iframe src="${contentUrl}" width="100%" height="400px" style="border: 1px solid #ddd;"
                                onload="console.log('✅ Saved PDF loaded successfully')"
                                onerror="console.error('❌ Saved PDF failed to load'); alert('فشل في تحميل PDF المحفوظ');">
                        </iframe>
                    `;
                }
                
            } catch (error) {
                console.error('Error testing saved file preview:', error);
                alert('خطأ في اختبار معاينة الملف المحفوظ: ' + error.message);
            }
        }

        // PDF testing functions
        function testPdfIframe(base64Content, fileName) {
            const testArea = document.getElementById('pdf-test-area');
            testArea.innerHTML = `
                <h5>اختبار Iframe:</h5>
                <iframe src="${base64Content}" width="100%" height="350px" style="border: 1px solid #ccc;"
                        onload="console.log('✅ PDF Iframe loaded'); document.getElementById('iframe-status').innerHTML='✅ تم التحميل بنجاح'"
                        onerror="console.error('❌ PDF Iframe failed'); document.getElementById('iframe-status').innerHTML='❌ فشل التحميل'">
                </iframe>
                <div id="iframe-status" class="mt-2">جاري التحميل...</div>
            `;
        }

        function testPdfEmbed(base64Content, fileName) {
            const testArea = document.getElementById('pdf-test-area');
            testArea.innerHTML = `
                <h5>اختبار Embed:</h5>
                <embed src="${base64Content}" type="application/pdf" width="100%" height="350px">
                <div class="mt-2">
                    <small>إذا لم تشاهد PDF أعلاه، فإن متصفحك لا يدعم عرض PDF مباشرة</small>
                </div>
            `;
        }

        function testPdfNewTab(base64Content, fileName) {
            try {
                const newWindow = window.open('', '_blank');
                if (newWindow) {
                    newWindow.document.write(`
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>${fileName}</title>
                            <style>
                                body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
                                .header { background: #f8f9fa; padding: 10px; text-align: center; border-bottom: 1px solid #ddd; }
                                iframe { width: 100%; height: calc(100vh - 60px); border: none; }
                                .fallback { text-align: center; padding: 50px; }
                            </style>
                        </head>
                        <body>
                            <div class="header">
                                <h3>${fileName}</h3>
                                <small>إذا لم يظهر PDF، <a href="${base64Content}" download="${fileName}">انقر هنا للتحميل</a></small>
                            </div>
                            <iframe src="${base64Content}" onload="console.log('PDF loaded in new tab')"
                                    onerror="document.querySelector('.fallback').style.display='block'">
                            </iframe>
                            <div class="fallback" style="display: none;">
                                <h4>لا يمكن عرض PDF</h4>
                                <p>متصفحك لا يدعم عرض PDF مباشرة</p>
                                <a href="${base64Content}" download="${fileName}" class="btn btn-primary">تحميل الملف</a>
                            </div>
                        </body>
                        </html>
                    `);
                    newWindow.document.close();

                    const testArea = document.getElementById('pdf-test-area');
                    testArea.innerHTML = `
                        <div class="alert alert-success">
                            <h5>✅ تم فتح PDF في تبويب جديد</h5>
                            <p>تحقق من التبويب الجديد لرؤية PDF</p>
                        </div>
                    `;
                } else {
                    alert('لا يمكن فتح نافذة جديدة. تحقق من إعدادات المتصفح.');
                }
            } catch (error) {
                console.error('Error opening PDF in new tab:', error);
                alert('خطأ في فتح PDF في تبويب جديد: ' + error.message);
            }
        }

        // Auto-test on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (window.localDB) {
                    testSavedFiles();
                }
            }, 1000);
        });
    </script>
</body>
</html>
