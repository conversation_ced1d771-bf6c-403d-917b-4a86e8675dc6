/* ===== Enhanced Login Page Styles ===== */

/* ===== Animated Background ===== */
.animated-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--primary-gradient);
    z-index: -2;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 20s infinite linear;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 10%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    bottom: 10%;
    right: 20%;
    animation-delay: 6s;
}

.shape-5 {
    width: 40px;
    height: 40px;
    top: 50%;
    left: 5%;
    animation-delay: 8s;
}

.shape-6 {
    width: 90px;
    height: 90px;
    top: 70%;
    right: 5%;
    animation-delay: 10s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

.gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, 
        rgba(37, 99, 235, 0.1) 0%, 
        rgba(139, 92, 246, 0.1) 50%, 
        rgba(59, 130, 246, 0.1) 100%);
    z-index: -1;
}

/* ===== Main Login Container ===== */
.login-page {
    min-height: 100vh;
    overflow: hidden;
    position: relative;
}

.login-container {
    display: flex;
    min-height: 100vh;
    position: relative;
    z-index: 1;
}

/* ===== Left Side - Branding ===== */
.login-branding {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.branding-content {
    text-align: center;
    max-width: 500px;
}

.brand-logo {
    position: relative;
    display: inline-block;
    margin-bottom: 2rem;
}

.logo-circle {
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    position: relative;
    z-index: 2;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    animation: pulse 3s infinite;
}

.logo-rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ring {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    animation: rotate 10s linear infinite;
}

.ring-1 {
    width: 140px;
    height: 140px;
    top: -70px;
    left: -70px;
}

.ring-2 {
    width: 160px;
    height: 160px;
    top: -80px;
    left: -80px;
    animation-delay: 2s;
}

.ring-3 {
    width: 180px;
    height: 180px;
    top: -90px;
    left: -90px;
    animation-delay: 4s;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.brand-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.brand-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.brand-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
}

.feature-item i {
    width: 24px;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
}

/* ===== Right Side - Login Form ===== */
.login-form-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
}

.login-card {
    width: 100%;
    max-width: 450px;
    background: var(--bg-primary);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

/* ===== Header Controls ===== */
.card-header-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.theme-toggle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-secondary);
}

.theme-toggle:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.language-switcher {
    display: flex;
    gap: 0.5rem;
}

.lang-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 0.9rem;
}

.lang-btn.active,
.lang-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

/* ===== Login Header ===== */
.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.welcome-text h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.welcome-text p {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* ===== Enhanced Form Styles ===== */
.login-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.input-wrapper {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    z-index: 2;
    transition: var(--transition);
}

.input-wrapper input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition);
    outline: none;
}

.input-wrapper input:focus {
    border-color: var(--primary-color);
    background: var(--bg-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input-wrapper input:focus + .input-focus-line {
    transform: scaleX(1);
}

.input-wrapper input:focus ~ .input-icon {
    color: var(--primary-color);
}

.input-focus-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
    border-radius: 0 0 12px 12px;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: var(--transition);
    z-index: 2;
}

.password-toggle:hover {
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

/* ===== Form Options ===== */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.custom-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.custom-checkbox input {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    margin-right: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    background: var(--bg-secondary);
}

.custom-checkbox input:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkmark i {
    color: white;
    font-size: 0.8rem;
    opacity: 0;
    transition: var(--transition);
}

.custom-checkbox input:checked + .checkmark i {
    opacity: 1;
}

.checkbox-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: var(--transition);
}

.forgot-password:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* ===== Enhanced Login Button ===== */
.login-btn {
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: 12px;
    background: var(--primary-gradient);
    color: white;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--transition);
    margin-bottom: 1.5rem;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

.login-btn:active {
    transform: translateY(0);
}

.btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: var(--transition);
}

.btn-loader,
.btn-success {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: var(--transition);
}

.login-btn.loading .btn-content {
    opacity: 0;
}

.login-btn.loading .btn-loader {
    opacity: 1;
}

.login-btn.success .btn-content,
.login-btn.success .btn-loader {
    opacity: 0;
}

.login-btn.success .btn-success {
    opacity: 1;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== Demo Accounts ===== */
.demo-accounts {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.demo-title {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.demo-buttons {
    display: flex;
    gap: 0.75rem;
}

.demo-btn {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.85rem;
}

.demo-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* ===== Loading Overlay ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-overlay.show {
    display: flex;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
}

.spinner-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 4px solid transparent;
    border-top: 4px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
    width: 60px;
    height: 60px;
    top: 10px;
    left: 10px;
    animation-delay: 0.1s;
    border-top-color: rgba(255, 255, 255, 0.6);
}

.spinner-ring:nth-child(3) {
    width: 40px;
    height: 40px;
    top: 20px;
    left: 20px;
    animation-delay: 0.2s;
    border-top-color: rgba(255, 255, 255, 0.4);
}

.loading-text {
    font-size: 1.1rem;
    font-weight: 500;
}

/* ===== Success Animation ===== */
.success-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.success-animation.show {
    display: flex;
}

.success-checkmark {
    margin-bottom: 1rem;
}

.check-icon {
    width: 80px;
    height: 80px;
    position: relative;
    border-radius: 50%;
    background: var(--success-color);
    margin: 0 auto;
}

.icon-line {
    height: 4px;
    background: white;
    display: block;
    border-radius: 2px;
    position: absolute;
    z-index: 10;
}

.line-tip {
    top: 46px;
    left: 14px;
    width: 25px;
    transform: rotate(45deg);
    animation: icon-line-tip 0.75s;
}

.line-long {
    top: 38px;
    right: 8px;
    width: 47px;
    transform: rotate(-45deg);
    animation: icon-line-long 0.75s;
}

.icon-circle {
    top: -4px;
    left: -4px;
    z-index: 10;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    position: absolute;
    border: 4px solid rgba(5, 150, 105, 0.5);
    animation: icon-circle 0.6s ease-in-out;
}

.icon-fix {
    top: 8px;
    width: 5px;
    left: 26px;
    z-index: 1;
    height: 85px;
    position: absolute;
    transform: rotate(-45deg);
    background: white;
}

@keyframes icon-line-tip {
    0% { width: 0; left: 1px; top: 19px; }
    54% { width: 0; left: 1px; top: 19px; }
    70% { width: 50px; left: -8px; top: 37px; }
    84% { width: 17px; left: 21px; top: 48px; }
    100% { width: 25px; left: 14px; top: 46px; }
}

@keyframes icon-line-long {
    0% { width: 0; right: 46px; top: 54px; }
    65% { width: 0; right: 46px; top: 54px; }
    84% { width: 55px; right: 0px; top: 35px; }
    100% { width: 47px; right: 8px; top: 38px; }
}

@keyframes icon-circle {
    0% { transform: scale(0); }
    25% { transform: scale(1.1); }
    50% { transform: scale(1); }
}

.success-text {
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    text-align: center;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .login-container {
        flex-direction: column;
    }

    .login-branding {
        flex: none;
        padding: 2rem 1rem;
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .brand-title {
        font-size: 2rem;
    }

    .brand-subtitle {
        font-size: 1rem;
    }

    .login-form-container {
        padding: 1rem;
    }

    .login-card {
        padding: 2rem 1.5rem;
    }

    .demo-buttons {
        flex-direction: column;
    }

    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}
