// ===== User Management System =====

class UserManagement {
    constructor() {
        this.users = [];
        this.currentUser = null;
        this.initializeUserManagement();
    }

    // Initialize user management
    initializeUserManagement() {
        this.loadUsers();
        this.getCurrentUser();
        this.createUserInterface();
        this.setupUserEvents();
    }

    // Backup password hashing function
    hashPassword(password) {
        return btoa(password + 'archive_salt');
    }

    // Load users from storage
    loadUsers() {
        this.users = JSON.parse(localStorage.getItem('archive_users') || '[]');
    }

    // Get current user
    getCurrentUser() {
        this.currentUser = window.authSystem?.currentUser;
    }

    // Create user management interface
    createUserInterface() {
        const userHTML = `
            <div class="user-management-container">
                <div class="management-header">
                    <h4><i class="fas fa-users me-2"></i>إدارة المستخدمين</h4>
                    <p class="text-muted">إدارة حسابات المستخدمين وصلاحياتهم</p>
                </div>

                <div class="user-actions mb-4">
                    <button class="btn btn-primary" onclick="userManagement.showAddUserModal()">
                        <i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد
                    </button>
                    <button class="btn btn-outline-secondary" onclick="userManagement.exportUsers()">
                        <i class="fas fa-download me-2"></i>تصدير قائمة المستخدمين
                    </button>
                    <button class="btn btn-outline-info" onclick="userManagement.refreshUsers()">
                        <i class="fas fa-sync me-2"></i>تحديث
                    </button>
                </div>

                <div class="users-stats mb-4">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-icon bg-primary">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 id="totalUsersCount">0</h3>
                                    <p>إجمالي المستخدمين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-icon bg-success">
                                    <i class="fas fa-user-check"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 id="activeUsersCount">0</h3>
                                    <p>المستخدمين النشطين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-icon bg-warning">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 id="adminUsersCount">0</h3>
                                    <p>المديرين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <div class="stat-icon bg-info">
                                    <i class="fas fa-user-clock"></i>
                                </div>
                                <div class="stat-info">
                                    <h3 id="onlineUsersCount">1</h3>
                                    <p>متصل الآن</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="users-table-container">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6>قائمة المستخدمين</h6>
                                <div class="table-controls">
                                    <input type="text" class="form-control form-control-sm"
                                           id="userSearchInput" placeholder="البحث في المستخدمين...">
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0" id="usersTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th>المستخدم</th>
                                            <th>الدور</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>آخر دخول</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="usersTableBody">
                                        <!-- Users will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const usersSection = document.getElementById('users-section');
        if (usersSection) {
            usersSection.innerHTML = userHTML;
            this.loadUsersTable();
            this.updateUserStats();
        }
    }

    // Setup user management events
    setupUserEvents() {
        // Search functionality
        const searchInput = document.getElementById('userSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', () => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.filterUsers(searchInput.value);
                }, 300);
            });
        }
    }

    // Load users table
    loadUsersTable() {
        const tbody = document.getElementById('usersTableBody');
        if (!tbody) return;

        if (this.users.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <div class="empty-state">
                            <i class="fas fa-users text-muted"></i>
                            <p class="text-muted mt-2">لا توجد مستخدمين</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.users.map(user => this.renderUserRow(user)).join('');
    }

    // Render user row
    renderUserRow(user) {
        const isCurrentUser = this.currentUser && this.currentUser.id === user.id;
        const canEdit = this.currentUser && (this.currentUser.role === 'admin' || isCurrentUser);
        const canDelete = this.currentUser && this.currentUser.role === 'admin' && !isCurrentUser;

        return `
            <tr ${isCurrentUser ? 'class="table-primary"' : ''}>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="user-avatar me-3">
                            <i class="fas fa-user-circle text-primary" style="font-size: 2rem;"></i>
                        </div>
                        <div>
                            <div class="fw-bold">${user.name}</div>
                            <small class="text-muted">@${user.username}</small>
                            ${isCurrentUser ? '<span class="badge bg-primary ms-2">أنت</span>' : ''}
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge ${user.role === 'admin' ? 'bg-danger' : 'bg-secondary'}">
                        ${this.getRoleDisplayName(user.role)}
                    </span>
                </td>
                <td>${user.email || 'غير محدد'}</td>
                <td>${this.formatDate(user.createdAt)}</td>
                <td>
                    ${user.lastLogin ?
                        `<span class="text-success">${this.formatDate(user.lastLogin)}</span>` :
                        '<span class="text-muted">لم يسجل دخول</span>'
                    }
                </td>
                <td>
                    <span class="badge ${user.isActive ? 'bg-success' : 'bg-secondary'}">
                        ${user.isActive ? 'نشط' : 'معطل'}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        ${canEdit ? `
                            <button class="btn btn-outline-primary" onclick="userManagement.editUser(${user.id})"
                                    title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-outline-info" onclick="userManagement.viewUserDetails(${user.id})"
                                title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${canDelete ? `
                            <button class="btn btn-outline-danger" onclick="userManagement.deleteUser(${user.id})"
                                    title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                        ${this.currentUser && this.currentUser.role === 'admin' ? `
                            <button class="btn btn-outline-warning" onclick="userManagement.toggleUserStatus(${user.id})"
                                    title="${user.isActive ? 'تعطيل' : 'تفعيل'}">
                                <i class="fas fa-${user.isActive ? 'ban' : 'check'}"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    }

    // Update user statistics
    updateUserStats() {
        const totalUsers = this.users.length;
        const activeUsers = this.users.filter(u => u.isActive).length;
        const adminUsers = this.users.filter(u => u.role === 'admin').length;

        document.getElementById('totalUsersCount').textContent = totalUsers;
        document.getElementById('activeUsersCount').textContent = activeUsers;
        document.getElementById('adminUsersCount').textContent = adminUsers;
    }

    // Show add user modal
    showAddUserModal() {
        const modalHTML = `
            <div class="user-modal" id="addUserModal">
                <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h5>إضافة مستخدم جديد</h5>
                        <button class="btn-close" onclick="this.closest('.user-modal').remove()">×</button>
                    </div>
                    <div class="modal-body">
                        <form id="addUserForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اسم المستخدم *</label>
                                    <input type="text" class="form-control" name="username" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الدور *</label>
                                    <select class="form-control" name="role" required>
                                        <option value="employee">موظف</option>
                                        <option value="admin">مدير</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">كلمة المرور *</label>
                                    <input type="password" class="form-control" name="password" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تأكيد كلمة المرور *</label>
                                    <input type="password" class="form-control" name="confirmPassword" required>
                                </div>
                                <div class="col-12 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="isActive" checked>
                                        <label class="form-check-label">
                                            تفعيل المستخدم
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.user-modal').remove()">إلغاء</button>
                        <button class="btn btn-primary" onclick="userManagement.saveNewUser()">إضافة المستخدم</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    // Save new user
    saveNewUser() {
        const form = document.getElementById('addUserForm');
        const formData = new FormData(form);

        // Validate form
        const name = formData.get('name').trim();
        const username = formData.get('username').trim();
        const email = formData.get('email').trim();
        const role = formData.get('role');
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');
        const isActive = formData.has('isActive');

        if (!name || !username || !password) {
            this.showAlert('error', 'يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        if (password !== confirmPassword) {
            this.showAlert('error', 'كلمة المرور وتأكيدها غير متطابقتين');
            return;
        }

        if (password.length < 6) {
            this.showAlert('error', 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            return;
        }

        // Check if username exists
        if (this.users.find(u => u.username === username)) {
            this.showAlert('error', 'اسم المستخدم موجود بالفعل');
            return;
        }

        // Check if email exists
        if (email && this.users.find(u => u.email === email)) {
            this.showAlert('error', 'البريد الإلكتروني موجود بالفعل');
            return;
        }

        try {
            // Check for duplicate username
            if (this.users.some(u => u.username === username)) {
                throw new Error('اسم المستخدم موجود بالفعل');
            }

            // Hash password using authSystem or backup function
            let hashedPassword;
            if (window.authSystem && typeof window.authSystem.hashPassword === 'function') {
                hashedPassword = window.authSystem.hashPassword(password);
            } else {
                hashedPassword = this.hashPassword(password);
                console.warn('Using backup password hashing - authSystem not available');
            }

            // Create new user
            const newUser = {
                id: Date.now(),
                name: name,
                username: username,
                email: email || null,
                role: role,
                password: hashedPassword,
                isActive: isActive,
                createdAt: new Date().toISOString(),
                lastLogin: null
            };

            // Add to users array
            this.users.push(newUser);

            // Save to storage
            localStorage.setItem('archive_users', JSON.stringify(this.users));

            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('user_create', `إنشاء مستخدم جديد: ${newUser.name}`);
            }

            // Refresh interface
            this.loadUsersTable();
            this.updateUserStats();

            // Close modal
            document.getElementById('addUserModal').remove();

            this.showAlert('success', 'تم إضافة المستخدم بنجاح');

        } catch (error) {
            console.error('Error creating user:', error);

            // Show specific error message
            let errorMessage = 'حدث خطأ أثناء إضافة المستخدم';
            if (error.message) {
                errorMessage = error.message;
            }

            this.showAlert('error', errorMessage);
        }
    }

    // Edit user
    editUser(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) {
            this.showAlert('error', 'المستخدم غير موجود');
            return;
        }

        const modalHTML = `
            <div class="user-modal" id="editUserModal">
                <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h5>تعديل المستخدم: ${user.name}</h5>
                        <button class="btn-close" onclick="this.closest('.user-modal').remove()">×</button>
                    </div>
                    <div class="modal-body">
                        <form id="editUserForm">
                            <input type="hidden" name="userId" value="${user.id}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" name="name" value="${user.name}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اسم المستخدم *</label>
                                    <input type="text" class="form-control" name="username" value="${user.username}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" name="email" value="${user.email || ''}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الدور *</label>
                                    <select class="form-control" name="role" required>
                                        <option value="employee" ${user.role === 'employee' ? 'selected' : ''}>موظف</option>
                                        <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>مدير</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">كلمة المرور الجديدة</label>
                                    <input type="password" class="form-control" name="password" placeholder="اتركها فارغة للاحتفاظ بالحالية">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تأكيد كلمة المرور</label>
                                    <input type="password" class="form-control" name="confirmPassword">
                                </div>
                                <div class="col-12 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="isActive" ${user.isActive ? 'checked' : ''}>
                                        <label class="form-check-label">
                                            تفعيل المستخدم
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.user-modal').remove()">إلغاء</button>
                        <button class="btn btn-primary" onclick="userManagement.saveUserChanges()">حفظ التغييرات</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    // Save user changes
    saveUserChanges() {
        const form = document.getElementById('editUserForm');
        const formData = new FormData(form);

        const userId = parseInt(formData.get('userId'));
        const name = formData.get('name').trim();
        const username = formData.get('username').trim();
        const email = formData.get('email').trim();
        const role = formData.get('role');
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');
        const isActive = formData.has('isActive');

        if (!name || !username) {
            this.showAlert('error', 'يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        if (password && password !== confirmPassword) {
            this.showAlert('error', 'كلمة المرور وتأكيدها غير متطابقتين');
            return;
        }

        if (password && password.length < 6) {
            this.showAlert('error', 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            return;
        }

        // Check if username exists (excluding current user)
        if (this.users.find(u => u.username === username && u.id !== userId)) {
            this.showAlert('error', 'اسم المستخدم موجود بالفعل');
            return;
        }

        // Check if email exists (excluding current user)
        if (email && this.users.find(u => u.email === email && u.id !== userId)) {
            this.showAlert('error', 'البريد الإلكتروني موجود بالفعل');
            return;
        }

        try {
            // Find and update user
            const userIndex = this.users.findIndex(u => u.id === userId);
            if (userIndex === -1) {
                this.showAlert('error', 'المستخدم غير موجود');
                return;
            }

            const user = this.users[userIndex];
            user.name = name;
            user.username = username;
            user.email = email || null;
            user.role = role;
            user.isActive = isActive;

            if (password) {
                user.password = window.authSystem.hashPassword(password);
            }

            // Save to storage
            localStorage.setItem('archive_users', JSON.stringify(this.users));

            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('user_update', `تعديل المستخدم: ${user.name}`);
            }

            // Refresh interface
            this.loadUsersTable();
            this.updateUserStats();

            // Close modal
            document.getElementById('editUserModal').remove();

            this.showAlert('success', 'تم تحديث المستخدم بنجاح');

        } catch (error) {
            console.error('Error updating user:', error);
            this.showAlert('error', 'حدث خطأ أثناء تحديث المستخدم');
        }
    }

    // View user details
    viewUserDetails(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) {
            this.showAlert('error', 'المستخدم غير موجود');
            return;
        }

        // Get user activity logs
        const logs = JSON.parse(localStorage.getItem('archive_activity_logs') || '[]');
        const userLogs = logs.filter(log => log.userId === userId).slice(0, 10);

        const modalHTML = `
            <div class="user-modal" id="userDetailsModal">
                <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
                <div class="modal-content" style="max-width: 800px;">
                    <div class="modal-header">
                        <h5>تفاصيل المستخدم: ${user.name}</h5>
                        <button class="btn-close" onclick="this.closest('.user-modal').remove()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="user-info-card">
                                    <h6>المعلومات الأساسية</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>الاسم:</strong></td>
                                            <td>${user.name}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>اسم المستخدم:</strong></td>
                                            <td>@${user.username}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>البريد الإلكتروني:</strong></td>
                                            <td>${user.email || 'غير محدد'}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الدور:</strong></td>
                                            <td>
                                                <span class="badge ${user.role === 'admin' ? 'bg-danger' : 'bg-secondary'}">
                                                    ${this.getRoleDisplayName(user.role)}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة:</strong></td>
                                            <td>
                                                <span class="badge ${user.isActive ? 'bg-success' : 'bg-secondary'}">
                                                    ${user.isActive ? 'نشط' : 'معطل'}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الإنشاء:</strong></td>
                                            <td>${this.formatDate(user.createdAt)}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>آخر دخول:</strong></td>
                                            <td>${user.lastLogin ? this.formatDate(user.lastLogin) : 'لم يسجل دخول'}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="user-activity-card">
                                    <h6>النشاط الأخير</h6>
                                    <div class="activity-list" style="max-height: 300px; overflow-y: auto;">
                                        ${userLogs.length > 0 ? userLogs.map(log => `
                                            <div class="activity-item">
                                                <div class="activity-action">${log.description}</div>
                                                <div class="activity-time">${this.formatDate(log.timestamp)}</div>
                                            </div>
                                        `).join('') : '<p class="text-muted">لا يوجد نشاط مسجل</p>'}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.user-modal').remove()">إغلاق</button>
                        ${this.currentUser && (this.currentUser.role === 'admin' || this.currentUser.id === userId) ? `
                            <button class="btn btn-primary" onclick="this.closest('.user-modal').remove(); userManagement.editUser(${userId})">
                                تعديل المستخدم
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    // Delete user
    deleteUser(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) {
            this.showAlert('error', 'المستخدم غير موجود');
            return;
        }

        if (!confirm(`هل أنت متأكد من حذف المستخدم "${user.name}"؟\nلا يمكن التراجع عن هذا الإجراء.`)) {
            return;
        }

        try {
            // Remove user from array
            this.users = this.users.filter(u => u.id !== userId);

            // Save to storage
            localStorage.setItem('archive_users', JSON.stringify(this.users));

            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('user_delete', `حذف المستخدم: ${user.name}`);
            }

            // Refresh interface
            this.loadUsersTable();
            this.updateUserStats();

            this.showAlert('success', 'تم حذف المستخدم بنجاح');

        } catch (error) {
            console.error('Error deleting user:', error);
            this.showAlert('error', 'حدث خطأ أثناء حذف المستخدم');
        }
    }

    // Toggle user status
    toggleUserStatus(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) {
            this.showAlert('error', 'المستخدم غير موجود');
            return;
        }

        const action = user.isActive ? 'تعطيل' : 'تفعيل';
        if (!confirm(`هل أنت متأكد من ${action} المستخدم "${user.name}"؟`)) {
            return;
        }

        try {
            // Toggle status
            user.isActive = !user.isActive;

            // Save to storage
            localStorage.setItem('archive_users', JSON.stringify(this.users));

            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('user_status_change',
                    `${action} المستخدم: ${user.name}`);
            }

            // Refresh interface
            this.loadUsersTable();
            this.updateUserStats();

            this.showAlert('success', `تم ${action} المستخدم بنجاح`);

        } catch (error) {
            console.error('Error toggling user status:', error);
            this.showAlert('error', `حدث خطأ أثناء ${action} المستخدم`);
        }
    }

    // Filter users
    filterUsers(searchTerm) {
        const tbody = document.getElementById('usersTableBody');
        if (!tbody) return;

        const filteredUsers = this.users.filter(user => {
            const searchText = searchTerm.toLowerCase();
            return user.name.toLowerCase().includes(searchText) ||
                   user.username.toLowerCase().includes(searchText) ||
                   (user.email && user.email.toLowerCase().includes(searchText)) ||
                   this.getRoleDisplayName(user.role).toLowerCase().includes(searchText);
        });

        if (filteredUsers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <div class="empty-state">
                            <i class="fas fa-search text-muted"></i>
                            <p class="text-muted mt-2">لا توجد نتائج للبحث "${searchTerm}"</p>
                        </div>
                    </td>
                </tr>
            `;
        } else {
            tbody.innerHTML = filteredUsers.map(user => this.renderUserRow(user)).join('');
        }
    }

    // Export users
    exportUsers() {
        if (this.users.length === 0) {
            this.showAlert('warning', 'لا توجد مستخدمين للتصدير');
            return;
        }

        const csvContent = this.generateUsersCSV();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `users_export_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        // Log activity
        if (window.authSystem) {
            window.authSystem.logActivity('users_export', 'تصدير قائمة المستخدمين');
        }

        this.showAlert('success', 'تم تصدير قائمة المستخدمين بنجاح');
    }

    // Generate CSV for users
    generateUsersCSV() {
        const headers = ['الاسم', 'اسم المستخدم', 'البريد الإلكتروني', 'الدور', 'الحالة', 'تاريخ الإنشاء', 'آخر دخول'];
        const rows = this.users.map(user => [
            user.name,
            user.username,
            user.email || '',
            this.getRoleDisplayName(user.role),
            user.isActive ? 'نشط' : 'معطل',
            this.formatDate(user.createdAt),
            user.lastLogin ? this.formatDate(user.lastLogin) : 'لم يسجل دخول'
        ]);

        return [headers, ...rows].map(row =>
            row.map(field => `"${field}"`).join(',')
        ).join('\n');
    }

    // Refresh users
    refreshUsers() {
        this.loadUsers();
        this.loadUsersTable();
        this.updateUserStats();
        this.showAlert('success', 'تم تحديث قائمة المستخدمين');
    }

    // Helper functions
    getRoleDisplayName(role) {
        const roleNames = {
            admin: 'مدير النظام',
            employee: 'موظف'
        };
        return roleNames[role] || role;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    showAlert(type, message) {
        // Use the same alert system as file manager
        if (window.fileManager) {
            window.fileManager.showAlert(type, message);
        } else {
            // Fallback alert
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
    }
}

// ===== Activity Logs System =====

class ActivityLogsSystem {
    constructor() {
        this.logs = [];
        this.initializeLogsSystem();
    }

    // Initialize logs system
    initializeLogsSystem() {
        this.loadLogs();
        this.createLogsInterface();
    }

    // Load logs from storage
    loadLogs() {
        this.logs = JSON.parse(localStorage.getItem('archive_activity_logs') || '[]');
    }

    // Create logs interface
    createLogsInterface() {
        const logsHTML = `
            <div class="activity-logs-container">
                <div class="logs-header">
                    <h4><i class="fas fa-history me-2"></i>سجل العمليات</h4>
                    <p class="text-muted">تتبع جميع الأنشطة والعمليات في النظام</p>
                </div>

                <div class="logs-controls mb-4">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">البحث</label>
                            <input type="text" class="form-control" id="logsSearchInput" placeholder="ابحث في السجل...">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">نوع العملية</label>
                            <select class="form-control" id="logsActionFilter">
                                <option value="">جميع العمليات</option>
                                <option value="login">تسجيل دخول</option>
                                <option value="logout">تسجيل خروج</option>
                                <option value="file_upload">رفع ملف</option>
                                <option value="file_view">عرض ملف</option>
                                <option value="file_download">تحميل ملف</option>
                                <option value="file_delete">حذف ملف</option>
                                <option value="search">بحث</option>
                                <option value="user_create">إنشاء مستخدم</option>
                                <option value="user_update">تعديل مستخدم</option>
                                <option value="user_delete">حذف مستخدم</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">المستخدم</label>
                            <select class="form-control" id="logsUserFilter">
                                <option value="">جميع المستخدمين</option>
                                ${this.getUsersForFilter()}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="logsDateFrom">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="logsDateTo">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-1">
                                <button class="btn btn-primary btn-sm" onclick="activityLogs.applyFilters()" title="تطبيق">
                                    <i class="fas fa-search"></i>
                                </button>
                                <button class="btn btn-secondary btn-sm" onclick="activityLogs.clearFilters()" title="مسح">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="logs-actions mb-3">
                    <button class="btn btn-outline-primary" onclick="activityLogs.exportLogs()">
                        <i class="fas fa-download me-2"></i>تصدير السجل
                    </button>
                    <button class="btn btn-outline-secondary" onclick="activityLogs.refreshLogs()">
                        <i class="fas fa-sync me-2"></i>تحديث
                    </button>
                    <button class="btn btn-outline-danger" onclick="activityLogs.clearLogs()">
                        <i class="fas fa-trash me-2"></i>مسح السجل
                    </button>
                </div>

                <div class="logs-table-container">
                    <div class="card">
                        <div class="card-header">
                            <h6>سجل الأنشطة (${this.logs.length} عملية)</h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>التاريخ والوقت</th>
                                            <th>المستخدم</th>
                                            <th>العملية</th>
                                            <th>الوصف</th>
                                            <th>عنوان IP</th>
                                        </tr>
                                    </thead>
                                    <tbody id="logsTableBody">
                                        <!-- Logs will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const logsSection = document.getElementById('logs-section');
        if (logsSection) {
            logsSection.innerHTML = logsHTML;
            this.loadLogsTable();
            this.setupLogsEvents();
        }
    }

    // Setup logs events
    setupLogsEvents() {
        // Search functionality
        const searchInput = document.getElementById('logsSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', () => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.applyFilters();
                }, 300);
            });
        }
    }

    // Load logs table
    loadLogsTable(filteredLogs = null) {
        const tbody = document.getElementById('logsTableBody');
        if (!tbody) return;

        const logsToShow = filteredLogs || this.logs;

        if (logsToShow.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center py-4">
                        <div class="empty-state">
                            <i class="fas fa-history text-muted"></i>
                            <p class="text-muted mt-2">لا توجد عمليات مسجلة</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = logsToShow.map(log => this.renderLogRow(log)).join('');
    }

    // Render log row
    renderLogRow(log) {
        return `
            <tr>
                <td>
                    <div class="log-timestamp">
                        ${this.formatDate(log.timestamp)}
                    </div>
                </td>
                <td>
                    <div class="log-user">
                        <strong>${log.username}</strong>
                    </div>
                </td>
                <td>
                    <span class="badge ${this.getActionBadgeClass(log.action)}">
                        ${this.getActionDisplayName(log.action)}
                    </span>
                </td>
                <td>
                    <div class="log-description">
                        ${log.description}
                    </div>
                </td>
                <td>
                    <small class="text-muted">${log.ip}</small>
                </td>
            </tr>
        `;
    }

    // Apply filters
    applyFilters() {
        const searchTerm = document.getElementById('logsSearchInput')?.value.toLowerCase() || '';
        const actionFilter = document.getElementById('logsActionFilter')?.value || '';
        const userFilter = document.getElementById('logsUserFilter')?.value || '';
        const dateFrom = document.getElementById('logsDateFrom')?.value || '';
        const dateTo = document.getElementById('logsDateTo')?.value || '';

        let filteredLogs = this.logs.filter(log => {
            // Search filter
            if (searchTerm) {
                const searchText = `${log.username} ${log.description} ${log.action}`.toLowerCase();
                if (!searchText.includes(searchTerm)) {
                    return false;
                }
            }

            // Action filter
            if (actionFilter && log.action !== actionFilter) {
                return false;
            }

            // User filter
            if (userFilter && log.username !== userFilter) {
                return false;
            }

            // Date range filter
            if (dateFrom) {
                if (new Date(log.timestamp) < new Date(dateFrom)) {
                    return false;
                }
            }
            if (dateTo) {
                if (new Date(log.timestamp) > new Date(dateTo + 'T23:59:59')) {
                    return false;
                }
            }

            return true;
        });

        this.loadLogsTable(filteredLogs);
    }

    // Clear filters
    clearFilters() {
        document.getElementById('logsSearchInput').value = '';
        document.getElementById('logsActionFilter').value = '';
        document.getElementById('logsUserFilter').value = '';
        document.getElementById('logsDateFrom').value = '';
        document.getElementById('logsDateTo').value = '';

        this.loadLogsTable();
    }

    // Helper functions
    getUsersForFilter() {
        const users = JSON.parse(localStorage.getItem('archive_users') || '[]');
        return users.map(user => `<option value="${user.username}">${user.name}</option>`).join('');
    }

    getActionBadgeClass(action) {
        const classes = {
            login: 'bg-success',
            logout: 'bg-secondary',
            file_upload: 'bg-primary',
            file_view: 'bg-info',
            file_download: 'bg-warning',
            file_delete: 'bg-danger',
            search: 'bg-info',
            user_create: 'bg-success',
            user_update: 'bg-warning',
            user_delete: 'bg-danger'
        };
        return classes[action] || 'bg-secondary';
    }

    getActionDisplayName(action) {
        const names = {
            login: 'تسجيل دخول',
            logout: 'تسجيل خروج',
            file_upload: 'رفع ملف',
            file_view: 'عرض ملف',
            file_download: 'تحميل ملف',
            file_delete: 'حذف ملف',
            search: 'بحث',
            user_create: 'إنشاء مستخدم',
            user_update: 'تعديل مستخدم',
            user_delete: 'حذف مستخدم'
        };
        return names[action] || action;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // Export logs
    exportLogs() {
        if (this.logs.length === 0) {
            this.showAlert('warning', 'لا توجد عمليات للتصدير');
            return;
        }

        const csvContent = this.generateLogsCSV();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `activity_logs_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        this.showAlert('success', 'تم تصدير سجل العمليات بنجاح');
    }

    // Generate CSV for logs
    generateLogsCSV() {
        const headers = ['التاريخ والوقت', 'المستخدم', 'العملية', 'الوصف', 'عنوان IP'];
        const rows = this.logs.map(log => [
            this.formatDate(log.timestamp),
            log.username,
            this.getActionDisplayName(log.action),
            log.description,
            log.ip
        ]);

        return [headers, ...rows].map(row =>
            row.map(field => `"${field}"`).join(',')
        ).join('\n');
    }

    // Refresh logs
    refreshLogs() {
        this.loadLogs();
        this.loadLogsTable();
        this.showAlert('success', 'تم تحديث سجل العمليات');
    }

    // Clear logs
    clearLogs() {
        if (!confirm('هل أنت متأكد من مسح جميع سجلات العمليات؟\nلا يمكن التراجع عن هذا الإجراء.')) {
            return;
        }

        localStorage.removeItem('archive_activity_logs');
        this.logs = [];
        this.loadLogsTable();

        // Log this action
        if (window.authSystem) {
            window.authSystem.logActivity('logs_clear', 'مسح سجل العمليات');
        }

        this.showAlert('success', 'تم مسح سجل العمليات');
    }

    showAlert(type, message) {
        if (window.fileManager) {
            window.fileManager.showAlert(type, message);
        }
    }
}

// Initialize systems
let userManagement, activityLogs;

document.addEventListener('DOMContentLoaded', () => {
    userManagement = new UserManagement();
    activityLogs = new ActivityLogsSystem();

    window.userManagement = userManagement;
    window.activityLogs = activityLogs;
});

// Export for use in other modules
window.UserManagement = UserManagement;
window.ActivityLogsSystem = ActivityLogsSystem;