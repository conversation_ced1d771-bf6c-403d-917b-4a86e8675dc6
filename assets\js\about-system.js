// ===== About System Page =====

class AboutSystem {
    constructor() {
        this.systemInfo = {
            name: 'نظام الأرشفة الإلكترونية',
            name1: 'جناح الدورات',
            version: '1.0.0',
            buildDate: '2025-07-01',
            organization: 'معهد سلاح المشاة',
            developer: 'مكتب الجزاءات',
            description: 'نظام أرشفة إلكترونية متطور لإدارة وحفظ المستندات الخاصة بيوميات الدورات',
            features: [
                'إدارة الملفات والمستندات',
                'نظام بحث متقدم',
                'إدارة المستخدمين والصلاحيات',
                'سجل العمليات والأنشطة',
                'دعم اللغة العربية والإنجليزية',
                'واجهة مستخدم عصرية ومتجاوبة',
                'نظام الوضع الداكن والفاتح',
                'تصدير وطباعة التقارير'
            ],
            technologies: [
                'HTML5 & CSS3',
                'JavaScript ES6+',
                'Bootstrap 5',
                'Font Awesome',
                'Chart.js',
                'PDF.js',
                'IndexedDB'
            ]
        };
        this.initializeAboutSystem();
    }

    // Initialize about system
    initializeAboutSystem() {
        this.createAboutInterface();
        this.loadSystemStats();
    }

    // Create about interface
    createAboutInterface() {
        const aboutHTML = `
            <div class="about-system-container">
                <div class="about-header text-center">
                    <div class="system-logo">
                        <i class="fas fa-archive system-icon"></i>
                    </div>
                    <h2 class="system-title">${this.systemInfo.name}</h2>
                    <h2 class="system-title">${this.systemInfo.name1}</h2>
                    <p class="system-description">${this.systemInfo.description}</p>
                </div>

                <div class="about-content">
                    <div class="row">
                        <div class="col-lg-6 mb-4">
                            <div class="info-card">
                                <div class="card-header">
                                    <h5><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>اسم النظام:</strong></td>
                                            <td>${this.systemInfo.name}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الإصدار:</strong></td>
                                            <td>${this.systemInfo.version}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ البناء:</strong></td>
                                            <td>${this.formatDate(this.systemInfo.buildDate)}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الجهة:</strong></td>
                                            <td>${this.systemInfo.organization}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>المطور:</strong></td>
                                            <td>${this.systemInfo.developer}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>المتصفح:</strong></td>
                                            <td id="browserInfo">جاري التحميل...</td>
                                        </tr>
                                        <tr>
                                            <td><strong>نظام التشغيل:</strong></td>
                                            <td id="osInfo">جاري التحميل...</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 mb-4">
                            <div class="info-card">
                                <div class="card-header">
                                    <h5><i class="fas fa-chart-bar me-2"></i>إحصائيات النظام</h5>
                                </div>
                                <div class="card-body">
                                    <div class="stats-grid">
                                        <div class="stat-item">
                                            <div class="stat-value" id="totalFilesCount">0</div>
                                            <div class="stat-label">إجمالي الملفات</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-value" id="totalUsersCount">0</div>
                                            <div class="stat-label">المستخدمين</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-value" id="totalLogsCount">0</div>
                                            <div class="stat-label">سجل العمليات</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-value" id="systemUptime">0</div>
                                            <div class="stat-label">أيام التشغيل</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 mb-4">
                            <div class="info-card">
                                <div class="card-header">
                                    <h5><i class="fas fa-star me-2"></i>مميزات النظام</h5>
                                </div>
                                <div class="card-body">
                                    <ul class="features-list">
                                        ${this.systemInfo.features.map(feature => `
                                            <li><i class="fas fa-check text-success me-2"></i>${feature}</li>
                                        `).join('')}
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 mb-4">
                            <div class="info-card">
                                <div class="card-header">
                                    <h5><i class="fas fa-code me-2"></i>التقنيات المستخدمة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="technologies-grid">
                                        ${this.systemInfo.technologies.map(tech => `
                                            <div class="tech-item">
                                                <i class="fas fa-cog text-primary me-2"></i>
                                                <span>${tech}</span>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 mb-4">
                            <div class="info-card">
                                <div class="card-header">
                                    <h5><i class="fas fa-download me-2"></i>تصدير معلومات النظام</h5>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">يمكنك تصدير معلومات النظام والإحصائيات للمراجعة أو الأرشفة</p>
                                    <div class="export-actions">
                                        <button class="btn btn-primary" onclick="aboutSystem.exportSystemInfo()">
                                            <i class="fas fa-file-pdf me-2"></i>تصدير تقرير PDF
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="aboutSystem.exportSystemData()">
                                            <i class="fas fa-file-csv me-2"></i>تصدير البيانات CSV
                                        </button>
                                        <button class="btn btn-outline-info" onclick="aboutSystem.printSystemInfo()">
                                            <i class="fas fa-print me-2"></i>طباعة التقرير
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="info-card">
                                <div class="card-header">
                                    <h5><i class="fas fa-shield-alt me-2"></i>الأمان والخصوصية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>أمان البيانات</h6>
                                            <ul class="security-list">
                                                <li><i class="fas fa-lock text-success me-2"></i>تشفير كلمات المرور</li>
                                                <li><i class="fas fa-user-shield text-success me-2"></i>نظام صلاحيات متقدم</li>
                                                <li><i class="fas fa-history text-success me-2"></i>تسجيل جميع العمليات</li>
                                                <li><i class="fas fa-database text-success me-2"></i>تخزين محلي آمن</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>الخصوصية</h6>
                                            <ul class="security-list">
                                                <li><i class="fas fa-eye-slash text-success me-2"></i>لا يتم إرسال البيانات خارجياً</li>
                                                <li><i class="fas fa-server text-success me-2"></i>عمل على الشبكة المحلية</li>
                                                <li><i class="fas fa-user-secret text-success me-2"></i>حماية هوية المستخدمين</li>
                                                <li><i class="fas fa-trash text-success me-2"></i>إمكانية حذف البيانات</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="about-footer text-center mt-4">
                    <div class="footer-content">
                        <p class="text-muted">
                            جميع الحقوق محفوظة. © ${new Date().getFullYear()} - ${this.systemInfo.organization}.
                        </p>
                        <p class="text-muted">
                            تم تطوير هذا النظام بعناية فائقة لضمان أفضل تجربة للمستخدم
                        </p>
                    </div>
                </div>
            </div>
        `;

        const aboutSection = document.getElementById('about-section');
        if (aboutSection) {
            console.log('About section found, inserting HTML...');
            aboutSection.innerHTML = aboutHTML;

            // Load additional data
            setTimeout(() => {
                this.loadBrowserInfo();
                this.loadSystemStats();
            }, 100);

            console.log('About interface created successfully');
        } else {
            console.error('About section not found in DOM');
        }
    }

    // Load system statistics
    async loadSystemStats() {
        try {
            console.log('Loading system statistics...');

            // Get real data from database
            const files = await window.localDB?.getFiles() || [];
            const users = JSON.parse(localStorage.getItem('archive_users') || '[]');
            const logs = JSON.parse(localStorage.getItem('archive_activity_logs') || '[]');

            // Update basic counts
            this.updateElement('totalFilesCount', files.length);
            this.updateElement('totalUsersCount', users.length);
            this.updateElement('totalLogsCount', logs.length);

            // Calculate detailed statistics
            const stats = this.calculateDetailedStats(files, users, logs);
            this.updateDetailedStats(stats);

            // Calculate system uptime
            this.calculateSystemUptime(logs);

            // Update storage information
            this.updateStorageInfo(files);

            // Update category breakdown
            this.updateCategoryStats(files);

            // Update user statistics
            this.updateUserStats(users);

            console.log('System statistics loaded successfully');

        } catch (error) {
            console.error('Error loading system stats:', error);
            this.loadFallbackStats();
        }
    }

    // Calculate detailed statistics
    calculateDetailedStats(files, users, logs) {
        const now = new Date();
        const today = now.toDateString();
        const thisMonth = now.getMonth();
        const thisYear = now.getFullYear();

        return {
            // File statistics
            totalFiles: files.length,
            filesThisMonth: files.filter(f => {
                const fileDate = new Date(f.createdAt);
                return fileDate.getMonth() === thisMonth && fileDate.getFullYear() === thisYear;
            }).length,
            filesToday: files.filter(f =>
                new Date(f.createdAt).toDateString() === today
            ).length,

            // User statistics
            totalUsers: users.length,
            activeUsers: users.filter(u => u.isActive).length,
            adminUsers: users.filter(u => u.role === 'admin').length,
            employeeUsers: users.filter(u => u.role === 'employee').length,

            // Activity statistics
            totalLogs: logs.length,
            logsToday: logs.filter(l =>
                new Date(l.timestamp).toDateString() === today
            ).length,
            logsThisMonth: logs.filter(l => {
                const logDate = new Date(l.timestamp);
                return logDate.getMonth() === thisMonth && logDate.getFullYear() === thisYear;
            }).length,

            // Storage statistics
            totalSize: files.reduce((sum, file) => sum + (file.size || 0), 0),
            averageFileSize: files.length > 0 ?
                files.reduce((sum, file) => sum + (file.size || 0), 0) / files.length : 0
        };
    }

    // Update detailed statistics in UI
    updateDetailedStats(stats) {
        // Update additional statistics
        this.updateElement('filesThisMonth', stats.filesThisMonth);
        this.updateElement('filesToday', stats.filesToday);
        this.updateElement('activeUsersCount', stats.activeUsers);
        this.updateElement('logsToday', stats.logsToday);
        this.updateElement('logsThisMonth', stats.logsThisMonth);
        this.updateElement('averageFileSize', this.formatFileSize(stats.averageFileSize));
    }

    // Calculate system uptime
    calculateSystemUptime(logs) {
        if (logs.length > 0) {
            const firstLog = new Date(logs[logs.length - 1].timestamp);
            const now = new Date();
            const diffTime = Math.abs(now - firstLog);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            this.updateElement('systemUptime', diffDays);
        } else {
            this.updateElement('systemUptime', '0');
        }
    }

    // Update storage information
    updateStorageInfo(files) {
        const totalSize = files.reduce((sum, file) => sum + (file.size || 0), 0);
        this.updateElement('totalStorageUsed', this.formatFileSize(totalSize));

        // Calculate storage by type
        const storageByType = {};
        files.forEach(file => {
            const type = file.type.split('/')[0] || 'other';
            storageByType[type] = (storageByType[type] || 0) + (file.size || 0);
        });

        const storageBreakdown = Object.entries(storageByType)
            .map(([type, size]) => `${this.getTypeDisplayName(type)}: ${this.formatFileSize(size)}`)
            .join(', ');

        this.updateElement('storageBreakdown', storageBreakdown);
    }

    // Update category statistics
    updateCategoryStats(files) {
        const categories = {};
        files.forEach(file => {
            const category = file.category || 'غير مصنف';
            categories[category] = (categories[category] || 0) + 1;
        });

        const categoryList = Object.entries(categories)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([category, count]) => `${category}: ${count}`)
            .join(', ');

        this.updateElement('topCategories', categoryList);
        this.updateElement('totalCategories', Object.keys(categories).length);
    }

    // Update user statistics
    updateUserStats(users) {
        const adminCount = users.filter(u => u.role === 'admin').length;
        const employeeCount = users.filter(u => u.role === 'employee').length;
        const activeCount = users.filter(u => u.isActive).length;
        const inactiveCount = users.filter(u => !u.isActive).length;

        this.updateElement('adminUsersCount', adminCount);
        this.updateElement('employeeUsersCount', employeeCount);
        this.updateElement('activeUsersCount', activeCount);
        this.updateElement('inactiveUsersCount', inactiveCount);
    }

    // Helper function to update element text
    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        } else {
            console.warn(`Element with id '${id}' not found`);
        }
    }

    // Get type display name
    getTypeDisplayName(type) {
        const typeNames = {
            'image': 'صور',
            'application': 'مستندات',
            'text': 'نصوص',
            'video': 'فيديو',
            'audio': 'صوت',
            'other': 'أخرى'
        };
        return typeNames[type] || type;
    }

    // Format file size
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Fallback statistics loading
    loadFallbackStats() {
        console.log('Loading fallback statistics...');
        this.updateElement('totalFilesCount', '0');
        this.updateElement('totalUsersCount', '0');
        this.updateElement('totalLogsCount', '0');
        this.updateElement('systemUptime', '0');
    }

    // Load browser information
    loadBrowserInfo() {
        // Get browser info
        const browserInfo = this.getBrowserInfo();
        document.getElementById('browserInfo').textContent = browserInfo;

        // Get OS info
        const osInfo = this.getOSInfo();
        document.getElementById('osInfo').textContent = osInfo;
    }

    // Get browser information
    getBrowserInfo() {
        const userAgent = navigator.userAgent;
        let browserName = 'غير معروف';
        let browserVersion = '';

        if (userAgent.indexOf('Chrome') > -1) {
            browserName = 'Google Chrome';
            browserVersion = userAgent.match(/Chrome\/([0-9.]+)/)?.[1] || '';
        } else if (userAgent.indexOf('Firefox') > -1) {
            browserName = 'Mozilla Firefox';
            browserVersion = userAgent.match(/Firefox\/([0-9.]+)/)?.[1] || '';
        } else if (userAgent.indexOf('Safari') > -1) {
            browserName = 'Safari';
            browserVersion = userAgent.match(/Version\/([0-9.]+)/)?.[1] || '';
        } else if (userAgent.indexOf('Edge') > -1) {
            browserName = 'Microsoft Edge';
            browserVersion = userAgent.match(/Edge\/([0-9.]+)/)?.[1] || '';
        }

        return `${browserName} ${browserVersion}`;
    }

    // Get OS information
    getOSInfo() {
        const userAgent = navigator.userAgent;
        let osName = 'غير معروف';

        if (userAgent.indexOf('Windows') > -1) {
            osName = 'Windows';
        } else if (userAgent.indexOf('Mac') > -1) {
            osName = 'macOS';
        } else if (userAgent.indexOf('Linux') > -1) {
            osName = 'Linux';
        } else if (userAgent.indexOf('Android') > -1) {
            osName = 'Android';
        } else if (userAgent.indexOf('iOS') > -1) {
            osName = 'iOS';
        }

        return osName;
    }

    // Export system information as PDF
    exportSystemInfo() {
        try {
            const printWindow = window.open('', '_blank');
            const htmlContent = this.generateSystemReport();

            printWindow.document.write(htmlContent);
            printWindow.document.close();

            // Wait for content to load then trigger print
            setTimeout(() => {
                printWindow.print();
            }, 1000);

            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('system_export', 'تصدير معلومات النظام');
            }

            this.showAlert('success', 'تم إنشاء تقرير النظام بنجاح');
        } catch (error) {
            console.error('Error exporting system info:', error);
            this.showAlert('error', 'حدث خطأ أثناء تصدير معلومات النظام');
        }
    }

    // Export system data as CSV
    async exportSystemData() {
        try {
            const systemData = await this.generateSystemDataCSV();
            const blob = new Blob([systemData], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `system_data_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('system_data_export', 'تصدير بيانات النظام');
            }

            this.showAlert('success', 'تم تصدير بيانات النظام بنجاح');
        } catch (error) {
            console.error('Error exporting system data:', error);
            this.showAlert('error', 'حدث خطأ أثناء تصدير بيانات النظام');
        }
    }

    // Print system information
    printSystemInfo() {
        try {
            const printWindow = window.open('', '_blank');
            const htmlContent = this.generateSystemReport();

            printWindow.document.write(htmlContent);
            printWindow.document.close();

            setTimeout(() => {
                printWindow.print();
            }, 1000);

            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('system_print', 'طباعة معلومات النظام');
            }
        } catch (error) {
            console.error('Error printing system info:', error);
            this.showAlert('error', 'حدث خطأ أثناء طباعة معلومات النظام');
        }
    }

    // Generate system report HTML
    generateSystemReport() {
        const currentDate = new Date().toLocaleDateString('ar-SA');
        const currentTime = new Date().toLocaleTimeString('ar-SA');

        return `
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>تقرير النظام - ${this.systemInfo.name}</title>
                <style>
                    body {
                        font-family: 'Arial', sans-serif;
                        margin: 0;
                        padding: 20px;
                        background-color: #fff;
                        color: #333;
                        line-height: 1.6;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                        padding-bottom: 20px;
                        border-bottom: 3px solid #2563eb;
                    }
                    .logo {
                        font-size: 3rem;
                        color: #2563eb;
                        margin-bottom: 10px;
                    }
                    .title {
                        font-size: 2rem;
                        color: #1e293b;
                        margin: 10px 0;
                    }
                    .subtitle {
                        color: #64748b;
                        margin-bottom: 10px;
                    }
                    .report-info {
                        background-color: #f8fafc;
                        padding: 15px;
                        border-radius: 8px;
                        margin-bottom: 30px;
                        text-align: center;
                    }
                    .section {
                        margin-bottom: 30px;
                        page-break-inside: avoid;
                    }
                    .section-title {
                        font-size: 1.5rem;
                        color: #2563eb;
                        margin-bottom: 15px;
                        padding-bottom: 10px;
                        border-bottom: 2px solid #e2e8f0;
                    }
                    .info-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 20px;
                    }
                    .info-table th,
                    .info-table td {
                        padding: 12px;
                        text-align: right;
                        border-bottom: 1px solid #e2e8f0;
                    }
                    .info-table th {
                        background-color: #f1f5f9;
                        font-weight: bold;
                        color: #1e293b;
                    }
                    .stats-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                        gap: 20px;
                        margin-bottom: 20px;
                    }
                    .stat-card {
                        background-color: #f8fafc;
                        padding: 20px;
                        border-radius: 8px;
                        text-align: center;
                        border: 1px solid #e2e8f0;
                    }
                    .stat-value {
                        font-size: 2rem;
                        font-weight: bold;
                        color: #2563eb;
                        margin-bottom: 5px;
                    }
                    .stat-label {
                        color: #64748b;
                        font-size: 0.9rem;
                    }
                    .features-list,
                    .tech-list {
                        list-style: none;
                        padding: 0;
                    }
                    .features-list li,
                    .tech-list li {
                        padding: 8px 0;
                        border-bottom: 1px solid #f1f5f9;
                    }
                    .features-list li:before {
                        content: "✓ ";
                        color: #059669;
                        font-weight: bold;
                        margin-left: 10px;
                    }
                    .tech-list li:before {
                        content: "⚙ ";
                        color: #2563eb;
                        font-weight: bold;
                        margin-left: 10px;
                    }
                    .footer {
                        margin-top: 40px;
                        padding-top: 20px;
                        border-top: 2px solid #e2e8f0;
                        text-align: center;
                        color: #64748b;
                        font-size: 0.9rem;
                    }
                    @media print {
                        body { margin: 0; }
                        .header { page-break-after: avoid; }
                        .section { page-break-inside: avoid; }
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="logo">📁</div>
                    <h1 class="title">${this.systemInfo.name}</h1>
                    <p class="subtitle">تقرير معلومات النظام</p>
                </div>

                <div class="report-info">
                    <strong>تاريخ التقرير:</strong> ${currentDate} - ${currentTime}<br>
                    <strong>الإصدار:</strong> ${this.systemInfo.version}<br>
                    <strong>الجهة:</strong> ${this.systemInfo.organization}
                </div>

                <div class="section">
                    <h2 class="section-title">معلومات النظام</h2>
                    <table class="info-table">
                        <tr>
                            <th>اسم النظام</th>
                            <td>${this.systemInfo.name}</td>
                        </tr>
                        <tr>
                            <th>الإصدار</th>
                            <td>${this.systemInfo.version}</td>
                        </tr>
                        <tr>
                            <th>تاريخ البناء</th>
                            <td>${this.formatDate(this.systemInfo.buildDate)}</td>
                        </tr>
                        <tr>
                            <th>الجهة</th>
                            <td>${this.systemInfo.organization}</td>
                        </tr>
                        <tr>
                            <th>المطور</th>
                            <td>${this.systemInfo.developer}</td>
                        </tr>
                        <tr>
                            <th>المتصفح</th>
                            <td>${this.getBrowserInfo()}</td>
                        </tr>
                        <tr>
                            <th>نظام التشغيل</th>
                            <td>${this.getOSInfo()}</td>
                        </tr>
                    </table>
                </div>

                <div class="section">
                    <h2 class="section-title">إحصائيات النظام</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">${document.getElementById('totalFilesCount')?.textContent || '0'}</div>
                            <div class="stat-label">إجمالي الملفات</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${document.getElementById('totalUsersCount')?.textContent || '0'}</div>
                            <div class="stat-label">المستخدمين</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${document.getElementById('totalLogsCount')?.textContent || '0'}</div>
                            <div class="stat-label">سجل العمليات</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${document.getElementById('systemUptime')?.textContent || '0'}</div>
                            <div class="stat-label">أيام التشغيل</div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h2 class="section-title">مميزات النظام</h2>
                    <ul class="features-list">
                        ${this.systemInfo.features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                </div>

                <div class="section">
                    <h2 class="section-title">التقنيات المستخدمة</h2>
                    <ul class="tech-list">
                        ${this.systemInfo.technologies.map(tech => `<li>${tech}</li>`).join('')}
                    </ul>
                </div>

                <div class="footer">
                    <p>© ${new Date().getFullYear()} ${this.systemInfo.organization}. جميع الحقوق محفوظة.</p>
                    <p>تم إنشاء هذا التقرير تلقائياً من نظام الأرشفة الإلكترونية</p>
                </div>
            </body>
            </html>
        `;
    }

    // Generate system data CSV
    async generateSystemDataCSV() {
        const files = await window.localDB?.getFiles() || [];
        const users = JSON.parse(localStorage.getItem('archive_users') || '[]');
        const logs = JSON.parse(localStorage.getItem('archive_activity_logs') || '[]');

        let csvContent = '';

        // System info section
        csvContent += 'معلومات النظام\n';
        csvContent += 'البند,القيمة\n';
        csvContent += `"اسم النظام","${this.systemInfo.name}"\n`;
        csvContent += `"الإصدار","${this.systemInfo.version}"\n`;
        csvContent += `"تاريخ البناء","${this.formatDate(this.systemInfo.buildDate)}"\n`;
        csvContent += `"الجهة","${this.systemInfo.organization}"\n`;
        csvContent += `"المطور","${this.systemInfo.developer}"\n`;
        csvContent += '\n';

        // Statistics section
        csvContent += 'الإحصائيات\n';
        csvContent += 'النوع,العدد\n';
        csvContent += `"إجمالي الملفات","${files.length}"\n`;
        csvContent += `"المستخدمين","${users.length}"\n`;
        csvContent += `"سجل العمليات","${logs.length}"\n`;
        csvContent += '\n';

        // Files summary
        csvContent += 'ملخص الملفات\n';
        csvContent += 'التصنيف,العدد\n';
        const filesByCategory = {};
        files.forEach(file => {
            filesByCategory[file.category] = (filesByCategory[file.category] || 0) + 1;
        });
        Object.entries(filesByCategory).forEach(([category, count]) => {
            csvContent += `"${category}","${count}"\n`;
        });
        csvContent += '\n';

        // Users summary
        csvContent += 'ملخص المستخدمين\n';
        csvContent += 'الدور,العدد\n';
        const usersByRole = {};
        users.forEach(user => {
            const roleName = user.role === 'admin' ? 'مدير النظام' : 'موظف';
            usersByRole[roleName] = (usersByRole[roleName] || 0) + 1;
        });
        Object.entries(usersByRole).forEach(([role, count]) => {
            csvContent += `"${role}","${count}"\n`;
        });

        return csvContent;
    }

    // Format date
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    // Show alert
    showAlert(type, message) {
        if (window.fileManager) {
            window.fileManager.showAlert(type, message);
        }
    }
}

// Initialize about system
let aboutSystem;

// Initialize immediately if DOM is ready, otherwise wait
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAboutSystem);
} else {
    initializeAboutSystem();
}

function initializeAboutSystem() {
    console.log('Initializing AboutSystem...');
    try {
        aboutSystem = new AboutSystem();
        window.aboutSystem = aboutSystem;
        console.log('AboutSystem initialized successfully');
    } catch (error) {
        console.error('Error initializing AboutSystem:', error);
    }
}

// Export for use in other modules
window.AboutSystem = AboutSystem;