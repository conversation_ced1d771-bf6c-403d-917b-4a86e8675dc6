<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الخادم - نظام الأرشفة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .success {
            color: #27ae60;
            font-size: 18px;
            margin: 20px 0;
        }
        .info {
            color: #3498db;
            margin: 15px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .instructions {
            text-align: right;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .instructions h3 {
            color: #495057;
            margin-bottom: 15px;
        }
        .instructions ol {
            color: #6c757d;
        }
        .instructions li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 الخادم يعمل بنجاح!</h1>
        
        <div class="status status-success">
            <strong>✅ تم حل مشكلة chrome-error://chromewebdata</strong><br>
            الخادم المحلي يعمل الآن بشكل صحيح
        </div>
        
        <div class="success">
            <strong>نظام الأرشفة الإلكترونية جاهز للاستخدام</strong>
        </div>
        
        <div class="info">
            <p>إذا كنت ترى هذه الصفحة، فهذا يعني أن الخادم يعمل بشكل مثالي!</p>
        </div>
        
        <div>
            <a href="index.html" class="btn">صفحة تسجيل الدخول</a>
            <a href="dashboard.html" class="btn btn-success">لوحة التحكم</a>
        </div>
        
        <div class="instructions">
            <h3>خطوات الاستخدام:</h3>
            <ol>
                <li>انقر على "صفحة تسجيل الدخول" للبدء</li>
                <li>استخدم أي اسم مستخدم وكلمة مرور للدخول</li>
                <li>أو انقر مباشرة على "لوحة التحكم"</li>
                <li>جرب ميزة "إدارة الملفات" الجديدة</li>
                <li>اختبر رفع الصور وملفات PDF</li>
                <li>جرب خيار "عرض مضمن" في المعاينة</li>
            </ol>
        </div>
        
        <div class="info">
            <small>
                <strong>معلومات تقنية:</strong><br>
                الخادم: HTTP Server<br>
                المنفذ: 8000<br>
                العنوان: localhost / 127.0.0.1<br>
                الحالة: ✅ متصل ويعمل
            </small>
        </div>
        
        <script>
            // Test JavaScript functionality
            console.log('✅ JavaScript يعمل بشكل صحيح');
            console.log('✅ نظام الأرشفة جاهز للاستخدام');
            
            // Show current time
            document.addEventListener('DOMContentLoaded', function() {
                const now = new Date();
                const timeStr = now.toLocaleString('ar-SA');
                
                const timeDiv = document.createElement('div');
                timeDiv.className = 'info';
                timeDiv.innerHTML = `<small>وقت الاتصال: ${timeStr}</small>`;
                document.querySelector('.container').appendChild(timeDiv);
            });
        </script>
    </div>
</body>
</html>
