/* ===== Enhanced Root Variables ===== */
:root {
    /* Primary Colors */
    --primary-color: #2563eb;
    --primary-light: #3b82f6;
    --primary-dark: #1d4ed8;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    /* Secondary Colors */
    --secondary-color: #64748b;
    --success-color: #059669;
    --danger-color: #dc2626;
    --warning-color: #d97706;
    --info-color: #0891b2;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-glass: rgba(255, 255, 255, 0.25);
    --bg-overlay: rgba(0, 0, 0, 0.5);

    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-inverse: #ffffff;

    /* Border & Radius */
    --border-color: #e2e8f0;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 20px;
    --border-radius-full: 50%;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-glow: 0 0 20px rgba(37, 99, 235, 0.3);

    /* Transitions */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* ===== Dark Theme Variables ===== */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-glass: rgba(15, 23, 42, 0.25);
    --bg-overlay: rgba(0, 0, 0, 0.7);
    --primary-gradient: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);

    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;

    --border-color: #334155;

    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.3);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.3);
    --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.4);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* ===== Theme Transition ===== */
.theme-transition,
.theme-transition *,
.theme-transition *:before,
.theme-transition *:after {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    transition-delay: 0 !important;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-secondary);
    transition: var(--transition);
    color: var(--text-primary);
    transition: var(--transition);
    line-height: 1.6;
}

/* ===== Login Page Styles ===== */
.login-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    padding: 20px;
}

.login-container {
    width: 100%;
    max-width: 400px;
}

.login-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.login-header {
    margin-bottom: 2rem;
}

.logo-container {
    margin-bottom: 1rem;
}

.logo-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.login-title {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-label {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.form-control {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    padding: 0.75rem;
    transition: var(--transition);
}

.form-control:focus {
    background-color: var(--bg-primary);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    color: var(--text-primary);
}

.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
}

.password-toggle:hover {
    color: var(--primary-color);
}

.login-btn {
    background-color: var(--primary-color);
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem;
    font-weight: 600;
    transition: var(--transition);
}

.login-btn:hover {
    background-color: #1d4ed8;
    transform: translateY(-1px);
}

.login-footer {
    margin-top: 2rem;
    text-align: center;
}

.language-switcher button,
.theme-switcher button {
    margin: 0 0.25rem;
    border-radius: var(--border-radius);
}

/* ===== Loading Overlay ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .login-card {
        padding: 1.5rem;
        margin: 1rem;
    }
    
    .logo-icon {
        font-size: 2.5rem;
    }
    
    .login-title {
        font-size: 1.5rem;
    }
}

/* ===== Utility Classes ===== */
.text-primary-custom {
    color: var(--primary-color) !important;
}

.bg-primary-custom {
    background-color: var(--primary-color) !important;
}

.border-primary-custom {
    border-color: var(--primary-color) !important;
}

.shadow-custom {
    box-shadow: var(--shadow-md);
}

.rounded-custom {
    border-radius: var(--border-radius);
}

.transition-custom {
    transition: var(--transition);
}

/* ===== Animation Classes ===== */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== Custom Scrollbar ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* ===== Form Enhancements ===== */
.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-label {
    color: var(--text-secondary);
}

/* ===== Button Enhancements ===== */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #1d4ed8;
    border-color: #1d4ed8;
}

.btn-outline-secondary {
    color: var(--text-secondary);
    border-color: var(--border-color);
}

.btn-outline-secondary:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

/* ===== Card Enhancements ===== */
.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
    font-weight: 600;
}

.card-body {
    color: var(--text-primary);
}

/* ===== Dropdown Enhancements ===== */
.dropdown-menu {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
}

.dropdown-item {
    color: var(--text-primary);
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

/* ===== Badge Enhancements ===== */
.badge {
    border-radius: var(--border-radius);
}

/* ===== Alert Enhancements ===== */
.alert {
    border-radius: var(--border-radius);
    border: none;
}

.alert-success {
    background-color: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
}

.alert-danger {
    background-color: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

.alert-warning {
    background-color: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
}

.alert-info {
    background-color: rgba(8, 145, 178, 0.1);
    color: var(--info-color);
}
