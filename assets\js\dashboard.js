// ===== Dashboard Management System =====

class DashboardManager {
    constructor() {
        this.currentSection = 'dashboard';
        this.sidebarCollapsed = false;
        this.isMobile = window.innerWidth <= 992;
        this.initializeDashboard();
    }

    // Initialize dashboard
    initializeDashboard() {
        this.setupSidebar();
        this.setupNavigation();
        this.loadDashboardData();
        this.setupResponsive();
        this.initializeCharts();
        this.loadUserInfo();
    }

    // Setup sidebar functionality
    setupSidebar() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        
        // Handle sidebar toggle
        window.toggleSidebar = () => {
            if (this.isMobile) {
                sidebar.classList.toggle('show');
                this.toggleSidebarOverlay();
            } else {
                this.sidebarCollapsed = !this.sidebarCollapsed;
                sidebar.classList.toggle('collapsed', this.sidebarCollapsed);
            }
        };

        // Close sidebar on mobile when clicking outside
        document.addEventListener('click', (e) => {
            if (this.isMobile && 
                !sidebar.contains(e.target) && 
                !e.target.closest('.sidebar-toggle')) {
                sidebar.classList.remove('show');
                this.hideSidebarOverlay();
            }
        });
    }

    // Toggle sidebar overlay for mobile
    toggleSidebarOverlay() {
        let overlay = document.querySelector('.sidebar-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            document.body.appendChild(overlay);
        }
        overlay.classList.toggle('show');
    }

    // Hide sidebar overlay
    hideSidebarOverlay() {
        const overlay = document.querySelector('.sidebar-overlay');
        if (overlay) {
            overlay.classList.remove('show');
        }
    }

    // Setup navigation
    setupNavigation() {
        // Handle section switching
        window.showSection = (sectionName) => {
            this.showSection(sectionName);
        };

        // Initialize with dashboard section
        this.showSection('dashboard');
    }

    // Show specific section
    showSection(sectionName) {
        // Hide all sections
        const sections = document.querySelectorAll('.content-section');
        sections.forEach(section => {
            section.classList.remove('active');
        });

        // Show target section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        // Update navigation
        this.updateNavigation(sectionName);
        
        // Update page title
        this.updatePageTitle(sectionName);
        
        // Load section content
        this.loadSectionContent(sectionName);
        
        // Close mobile sidebar
        if (this.isMobile) {
            document.getElementById('sidebar').classList.remove('show');
            this.hideSidebarOverlay();
        }

        this.currentSection = sectionName;
    }

    // Update navigation active state
    updateNavigation(sectionName) {
        const navLinks = document.querySelectorAll('.sidebar-menu .nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${sectionName}`) {
                link.classList.add('active');
            }
        });
    }

    // Update page title
    updatePageTitle(sectionName) {
        const titles = {
            'dashboard': 'لوحة التحكم',
            'files': 'إدارة الملفات',
            'add-file': 'إضافة ملف',
            'scanner': 'الماسح الضوئي',
            'search': 'البحث المتقدم',
            'users': 'إدارة المستخدمين',
            'logs': 'سجل العمليات',
            'about': 'حول النظام'
        };

        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle) {
            pageTitle.textContent = titles[sectionName] || sectionName;
        }
    }

    // Load section content dynamically
    loadSectionContent(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                this.loadDashboardContent();
                break;
            case 'files':
                this.loadFilesContent();
                break;
            case 'add-file':
                this.loadAddFileContent();
                break;
            case 'scanner':
                this.loadScannerContent();
                break;
            case 'search':
                this.loadSearchContent();
                break;
            case 'users':
                this.loadUsersContent();
                break;
            case 'logs':
                this.loadLogsContent();
                break;
            case 'about':
                this.loadAboutContent();
                break;
        }
    }

    // Load dashboard data
    async loadDashboardData() {
        try {
            // Wait for database to be ready
            if (window.localDB) {
                await this.updateStatistics();
                await this.loadRecentFiles();
            } else {
                // Fallback if database not ready
                setTimeout(() => this.loadDashboardData(), 1000);
            }
        } catch (error) {
            console.error('Error loading dashboard data:', error);
            // Fallback to localStorage
            this.updateStatisticsFromStorage();
            this.loadRecentFilesFromStorage();
        }
    }

    // Update statistics from database
    async updateStatistics() {
        try {
            // Get real data from database
            const files = await window.localDB.getFiles();
            const users = JSON.parse(localStorage.getItem('archive_users') || '[]');
            const logs = JSON.parse(localStorage.getItem('archive_activity_logs') || '[]');

            // Calculate real statistics
            const totalFiles = files.length;
            const totalFolders = this.calculateFolderCount(files);
            const totalUsers = users.length;
            const activeUsers = users.filter(user => user.isActive).length;

            // Calculate storage usage
            const totalSize = files.reduce((sum, file) => sum + (file.size || 0), 0);
            const storageUsed = this.formatFileSize(totalSize);

            // Calculate time-based statistics
            const now = new Date();
            const today = now.toDateString();
            const thisMonth = now.getMonth();
            const thisYear = now.getFullYear();

            // Today's activity
            const todayLogs = logs.filter(log =>
                new Date(log.timestamp).toDateString() === today
            );
            const todayActivity = todayLogs.length;

            const filesToday = files.filter(f =>
                new Date(f.createdAt).toDateString() === today
            ).length;

            const filesThisMonth = files.filter(f => {
                const fileDate = new Date(f.createdAt);
                return fileDate.getMonth() === thisMonth && fileDate.getFullYear() === thisYear;
            }).length;

            // Update UI elements
            this.updateStatisticsUI({
                totalFiles,
                totalFolders,
                totalUsers,
                activeUsers,
                storageUsed,
                todayActivity,
                filesToday,
                filesThisMonth,
                files,
                users,
                logs
            });

        } catch (error) {
            console.error('Error updating statistics:', error);
            this.updateStatisticsFromStorage();
        }
    }

    // Update statistics UI
    updateStatisticsUI(stats) {
        // Update main counters
        const totalFilesEl = document.getElementById('totalFiles');
        const totalFoldersEl = document.getElementById('totalFolders');
        const totalUsersEl = document.getElementById('totalUsers');
        const todayActivityEl = document.getElementById('todayActivity');

        if (totalFilesEl) totalFilesEl.textContent = stats.totalFiles;
        if (totalFoldersEl) totalFoldersEl.textContent = stats.totalFolders;
        if (totalUsersEl) totalUsersEl.textContent = stats.totalUsers;
        if (todayActivityEl) todayActivityEl.textContent = stats.todayActivity;

        // Update mini statistics
        this.updateElement('filesToday', stats.filesToday || 0);
        this.updateElement('filesThisMonth', stats.filesThisMonth || 0);
        this.updateElement('totalStorage', stats.storageUsed || '0 KB');
        this.updateElement('activeUsers', stats.activeUsers || 0);
        this.updateElement('totalCategories', stats.totalFolders || 0);
        this.updateElement('systemUptime', this.calculateUptime(stats.logs || []));

        // Update additional statistics
        this.updateDetailedStats(stats);
        this.updateCategoryBreakdown(stats.files);
        this.updateUserStats(stats.users);
    }

    // Helper function to update element text
    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    }

    // Calculate system uptime
    calculateUptime(logs) {
        if (logs.length > 0) {
            const firstLog = new Date(logs[logs.length - 1].timestamp);
            const now = new Date();
            const diffTime = Math.abs(now - firstLog);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return diffDays;
        }
        return 0;
    }

    // Calculate folder count from files
    calculateFolderCount(files) {
        const categories = new Set();
        files.forEach(file => {
            if (file.category) {
                categories.add(file.category);
            }
        });
        return categories.size;
    }

    // Format file size
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Fallback: Get stored files from localStorage
    getStoredFiles() {
        return JSON.parse(localStorage.getItem('archive_files') || '[]');
    }

    // Fallback: Update statistics from storage
    updateStatisticsFromStorage() {
        const files = this.getStoredFiles();
        const users = JSON.parse(localStorage.getItem('archive_users') || '[]');
        const logs = JSON.parse(localStorage.getItem('archive_activity_logs') || '[]');

        const totalFiles = files.length;
        const totalFolders = this.calculateFolderCount(files);
        const totalUsers = users.length;

        const today = new Date().toDateString();
        const todayLogs = logs.filter(log =>
            new Date(log.timestamp).toDateString() === today
        );
        const todayActivity = todayLogs.length;

        this.updateStatisticsUI({
            totalFiles,
            totalFolders,
            totalUsers,
            todayActivity,
            files,
            users
        });
    }

    // Update detailed statistics
    updateDetailedStats(stats) {
        // Add storage usage info
        const storageInfo = document.querySelector('.storage-info');
        if (storageInfo) {
            storageInfo.innerHTML = `
                <div class="stat-item">
                    <i class="fas fa-hdd text-info"></i>
                    <span>مساحة التخزين المستخدمة: ${stats.storageUsed}</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-users text-success"></i>
                    <span>المستخدمون النشطون: ${stats.activeUsers}/${stats.totalUsers}</span>
                </div>
            `;
        }
    }

    // Update category breakdown
    updateCategoryBreakdown(files) {
        const categoryCount = {};
        files.forEach(file => {
            const category = file.category || 'غير مصنف';
            categoryCount[category] = (categoryCount[category] || 0) + 1;
        });

        const categoryBreakdown = document.querySelector('.category-breakdown');
        if (categoryBreakdown) {
            const categoryHTML = Object.entries(categoryCount)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([category, count]) => `
                    <div class="category-item">
                        <span class="category-name">${category}</span>
                        <span class="category-count">${count}</span>
                    </div>
                `).join('');

            categoryBreakdown.innerHTML = categoryHTML;
        }
    }

    // Update user statistics
    updateUserStats(users) {
        const userStats = {
            admins: users.filter(u => u.role === 'admin').length,
            employees: users.filter(u => u.role === 'employee').length,
            active: users.filter(u => u.isActive).length,
            inactive: users.filter(u => !u.isActive).length
        };

        const userStatsEl = document.querySelector('.user-stats');
        if (userStatsEl) {
            userStatsEl.innerHTML = `
                <div class="user-stat-item">
                    <i class="fas fa-user-shield text-danger"></i>
                    <span>المديرون: ${userStats.admins}</span>
                </div>
                <div class="user-stat-item">
                    <i class="fas fa-user text-primary"></i>
                    <span>الموظفون: ${userStats.employees}</span>
                </div>
                <div class="user-stat-item">
                    <i class="fas fa-check-circle text-success"></i>
                    <span>نشط: ${userStats.active}</span>
                </div>
                <div class="user-stat-item">
                    <i class="fas fa-times-circle text-warning"></i>
                    <span>معطل: ${userStats.inactive}</span>
                </div>
            `;
        }
    }

    // Load recent files from database
    async loadRecentFiles() {
        try {
            const files = await window.localDB.getFiles();
            const recentFiles = files
                .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                .slice(0, 5);

            this.displayRecentFiles(recentFiles);
        } catch (error) {
            console.error('Error loading recent files:', error);
            this.loadRecentFilesFromStorage();
        }
    }

    // Fallback: Load recent files from storage
    loadRecentFilesFromStorage() {
        const files = this.getStoredFiles();
        const recentFiles = files
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        this.displayRecentFiles(recentFiles);
    }

    // Display recent files
    displayRecentFiles(recentFiles) {

        const container = document.getElementById('recentFiles');
        if (container) {
            if (recentFiles.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-folder-open"></i>
                        <h4>لا توجد ملفات</h4>
                        <p>لم يتم إضافة أي ملفات بعد</p>
                    </div>
                `;
            } else {
                container.innerHTML = recentFiles.map(file => `
                    <div class="file-item">
                        <div class="file-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <div class="file-info">
                            <div class="file-name">${file.name}</div>
                            <div class="file-meta">
                                ${file.category} • ${this.formatDate(file.createdAt)}
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="btn btn-sm btn-primary" onclick="viewFile('${file.id}')" title="عرض الملف">
                                <i class="fas fa-eye me-1"></i>عرض
                            </button>
                            <button class="btn btn-sm btn-success" onclick="downloadFile('${file.id}')" title="تحميل الملف">
                                <i class="fas fa-download me-1"></i>تحميل
                            </button>
                        </div>
                    </div>
                `).join('');
            }
        }
    }

    // Initialize charts with real data
    async initializeCharts() {
        try {
            await this.createCategoryChart();
            await this.createActivityChart();
            await this.createStorageChart();
        } catch (error) {
            console.error('Error initializing charts:', error);
            this.initializeChartsFromStorage();
        }
    }

    // Create category distribution chart
    async createCategoryChart() {
        const canvas = document.getElementById('statsChart');
        if (canvas && typeof Chart !== 'undefined') {
            const ctx = canvas.getContext('2d');

            // Get real data from database
            const files = await window.localDB.getFiles();
            const categories = {};

            files.forEach(file => {
                const category = file.category || 'غير مصنف';
                categories[category] = (categories[category] || 0) + 1;
            });

            // Create chart with real data
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(categories),
                    datasets: [{
                        label: 'توزيع الملفات حسب الفئة',
                        data: Object.values(categories),
                        backgroundColor: [
                            '#2563eb', '#059669', '#d97706', '#dc2626', '#7c3aed',
                            '#0891b2', '#be185d', '#9333ea', '#ea580c', '#16a34a'
                        ],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return `${context.label}: ${context.parsed} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }
    }

    // Fallback: Initialize charts from storage
    initializeChartsFromStorage() {
        const canvas = document.getElementById('statsChart');
        if (canvas && typeof Chart !== 'undefined') {
            const ctx = canvas.getContext('2d');
            const files = this.getStoredFiles();
            const categories = {};

            files.forEach(file => {
                const category = file.category || 'غير مصنف';
                categories[category] = (categories[category] || 0) + 1;
            });

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(categories),
                    datasets: [{
                        data: Object.values(categories),
                        backgroundColor: ['#2563eb', '#059669', '#d97706', '#dc2626', '#7c3aed']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } }
                }
            });
        }
    }

    // Load user info
    loadUserInfo() {
        const currentUser = window.authSystem?.currentUser;
        if (currentUser) {
            const userNameElement = document.getElementById('currentUserName');
            const userRoleElement = document.getElementById('currentUserRole');
            
            if (userNameElement) {
                userNameElement.textContent = currentUser.name;
            }
            if (userRoleElement) {
                userRoleElement.textContent = window.authSystem.getRoleDisplayName(currentUser.role);
            }
        }
    }

    // Setup responsive behavior
    setupResponsive() {
        window.addEventListener('resize', () => {
            const wasMobile = this.isMobile;
            this.isMobile = window.innerWidth <= 992;
            
            if (wasMobile !== this.isMobile) {
                const sidebar = document.getElementById('sidebar');
                if (this.isMobile) {
                    sidebar.classList.remove('collapsed');
                    sidebar.classList.remove('show');
                } else {
                    this.hideSidebarOverlay();
                }
            }
        });
    }

    // Format date
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) {
            return 'اليوم';
        } else if (diffDays === 2) {
            return 'أمس';
        } else if (diffDays <= 7) {
            return `منذ ${diffDays} أيام`;
        } else {
            return date.toLocaleDateString('ar-SA');
        }
    }

    // Load dashboard content
    loadDashboardContent() {
        // Dashboard content is already loaded
        this.updateStatistics();
        this.loadRecentFiles();
    }

    // Load files content
    loadFilesContent() {
        if (window.fileManager) {
            window.fileManager.loadFiles();
        }
    }

    // Load add file content
    loadAddFileContent() {
        if (window.fileManager) {
            window.fileManager.createUploadArea();
        }
    }

    // Load scanner content
    loadScannerContent() {
        if (window.scannerSystem) {
            window.scannerSystem.createScannerInterface();
        }
    }

    // Load search content
    loadSearchContent() {
        if (window.searchSystem) {
            window.searchSystem.createSearchInterface();
        }
    }

    // Create activity chart
    async createActivityChart() {
        const activityCanvas = document.getElementById('activityChart');
        if (activityCanvas && typeof Chart !== 'undefined') {
            const ctx = activityCanvas.getContext('2d');

            // Get activity data for last 7 days
            const logs = JSON.parse(localStorage.getItem('archive_activity_logs') || '[]');
            const last7Days = [];
            const activityData = [];

            for (let i = 6; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const dateStr = date.toDateString();
                last7Days.push(date.toLocaleDateString('ar-SA', { weekday: 'short' }));

                const dayActivity = logs.filter(log =>
                    new Date(log.timestamp).toDateString() === dateStr
                ).length;
                activityData.push(dayActivity);
            }

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: last7Days,
                    datasets: [{
                        label: 'النشاط اليومي',
                        data: activityData,
                        borderColor: '#2563eb',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });
        }
    }

    // Create storage usage chart
    async createStorageChart() {
        const storageCanvas = document.getElementById('storageChart');
        if (storageCanvas && typeof Chart !== 'undefined') {
            const ctx = storageCanvas.getContext('2d');

            const files = await window.localDB.getFiles();
            const fileTypes = {};

            files.forEach(file => {
                const type = file.type.split('/')[0] || 'other';
                const size = file.size || 0;
                fileTypes[type] = (fileTypes[type] || 0) + size;
            });

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: Object.keys(fileTypes).map(type => {
                        const typeNames = {
                            'image': 'صور',
                            'application': 'مستندات',
                            'text': 'نصوص',
                            'video': 'فيديو',
                            'audio': 'صوت',
                            'other': 'أخرى'
                        };
                        return typeNames[type] || type;
                    }),
                    datasets: [{
                        label: 'استخدام التخزين (بايت)',
                        data: Object.values(fileTypes),
                        backgroundColor: [
                            '#059669', '#2563eb', '#d97706', '#dc2626', '#7c3aed', '#0891b2'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.label}: ${this.formatFileSize(context.parsed.y)}`;
                                }.bind(this)
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return this.formatFileSize(value);
                                }.bind(this)
                            }
                        }
                    }
                }
            });
        }
    }

    // Load users content
    loadUsersContent() {
        if (window.userManagement) {
            window.userManagement.createUserInterface();
        }
    }

    // Load logs content
    loadLogsContent() {
        if (window.activityLogs) {
            window.activityLogs.createLogsInterface();
        }
    }

    // Load about content
    loadAboutContent() {
        console.log('Loading about content...');

        if (window.aboutSystem) {
            console.log('AboutSystem found, creating interface...');
            window.aboutSystem.createAboutInterface();
        } else {
            console.log('AboutSystem not found, initializing...');
            // Try to initialize AboutSystem if not available
            if (typeof AboutSystem !== 'undefined') {
                window.aboutSystem = new AboutSystem();
                window.aboutSystem.createAboutInterface();
            } else {
                console.error('AboutSystem class not loaded');
                // Show fallback content
                this.showAboutFallback();
            }
        }
    }

    // Show fallback about content
    showAboutFallback() {
        const aboutSection = document.getElementById('about-section');
        if (aboutSection) {
            aboutSection.innerHTML = `
                <div class="container-fluid">
                    <div class="text-center p-5">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h4>خطأ في تحميل صفحة "حول النظام"</h4>
                        <p class="text-muted">يرجى إعادة تحميل الصفحة أو الاتصال بالدعم التقني</p>
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-refresh me-2"></i>إعادة تحميل
                        </button>
                    </div>
                </div>
            `;
        }
    }
}

// Global functions for file operations
window.viewFile = async (fileId) => {
    try {
        console.log('Opening file:', fileId);

        // Get file from database
        const files = await window.localDB.getFiles();
        const file = files.find(f => f.id === fileId);

        if (!file) {
            alert('الملف غير موجود');
            return;
        }

        // Check if file has content
        if (!file.content) {
            alert('محتوى الملف غير متوفر');
            return;
        }

        // Ask user how to open the file
        const openInModal = confirm('هل تريد فتح الملف في نافذة منبثقة داخل لوحة التحكم؟\n\nانقر "موافق" للفتح في نافذة منبثقة\nانقر "إلغاء" للفتح في نافذة جديدة');

        if (openInModal) {
            // Open in modal within dashboard
            openFileInModal(file);
        } else {
            // Open file based on type in new window
            if (file.type.includes('image')) {
                openImageFile(file);
            } else if (file.type.includes('pdf')) {
                openPdfFile(file);
            } else {
                // For other file types, try to open as blob
                openGenericFile(file);
            }
        }

    } catch (error) {
        console.error('Error opening file:', error);
        alert('حدث خطأ أثناء فتح الملف');
    }
};

window.downloadFile = async (fileId) => {
    try {
        console.log('Downloading file:', fileId);

        // Get file from database
        const files = await window.localDB.getFiles();
        const file = files.find(f => f.id === fileId);

        if (!file) {
            alert('الملف غير موجود');
            return;
        }

        if (!file.content) {
            alert('محتوى الملف غير متوفر للتحميل');
            return;
        }

        // Download file
        downloadFileContent(file);

    } catch (error) {
        console.error('Error downloading file:', error);
        alert('حدث خطأ أثناء تحميل الملف');
    }
};

// Helper functions for opening different file types
function openFileInModal(file) {
    try {
        // Create modal HTML
        const modalHTML = `
            <div class="file-viewer-modal" id="fileViewerModal">
                <div class="modal-overlay" onclick="closeFileModal()"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h5><i class="fas fa-file me-2"></i>${file.name}</h5>
                        <button class="btn-close" onclick="closeFileModal()">×</button>
                    </div>
                    <div class="modal-body" id="fileViewerContent">
                        <div class="text-center p-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                            <p class="mt-2">جاري تحميل الملف...</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeFileModal()">إغلاق</button>
                        <button class="btn btn-primary" onclick="downloadFileContent(${JSON.stringify(file).replace(/"/g, '&quot;')})">
                            <i class="fas fa-download me-2"></i>تحميل
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Load file content based on type
        setTimeout(() => {
            const contentDiv = document.getElementById('fileViewerContent');
            if (file.type.includes('image')) {
                contentDiv.innerHTML = `
                    <div class="text-center">
                        <img src="${file.content}" alt="${file.name}"
                             style="max-width: 100%; max-height: 500px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                    </div>
                `;
            } else if (file.type.includes('pdf')) {
                const base64Data = file.content.split(',')[1];
                const byteCharacters = atob(base64Data);
                const byteNumbers = new Array(byteCharacters.length);

                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }

                const byteArray = new Uint8Array(byteNumbers);
                const blob = new Blob([byteArray], { type: 'application/pdf' });
                const blobUrl = URL.createObjectURL(blob);

                contentDiv.innerHTML = `
                    <iframe src="${blobUrl}" style="width: 100%; height: 500px; border: none; border-radius: 8px;"></iframe>
                `;
            } else {
                contentDiv.innerHTML = `
                    <div class="text-center p-4">
                        <i class="fas fa-file fa-4x text-secondary mb-3"></i>
                        <h5>${file.name}</h5>
                        <p class="text-muted">نوع الملف: ${file.type}</p>
                        <p class="text-muted">الحجم: ${formatFileSize(file.size)}</p>
                        <p class="text-muted">لا يمكن معاينة هذا النوع من الملفات. يمكنك تحميله للعرض.</p>
                    </div>
                `;
            }
        }, 500);

    } catch (error) {
        console.error('Error opening file in modal:', error);
        alert('حدث خطأ أثناء فتح الملف');
    }
}

function closeFileModal() {
    const modal = document.getElementById('fileViewerModal');
    if (modal) {
        modal.remove();
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function openImageFile(file) {
    try {
        // Create blob URL from base64 content
        const base64Data = file.content.split(',')[1];
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);

        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: file.type });
        const blobUrl = URL.createObjectURL(blob);

        // Open in new window
        const newWindow = window.open('', '_blank');
        newWindow.document.write(`
            <html>
                <head>
                    <title>${file.name}</title>
                    <style>
                        body { margin: 0; padding: 20px; background: #f5f5f5; text-align: center; }
                        img { max-width: 100%; max-height: 90vh; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
                        h3 { color: #333; margin-bottom: 20px; }
                    </style>
                </head>
                <body>
                    <h3>${file.name}</h3>
                    <img src="${blobUrl}" alt="${file.name}" onload="console.log('Image loaded successfully')">
                </body>
            </html>
        `);

        // Clean up blob URL after some time
        setTimeout(() => URL.revokeObjectURL(blobUrl), 60000);

    } catch (error) {
        console.error('Error opening image:', error);
        alert('حدث خطأ أثناء فتح الصورة');
    }
}

function openPdfFile(file) {
    try {
        // Create blob URL from base64 content
        const base64Data = file.content.split(',')[1];
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);

        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'application/pdf' });
        const blobUrl = URL.createObjectURL(blob);

        // Open PDF in new window
        const newWindow = window.open(blobUrl, '_blank');

        // Clean up blob URL after some time
        setTimeout(() => URL.revokeObjectURL(blobUrl), 60000);

    } catch (error) {
        console.error('Error opening PDF:', error);
        alert('حدث خطأ أثناء فتح ملف PDF');
    }
}

function openGenericFile(file) {
    try {
        // For other file types, try to open as blob
        const base64Data = file.content.split(',')[1];
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);

        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: file.type });
        const blobUrl = URL.createObjectURL(blob);

        // Open in new window
        window.open(blobUrl, '_blank');

        // Clean up blob URL after some time
        setTimeout(() => URL.revokeObjectURL(blobUrl), 60000);

    } catch (error) {
        console.error('Error opening file:', error);
        alert('حدث خطأ أثناء فتح الملف');
    }
}

function downloadFileContent(file) {
    try {
        // Create blob from base64 content
        const base64Data = file.content.split(',')[1];
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);

        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: file.type });

        // Create download link
        const downloadUrl = URL.createObjectURL(blob);
        const downloadLink = document.createElement('a');
        downloadLink.href = downloadUrl;
        downloadLink.download = file.name;

        // Trigger download
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);

        // Clean up blob URL
        setTimeout(() => URL.revokeObjectURL(downloadUrl), 1000);

        console.log('File downloaded successfully:', file.name);

    } catch (error) {
        console.error('Error downloading file:', error);
        alert('حدث خطأ أثناء تحميل الملف');
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboardManager = new DashboardManager();

    // Initialize user management system
    if (typeof UserManagement !== 'undefined') {
        window.userManagement = new UserManagement();
        console.log('UserManagement initialized successfully');
    } else {
        console.error('UserManagement class not found');
    }
});

// Export for use in other modules
window.DashboardManager = DashboardManager;
