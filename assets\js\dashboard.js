// ===== Dashboard Management System =====

class DashboardManager {
    constructor() {
        this.currentSection = 'dashboard';
        this.sidebarCollapsed = false;
        this.isMobile = window.innerWidth <= 992;
        this.initializeDashboard();
    }

    // Initialize dashboard
    initializeDashboard() {
        this.setupSidebar();
        this.setupNavigation();
        this.loadDashboardData();
        this.setupResponsive();
        this.initializeCharts();
        this.loadUserInfo();
    }

    // Setup sidebar functionality
    setupSidebar() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        
        // Handle sidebar toggle
        window.toggleSidebar = () => {
            if (this.isMobile) {
                sidebar.classList.toggle('show');
                this.toggleSidebarOverlay();
            } else {
                this.sidebarCollapsed = !this.sidebarCollapsed;
                sidebar.classList.toggle('collapsed', this.sidebarCollapsed);
            }
        };

        // Close sidebar on mobile when clicking outside
        document.addEventListener('click', (e) => {
            if (this.isMobile && 
                !sidebar.contains(e.target) && 
                !e.target.closest('.sidebar-toggle')) {
                sidebar.classList.remove('show');
                this.hideSidebarOverlay();
            }
        });
    }

    // Toggle sidebar overlay for mobile
    toggleSidebarOverlay() {
        let overlay = document.querySelector('.sidebar-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            document.body.appendChild(overlay);
        }
        overlay.classList.toggle('show');
    }

    // Hide sidebar overlay
    hideSidebarOverlay() {
        const overlay = document.querySelector('.sidebar-overlay');
        if (overlay) {
            overlay.classList.remove('show');
        }
    }

    // Setup navigation
    setupNavigation() {
        // Handle section switching
        window.showSection = (sectionName) => {
            this.showSection(sectionName);
        };

        // Initialize with dashboard section
        this.showSection('dashboard');
    }

    // Show specific section
    showSection(sectionName) {
        // Hide all sections
        const sections = document.querySelectorAll('.content-section');
        sections.forEach(section => {
            section.classList.remove('active');
        });

        // Show target section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        // Update navigation
        this.updateNavigation(sectionName);
        
        // Update page title
        this.updatePageTitle(sectionName);
        
        // Load section content
        this.loadSectionContent(sectionName);
        
        // Close mobile sidebar
        if (this.isMobile) {
            document.getElementById('sidebar').classList.remove('show');
            this.hideSidebarOverlay();
        }

        this.currentSection = sectionName;
    }

    // Update navigation active state
    updateNavigation(sectionName) {
        const navLinks = document.querySelectorAll('.sidebar-menu .nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${sectionName}`) {
                link.classList.add('active');
            }
        });
    }

    // Update page title
    updatePageTitle(sectionName) {
        const titles = {
            'dashboard': 'لوحة التحكم',
            'files': 'إدارة الملفات',
            'add-file': 'إضافة ملف',
            'scanner': 'الماسح الضوئي',
            'search': 'البحث المتقدم',
            'users': 'إدارة المستخدمين',
            'logs': 'سجل العمليات',
            'about': 'حول النظام'
        };

        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle) {
            pageTitle.textContent = titles[sectionName] || sectionName;
        }
    }

    // Load section content dynamically
    loadSectionContent(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                this.loadDashboardContent();
                break;
            case 'files':
                this.loadFilesContent();
                break;
            case 'add-file':
                this.loadAddFileContent();
                break;
            case 'scanner':
                this.loadScannerContent();
                break;
            case 'search':
                this.loadSearchContent();
                break;
            case 'users':
                this.loadUsersContent();
                break;
            case 'logs':
                this.loadLogsContent();
                break;
            case 'about':
                this.loadAboutContent();
                break;
        }
    }

    // Load dashboard data
    loadDashboardData() {
        // Simulate loading data
        setTimeout(() => {
            this.updateStatistics();
            this.loadRecentFiles();
        }, 500);
    }

    // Update statistics
    updateStatistics() {
        const files = this.getStoredFiles();
        const users = JSON.parse(localStorage.getItem('archive_users') || '[]');
        const logs = JSON.parse(localStorage.getItem('archive_activity_logs') || '[]');
        
        // Update counters
        document.getElementById('totalFiles').textContent = files.length;
        document.getElementById('totalFolders').textContent = this.getFolderCount();
        document.getElementById('totalUsers').textContent = users.length;
        
        // Today's activity
        const today = new Date().toDateString();
        const todayLogs = logs.filter(log => 
            new Date(log.timestamp).toDateString() === today
        );
        document.getElementById('todayActivity').textContent = todayLogs.length;
    }

    // Get stored files
    getStoredFiles() {
        return JSON.parse(localStorage.getItem('archive_files') || '[]');
    }

    // Get folder count
    getFolderCount() {
        const files = this.getStoredFiles();
        const folders = new Set();
        files.forEach(file => {
            if (file.category) {
                folders.add(file.category);
            }
        });
        return folders.size;
    }

    // Load recent files
    loadRecentFiles() {
        const files = this.getStoredFiles();
        const recentFiles = files
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);

        const container = document.getElementById('recentFiles');
        if (container) {
            if (recentFiles.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-folder-open"></i>
                        <h4>لا توجد ملفات</h4>
                        <p>لم يتم إضافة أي ملفات بعد</p>
                    </div>
                `;
            } else {
                container.innerHTML = recentFiles.map(file => `
                    <div class="file-item">
                        <div class="file-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <div class="file-info">
                            <div class="file-name">${file.name}</div>
                            <div class="file-meta">
                                ${file.category} • ${this.formatDate(file.createdAt)}
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewFile('${file.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="downloadFile('${file.id}')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                `).join('');
            }
        }
    }

    // Initialize charts
    initializeCharts() {
        const canvas = document.getElementById('statsChart');
        if (canvas && typeof Chart !== 'undefined') {
            const ctx = canvas.getContext('2d');
            
            // Sample data for chart
            const files = this.getStoredFiles();
            const categories = {};
            files.forEach(file => {
                categories[file.category] = (categories[file.category] || 0) + 1;
            });

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(categories),
                    datasets: [{
                        data: Object.values(categories),
                        backgroundColor: [
                            '#2563eb',
                            '#059669',
                            '#d97706',
                            '#dc2626',
                            '#7c3aed'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }

    // Load user info
    loadUserInfo() {
        const currentUser = window.authSystem?.currentUser;
        if (currentUser) {
            const userNameElement = document.getElementById('currentUserName');
            const userRoleElement = document.getElementById('currentUserRole');
            
            if (userNameElement) {
                userNameElement.textContent = currentUser.name;
            }
            if (userRoleElement) {
                userRoleElement.textContent = window.authSystem.getRoleDisplayName(currentUser.role);
            }
        }
    }

    // Setup responsive behavior
    setupResponsive() {
        window.addEventListener('resize', () => {
            const wasMobile = this.isMobile;
            this.isMobile = window.innerWidth <= 992;
            
            if (wasMobile !== this.isMobile) {
                const sidebar = document.getElementById('sidebar');
                if (this.isMobile) {
                    sidebar.classList.remove('collapsed');
                    sidebar.classList.remove('show');
                } else {
                    this.hideSidebarOverlay();
                }
            }
        });
    }

    // Format date
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) {
            return 'اليوم';
        } else if (diffDays === 2) {
            return 'أمس';
        } else if (diffDays <= 7) {
            return `منذ ${diffDays} أيام`;
        } else {
            return date.toLocaleDateString('ar-SA');
        }
    }

    // Load dashboard content
    loadDashboardContent() {
        // Dashboard content is already loaded
        this.updateStatistics();
        this.loadRecentFiles();
    }

    // Load files content
    loadFilesContent() {
        if (window.fileManager) {
            window.fileManager.loadFiles();
        }
    }

    // Load add file content
    loadAddFileContent() {
        if (window.fileManager) {
            window.fileManager.createUploadArea();
        }
    }

    // Load scanner content
    loadScannerContent() {
        if (window.scannerSystem) {
            window.scannerSystem.createScannerInterface();
        }
    }

    // Load search content
    loadSearchContent() {
        if (window.searchSystem) {
            window.searchSystem.createSearchInterface();
        }
    }

    // Load users content
    loadUsersContent() {
        if (window.userManagement) {
            window.userManagement.createUserInterface();
        }
    }

    // Load logs content
    loadLogsContent() {
        if (window.activityLogs) {
            window.activityLogs.createLogsInterface();
        }
    }

    // Load about content
    loadAboutContent() {
        if (window.aboutSystem) {
            window.aboutSystem.createAboutInterface();
        }
    }
}

// Global functions for file operations (placeholders)
window.viewFile = (fileId) => {
    console.log('View file:', fileId);
};

window.downloadFile = (fileId) => {
    console.log('Download file:', fileId);
};

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboardManager = new DashboardManager();

    // Initialize user management system
    if (typeof UserManagement !== 'undefined') {
        window.userManagement = new UserManagement();
        console.log('UserManagement initialized successfully');
    } else {
        console.error('UserManagement class not found');
    }
});

// Export for use in other modules
window.DashboardManager = DashboardManager;
