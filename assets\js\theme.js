// ===== Theme Management System =====

class ThemeManager {
    constructor() {
        this.currentTheme = this.getStoredTheme() || 'light';
        this.initializeTheme();
        this.setupThemeToggle();
    }

    // Get stored theme from localStorage
    getStoredTheme() {
        return localStorage.getItem('archive_theme');
    }

    // Store theme in localStorage
    setStoredTheme(theme) {
        localStorage.setItem('archive_theme', theme);
    }

    // Get preferred theme from system
    getPreferredTheme() {
        if (this.getStoredTheme()) {
            return this.getStoredTheme();
        }
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }

    // Set theme with smooth transition
    setTheme(theme) {
        // Add transition class for smooth theme change
        document.documentElement.classList.add('theme-transition');

        if (theme === 'auto') {
            const preferredTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', preferredTheme);
        } else {
            document.documentElement.setAttribute('data-theme', theme);
        }

        this.currentTheme = theme;
        this.setStoredTheme(theme);
        this.updateThemeIcon();
        this.updateThemeText();
        this.updateThemeButtons();

        // Remove transition class after animation
        setTimeout(() => {
            document.documentElement.classList.remove('theme-transition');
        }, 300);

        // Trigger custom event for theme change
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: theme }
        }));
    }

    // Initialize theme
    initializeTheme() {
        this.setTheme(this.getPreferredTheme());
        
        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)')
            .addEventListener('change', () => {
                if (this.getStoredTheme() !== 'light' && this.getStoredTheme() !== 'dark') {
                    this.setTheme(this.getPreferredTheme());
                }
            });
    }

    // Setup theme toggle button
    setupThemeToggle() {
        // Add event listeners to all theme toggle buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('[onclick*="toggleTheme"]') || 
                e.target.closest('.theme-toggle')) {
                e.preventDefault();
                this.toggleTheme();
            }
        });
    }

    // Toggle between light and dark themes
    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.setStoredTheme(newTheme);
        this.setTheme(newTheme);
        
        // Add transition effect
        this.addThemeTransition();
        
        // Log theme change
        if (window.authSystem && window.authSystem.currentUser) {
            window.authSystem.logActivity('theme_change', `تغيير المظهر إلى: ${newTheme === 'dark' ? 'داكن' : 'فاتح'}`);
        }
    }

    // Update theme icon
    updateThemeIcon() {
        const themeIcons = document.querySelectorAll('#themeIcon, .theme-icon');
        const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
        
        themeIcons.forEach(icon => {
            icon.className = isDark ? 'fas fa-sun' : 'fas fa-moon';
        });
    }

    // Update theme text
    updateThemeText() {
        const themeTexts = document.querySelectorAll('#themeText, .theme-text');
        const isDark = document.documentElement.getAttribute('data-theme') === 'dark';

        themeTexts.forEach(text => {
            text.textContent = isDark ? 'الوضع الفاتح' : 'الوضع الداكن';
        });
    }

    // Update theme buttons
    updateThemeButtons() {
        const themeButtons = document.querySelectorAll('[data-theme-toggle]');
        const currentTheme = document.documentElement.getAttribute('data-theme');

        themeButtons.forEach(button => {
            if (button.dataset.themeToggle === currentTheme) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
    }

    // Add smooth transition when changing themes
    addThemeTransition() {
        document.documentElement.style.transition = 'all 0.3s ease';
        
        setTimeout(() => {
            document.documentElement.style.transition = '';
        }, 300);
    }

    // Get current theme
    getCurrentTheme() {
        return document.documentElement.getAttribute('data-theme') || 'light';
    }

    // Check if current theme is dark
    isDarkTheme() {
        return this.getCurrentTheme() === 'dark';
    }

    // Apply theme to specific element
    applyThemeToElement(element, theme = null) {
        const targetTheme = theme || this.getCurrentTheme();
        element.setAttribute('data-theme', targetTheme);
    }

    // Get theme-aware color
    getThemeColor(colorName) {
        const colors = {
            light: {
                primary: '#2563eb',
                secondary: '#64748b',
                success: '#059669',
                danger: '#dc2626',
                warning: '#d97706',
                info: '#0891b2',
                background: '#ffffff',
                surface: '#f8fafc',
                text: '#1e293b',
                textSecondary: '#64748b',
                border: '#e2e8f0'
            },
            dark: {
                primary: '#3b82f6',
                secondary: '#94a3b8',
                success: '#10b981',
                danger: '#ef4444',
                warning: '#f59e0b',
                info: '#06b6d4',
                background: '#0f172a',
                surface: '#1e293b',
                text: '#f8fafc',
                textSecondary: '#cbd5e1',
                border: '#334155'
            }
        };

        return colors[this.getCurrentTheme()]?.[colorName] || colors.light[colorName];
    }

    // Create theme-aware CSS custom properties
    updateCSSCustomProperties() {
        const root = document.documentElement;
        
        const properties = {
            '--theme-primary': this.getThemeColor('primary'),
            '--theme-secondary': this.getThemeColor('secondary'),
            '--theme-success': this.getThemeColor('success'),
            '--theme-danger': this.getThemeColor('danger'),
            '--theme-warning': this.getThemeColor('warning'),
            '--theme-info': this.getThemeColor('info'),
            '--theme-background': this.getThemeColor('background'),
            '--theme-surface': this.getThemeColor('surface'),
            '--theme-text': this.getThemeColor('text'),
            '--theme-text-secondary': this.getThemeColor('textSecondary'),
            '--theme-border': this.getThemeColor('border')
        };

        Object.entries(properties).forEach(([property, value]) => {
            root.style.setProperty(property, value);
        });
    }

    // Export theme settings
    exportThemeSettings() {
        return {
            currentTheme: this.currentTheme,
            storedTheme: this.getStoredTheme(),
            preferredTheme: this.getPreferredTheme(),
            isDark: this.isDarkTheme()
        };
    }

    // Import theme settings
    importThemeSettings(settings) {
        if (settings.storedTheme) {
            this.setStoredTheme(settings.storedTheme);
            this.setTheme(settings.storedTheme);
        }
    }

    // Reset theme to system preference
    resetToSystemTheme() {
        localStorage.removeItem('archive_theme');
        this.currentTheme = this.getPreferredTheme();
        this.setTheme(this.currentTheme);
    }

    // Get theme statistics
    getThemeStats() {
        const usage = JSON.parse(localStorage.getItem('archive_theme_usage') || '{}');
        return {
            lightUsage: usage.light || 0,
            darkUsage: usage.dark || 0,
            totalSwitches: usage.switches || 0,
            lastChanged: usage.lastChanged || null
        };
    }

    // Track theme usage
    trackThemeUsage() {
        const usage = this.getThemeStats();
        const currentTheme = this.getCurrentTheme();
        
        usage[currentTheme + 'Usage'] = (usage[currentTheme + 'Usage'] || 0) + 1;
        usage.switches = (usage.switches || 0) + 1;
        usage.lastChanged = new Date().toISOString();
        
        localStorage.setItem('archive_theme_usage', JSON.stringify(usage));
    }
}

// Global theme toggle function
function toggleTheme() {
    if (window.themeManager) {
        window.themeManager.toggleTheme();
    }
}

// Initialize theme manager
const themeManager = new ThemeManager();

// Make theme manager globally available
window.themeManager = themeManager;
window.toggleTheme = toggleTheme;

// Update theme on page load
document.addEventListener('DOMContentLoaded', () => {
    themeManager.updateCSSCustomProperties();
    themeManager.updateThemeIcon();
    themeManager.updateThemeText();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
