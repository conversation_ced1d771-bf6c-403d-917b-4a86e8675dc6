// ===== Local Database Management System =====

class LocalDatabase {
    constructor() {
        this.dbName = 'ArchiveDB';
        this.version = 1;
        this.db = null;
        this.initializeDB();
    }

    // Initialize IndexedDB
    async initializeDB() {
        try {
            this.db = await this.openDB();
            console.log('IndexedDB initialized successfully');
        } catch (error) {
            console.error('Failed to initialize IndexedDB:', error);
            console.log('Falling back to localStorage');
            this.db = null;
        }

        // Always initialize localStorage as backup
        this.initializeLocalStorage();
    }

    // Open IndexedDB
    openDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => reject(request.error);
            request.onsuccess = () => resolve(request.result);

            request.onupgradeneeded = (event) => {
                const db = event.target.result;

                // Create files store
                if (!db.objectStoreNames.contains('files')) {
                    const filesStore = db.createObjectStore('files', { keyPath: 'id' });
                    filesStore.createIndex('name', 'name', { unique: false });
                    filesStore.createIndex('category', 'category', { unique: false });
                    filesStore.createIndex('createdAt', 'createdAt', { unique: false });
                    filesStore.createIndex('userId', 'userId', { unique: false });
                }

                // Create users store
                if (!db.objectStoreNames.contains('users')) {
                    const usersStore = db.createObjectStore('users', { keyPath: 'id' });
                    usersStore.createIndex('username', 'username', { unique: true });
                    usersStore.createIndex('email', 'email', { unique: true });
                }

                // Create logs store
                if (!db.objectStoreNames.contains('logs')) {
                    const logsStore = db.createObjectStore('logs', { keyPath: 'id' });
                    logsStore.createIndex('userId', 'userId', { unique: false });
                    logsStore.createIndex('timestamp', 'timestamp', { unique: false });
                    logsStore.createIndex('action', 'action', { unique: false });
                }

                // Create folders store
                if (!db.objectStoreNames.contains('folders')) {
                    const foldersStore = db.createObjectStore('folders', { keyPath: 'id' });
                    foldersStore.createIndex('name', 'name', { unique: false });
                    foldersStore.createIndex('parentId', 'parentId', { unique: false });
                }
            };
        });
    }

    // Initialize localStorage as fallback
    initializeLocalStorage() {
        console.log('Using localStorage as database fallback');
        
        // Initialize default data if not exists
        if (!localStorage.getItem('archive_files')) {
            const sampleFiles = [
                {
                    id: 'file_sample_1',
                    name: 'يومية مخالفات - يناير 2024.pdf',
                    type: 'application/pdf',
                    size: 1024000,
                    category: 'يومية مخالفات',
                    description: 'يومية مخالفات شهر يناير 2024',
                    tags: ['يناير', '2024', 'مخالفات'],
                    courseCode: 'YM-2024-01',
                    courseNumber: '001',
                    createdAt: new Date('2024-01-15').toISOString(),
                    userId: 'admin',
                    filePath: null
                },
                {
                    id: 'file_sample_2',
                    name: 'إفادة موظف - أحمد محمد.pdf',
                    type: 'application/pdf',
                    size: 512000,
                    category: 'إفادة',
                    description: 'إفادة للموظف أحمد محمد',
                    tags: ['إفادة', 'أحمد محمد'],
                    courseCode: 'IF-2024-01',
                    courseNumber: '002',
                    createdAt: new Date('2024-01-20').toISOString(),
                    userId: 'admin',
                    filePath: null
                }
            ];
            localStorage.setItem('archive_files', JSON.stringify(sampleFiles));
            console.log('Sample files created in localStorage:', sampleFiles.length);
        } else {
            const existingFiles = JSON.parse(localStorage.getItem('archive_files'));
            console.log('Existing files in localStorage:', existingFiles.length);
        }
        if (!localStorage.getItem('archive_folders')) {
            localStorage.setItem('archive_folders', JSON.stringify(this.getDefaultFolders()));
        }
        if (!localStorage.getItem('archive_activity_logs')) {
            localStorage.setItem('archive_activity_logs', JSON.stringify([]));
        }
    }

    // Get default folders
    getDefaultFolders() {
        return [
            {
                id: 'folder_1',
                name: 'يومية مخالفات',
                parentId: null,
                createdAt: new Date().toISOString(),
                createdBy: 'system'
            },
            {
                id: 'folder_2',
                name: 'إفادات',
                parentId: null,
                createdAt: new Date().toISOString(),
                createdBy: 'system'
            },
            {
                id: 'folder_3',
                name: 'مستندات الإجازات',
                parentId: null,
                createdAt: new Date().toISOString(),
                createdBy: 'system'
            },
            {
                id: 'folder_4',
                name: 'رخص إدارية',
                parentId: null,
                createdAt: new Date().toISOString(),
                createdBy: 'system'
            }
        ];
    }

    // ===== Files Operations =====

    // Add file
    async addFile(fileData) {
        const file = {
            id: this.generateId(),
            name: fileData.name,
            originalName: fileData.originalName,
            category: fileData.category,
            type: fileData.type,
            size: fileData.size,
            content: fileData.content, // Base64 encoded
            description: fileData.description || '',
            tags: fileData.tags || [],
            issueDate: fileData.issueDate,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: fileData.createdBy,
            folderId: fileData.folderId || null,
            metadata: fileData.metadata || {}
        };

        try {
            if (this.db) {
                await this.addToIndexedDB('files', file);
            } else {
                this.addToLocalStorage('archive_files', file);
            }
            return file;
        } catch (error) {
            console.error('Error adding file:', error);
            throw error;
        }
    }

    // Get files
    async getFiles(filters = {}) {
        try {
            let files = [];

            // Try IndexedDB first
            if (this.db) {
                try {
                    files = await this.getFromIndexedDB('files');
                    console.log('Files loaded from IndexedDB:', files.length);
                } catch (indexedDBError) {
                    console.warn('IndexedDB failed, falling back to localStorage:', indexedDBError);
                    files = JSON.parse(localStorage.getItem('archive_files') || '[]');
                }
            } else {
                // Use localStorage
                files = JSON.parse(localStorage.getItem('archive_files') || '[]');
                console.log('Files loaded from localStorage:', files.length);
            }

            // Ensure files is an array
            if (!Array.isArray(files)) {
                console.warn('Files data is not an array, initializing empty array');
                files = [];
            }

            // Apply filters
            if (filters.category) {
                files = files.filter(file => file.category === filters.category);
            }
            if (filters.dateFrom) {
                files = files.filter(file => new Date(file.createdAt) >= new Date(filters.dateFrom));
            }
            if (filters.dateTo) {
                files = files.filter(file => new Date(file.createdAt) <= new Date(filters.dateTo));
            }
            if (filters.search) {
                const searchTerm = filters.search.toLowerCase();
                files = files.filter(file =>
                    (file.name && file.name.toLowerCase().includes(searchTerm)) ||
                    (file.description && file.description.toLowerCase().includes(searchTerm)) ||
                    (file.tags && Array.isArray(file.tags) && file.tags.some(tag => tag.toLowerCase().includes(searchTerm))) ||
                    (file.courseCode && file.courseCode.toLowerCase().includes(searchTerm)) ||
                    (file.courseNumber && file.courseNumber.toLowerCase().includes(searchTerm))
                );
            }

            return files;
        } catch (error) {
            console.error('Error getting files:', error);
            return [];
        }
    }

    // Get file by ID
    async getFileById(id) {
        try {
            if (this.db) {
                return await this.getFromIndexedDBById('files', id);
            } else {
                const files = JSON.parse(localStorage.getItem('archive_files') || '[]');
                return files.find(file => file.id === id);
            }
        } catch (error) {
            console.error('Error getting file by ID:', error);
            return null;
        }
    }

    // Update file
    async updateFile(id, updates) {
        try {
            const file = await this.getFileById(id);
            if (!file) {
                throw new Error('File not found');
            }

            const updatedFile = {
                ...file,
                ...updates,
                updatedAt: new Date().toISOString()
            };

            if (this.db) {
                await this.updateInIndexedDB('files', updatedFile);
            } else {
                this.updateInLocalStorage('archive_files', id, updatedFile);
            }

            return updatedFile;
        } catch (error) {
            console.error('Error updating file:', error);
            throw error;
        }
    }

    // Delete file
    async deleteFile(id) {
        try {
            if (this.db) {
                await this.deleteFromIndexedDB('files', id);
            } else {
                this.deleteFromLocalStorage('archive_files', id);
            }
            return true;
        } catch (error) {
            console.error('Error deleting file:', error);
            throw error;
        }
    }

    // ===== Folders Operations =====

    // Add folder
    async addFolder(folderData) {
        const folder = {
            id: this.generateId(),
            name: folderData.name,
            parentId: folderData.parentId || null,
            description: folderData.description || '',
            createdAt: new Date().toISOString(),
            createdBy: folderData.createdBy
        };

        try {
            if (this.db) {
                await this.addToIndexedDB('folders', folder);
            } else {
                this.addToLocalStorage('archive_folders', folder);
            }
            return folder;
        } catch (error) {
            console.error('Error adding folder:', error);
            throw error;
        }
    }

    // Get folders
    async getFolders() {
        try {
            if (this.db) {
                return await this.getFromIndexedDB('folders');
            } else {
                return JSON.parse(localStorage.getItem('archive_folders') || '[]');
            }
        } catch (error) {
            console.error('Error getting folders:', error);
            return [];
        }
    }

    // ===== Logs Operations =====

    // Add log
    async addLog(logData) {
        const log = {
            id: this.generateId(),
            userId: logData.userId,
            username: logData.username,
            action: logData.action,
            description: logData.description,
            timestamp: new Date().toISOString(),
            ip: logData.ip || 'localhost',
            userAgent: logData.userAgent || navigator.userAgent,
            metadata: logData.metadata || {}
        };

        try {
            if (this.db) {
                await this.addToIndexedDB('logs', log);
            } else {
                this.addToLocalStorage('archive_activity_logs', log);
            }
            return log;
        } catch (error) {
            console.error('Error adding log:', error);
            throw error;
        }
    }

    // Get logs
    async getLogs(limit = 100) {
        try {
            let logs;
            if (this.db) {
                logs = await this.getFromIndexedDB('logs');
            } else {
                logs = JSON.parse(localStorage.getItem('archive_activity_logs') || '[]');
            }

            // Sort by timestamp (newest first) and limit
            return logs
                .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                .slice(0, limit);
        } catch (error) {
            console.error('Error getting logs:', error);
            return [];
        }
    }

    // ===== IndexedDB Helper Methods =====

    async addToIndexedDB(storeName, data) {
        const transaction = this.db.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);
        return store.add(data);
    }

    async getFromIndexedDB(storeName) {
        const transaction = this.db.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);
        const request = store.getAll();
        
        return new Promise((resolve, reject) => {
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getFromIndexedDBById(storeName, id) {
        const transaction = this.db.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);
        const request = store.get(id);
        
        return new Promise((resolve, reject) => {
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async updateInIndexedDB(storeName, data) {
        const transaction = this.db.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);
        return store.put(data);
    }

    async deleteFromIndexedDB(storeName, id) {
        const transaction = this.db.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);
        return store.delete(id);
    }

    // ===== localStorage Helper Methods =====

    addToLocalStorage(key, data) {
        const items = JSON.parse(localStorage.getItem(key) || '[]');
        items.push(data);
        localStorage.setItem(key, JSON.stringify(items));
    }

    updateInLocalStorage(key, id, data) {
        const items = JSON.parse(localStorage.getItem(key) || '[]');
        const index = items.findIndex(item => item.id === id);
        if (index !== -1) {
            items[index] = data;
            localStorage.setItem(key, JSON.stringify(items));
        }
    }

    deleteFromLocalStorage(key, id) {
        const items = JSON.parse(localStorage.getItem(key) || '[]');
        const filteredItems = items.filter(item => item.id !== id);
        localStorage.setItem(key, JSON.stringify(filteredItems));
    }

    // ===== Utility Methods =====

    generateId() {
        return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Export data
    async exportData() {
        const data = {
            files: await this.getFiles(),
            folders: await this.getFolders(),
            logs: await this.getLogs(1000),
            exportDate: new Date().toISOString()
        };
        return data;
    }

    // Import data
    async importData(data) {
        try {
            if (data.files) {
                for (const file of data.files) {
                    await this.addFile(file);
                }
            }
            if (data.folders) {
                for (const folder of data.folders) {
                    await this.addFolder(folder);
                }
            }
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            throw error;
        }
    }

    // Clear all data
    async clearAllData() {
        try {
            if (this.db) {
                const stores = ['files', 'folders', 'logs'];
                for (const storeName of stores) {
                    const transaction = this.db.transaction([storeName], 'readwrite');
                    const store = transaction.objectStore(storeName);
                    await store.clear();
                }
            } else {
                localStorage.removeItem('archive_files');
                localStorage.removeItem('archive_folders');
                localStorage.removeItem('archive_activity_logs');
                this.initializeLocalStorage();
            }
            return true;
        } catch (error) {
            console.error('Error clearing data:', error);
            throw error;
        }
    }

    // Reset database with sample data
    async resetDatabase() {
        try {
            await this.clearAllData();
            console.log('Database reset with sample data');
            return true;
        } catch (error) {
            console.error('Error resetting database:', error);
            throw error;
        }
    }
}

// Initialize database
console.log('Initializing LocalDatabase...');
const localDB = new LocalDatabase();

// Make database globally available
window.localDB = localDB;

// Add debug info
console.log('LocalDatabase instance created and assigned to window.localDB');

// Ensure database is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, database status:', {
        localDB: !!window.localDB,
        hasDB: !!window.localDB?.db,
        constructor: window.localDB?.constructor?.name
    });
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LocalDatabase;
}
