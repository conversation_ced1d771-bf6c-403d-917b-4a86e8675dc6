/* ===== Dashboard Layout ===== */
.dashboard-page {
    display: flex;
    min-height: 100vh;
    background-color: var(--bg-secondary);
}

/* ===== Sidebar Styles ===== */
.sidebar {
    width: 280px;
    background-color: var(--bg-primary);
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    right: 0;
    top: 0;
    z-index: 1000;
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-header .logo-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.sidebar-header .logo-icon {
    font-size: 1.75rem;
    color: var(--primary-color);
}

.sidebar-header .logo-text {
    font-weight: 700;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.sidebar.collapsed .logo-text {
    display: none;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background-color: var(--bg-secondary);
    color: var(--primary-color);
}

/* ===== Sidebar Menu ===== */
.sidebar-menu {
    flex: 1;
    padding: 1rem 0;
    overflow-y: auto;
}

.sidebar-menu .nav {
    padding: 0 1rem;
}

.sidebar-menu .nav-item {
    margin-bottom: 0.25rem;
}

.sidebar-menu .nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.sidebar-menu .nav-link:hover {
    background-color: var(--bg-secondary);
    color: var(--primary-color);
}

.sidebar-menu .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.sidebar-menu .nav-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.sidebar.collapsed .nav-link span {
    display: none;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 0.75rem;
}

/* ===== Sidebar Footer ===== */
.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
}

.user-avatar i {
    font-size: 2rem;
    color: var(--primary-color);
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.sidebar.collapsed .user-details {
    display: none;
}

.logout-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.sidebar.collapsed .logout-btn span {
    display: none;
}

/* ===== Main Content ===== */
.main-content {
    flex: 1;
    margin-right: 280px;
    display: flex;
    flex-direction: column;
    transition: var(--transition);
}

.sidebar.collapsed + .main-content {
    margin-right: 70px;
}

/* ===== Top Navigation ===== */
.top-nav {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.nav-right {
    display: flex;
    align-items: center;
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-controls .btn {
    position: relative;
}

.nav-controls .badge {
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 0.7rem;
}

/* ===== Content Area ===== */
.content-area {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
    animation: fadeIn 0.3s ease-in;
}

/* ===== Statistics Cards ===== */
.stat-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Mini Statistics Cards */
.stat-card.mini {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
    min-height: 100px;
}

.stat-card.mini .stat-icon.mini {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
    margin: 0 auto;
}

.stat-card.mini .stat-info h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0.25rem 0;
}

.stat-card.mini .stat-info small {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* System Information */
.system-info-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.system-info-item:last-child {
    border-bottom: none;
}

.system-info-item strong {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 600;
}

.system-info-item span {
    color: var(--text-primary);
    font-weight: 500;
}

/* File Viewer Modal */
.file-viewer-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.file-viewer-modal .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-viewer-modal .modal-content {
    background: var(--bg-primary);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 90vw;
    max-height: 90vh;
    width: 800px;
    position: relative;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--border-color);
}

.file-viewer-modal .modal-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--bg-secondary);
    border-radius: 12px 12px 0 0;
}

.file-viewer-modal .modal-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
}

.file-viewer-modal .btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.file-viewer-modal .btn-close:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.file-viewer-modal .modal-body {
    padding: 1.5rem;
    flex: 1;
    overflow: auto;
    max-height: 60vh;
}

.file-viewer-modal .modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    background: var(--bg-secondary);
    border-radius: 0 0 12px 12px;
}

@media (max-width: 768px) {
    .file-viewer-modal .modal-content {
        width: 95vw;
        max-height: 95vh;
    }

    .file-viewer-modal .modal-body {
        max-height: 50vh;
        padding: 1rem;
    }

    .file-viewer-modal .modal-header,
    .file-viewer-modal .modal-footer {
        padding: 0.75rem 1rem;
    }
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.stat-info p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

/* ===== Recent Files List ===== */
.recent-files-list {
    max-height: 400px;
    overflow-y: auto;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 0.75rem;
    background: var(--bg-secondary);
    transition: all 0.2s ease;
}

.file-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
    background: var(--bg-primary);
}

.file-item:last-child {
    margin-bottom: 0;
}

.file-info {
    flex: 1;
}

.file-info h6 {
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1rem;
}

.file-meta {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.file-actions {
    display: flex;
    gap: 0.5rem;
}

.file-actions .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
}

.file-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.file-icon {
    width: 40px;
    height: 40px;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.file-meta {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.file-actions {
    display: flex;
    gap: 0.5rem;
}

.file-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* ===== Responsive Design ===== */
@media (max-width: 1200px) {
    .sidebar {
        width: 250px;
    }
    
    .main-content {
        margin-right: 250px;
    }
    
    .sidebar.collapsed + .main-content {
        margin-right: 70px;
    }
}

@media (max-width: 992px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .content-area {
        padding: 1rem;
    }
    
    .top-nav {
        padding: 1rem;
    }
    
    .page-title {
        font-size: 1.25rem;
    }
}

@media (max-width: 768px) {
    .stat-card {
        padding: 1rem;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .stat-info h3 {
        font-size: 1.5rem;
    }
    
    .nav-controls {
        gap: 0.25rem;
    }
    
    .nav-controls .btn {
        padding: 0.5rem;
    }
}

@media (max-width: 576px) {
    .content-area {
        padding: 0.5rem;
    }
    
    .top-nav {
        padding: 0.75rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .nav-left,
    .nav-right {
        justify-content: space-between;
    }
    
    .page-title {
        font-size: 1.1rem;
    }
}

/* ===== Mobile Sidebar Overlay ===== */
@media (max-width: 992px) {
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
        display: none;
    }
    
    .sidebar-overlay.show {
        display: block;
    }
}

/* ===== Loading States ===== */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* ===== Empty States ===== */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-muted);
}

.empty-state h4 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.empty-state p {
    margin-bottom: 1.5rem;
}

/* ===== Tooltip Enhancements ===== */
.tooltip {
    font-size: 0.8rem;
}

.tooltip-inner {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: var(--border-color);
}

.tooltip.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: var(--border-color);
}

.tooltip.bs-tooltip-start .tooltip-arrow::before {
    border-left-color: var(--border-color);
}

.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-right-color: var(--border-color);
}

/* ===== File Upload Styles ===== */
.file-drop-zone {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    background-color: var(--bg-secondary);
}

.file-drop-zone:hover,
.file-drop-zone.drag-over {
    border-color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.05);
}

.drop-zone-content {
    pointer-events: none;
}

.drop-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.file-drop-zone h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.file-drop-zone p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

/* ===== Upload Queue Styles ===== */
.upload-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
}

.upload-info {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.file-size {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.upload-progress {
    min-width: 200px;
}

.progress {
    height: 6px;
    margin-bottom: 0.25rem;
}

.status-text {
    color: var(--text-secondary);
}

/* ===== Modal Styles ===== */
.upload-form-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    position: relative;
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    border: 1px solid var(--border-color);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.btn-close:hover {
    color: var(--text-primary);
    background-color: var(--bg-secondary);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

/* ===== File Grid Styles ===== */
.files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.file-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    transition: var(--transition);
    cursor: pointer;
}

.file-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.file-card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.file-type-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.file-card-info h6 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1rem;
}

.file-card-meta {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.file-card-body {
    margin-bottom: 1rem;
}

.file-category {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
}

.file-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.file-card-footer {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.file-card-footer .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
}

/* ===== Search and Filter Styles ===== */
.search-filters {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.filter-row {
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
}

.filter-actions {
    display: flex;
    gap: 0.5rem;
}

/* ===== Responsive File Grid ===== */
@media (max-width: 768px) {
    .files-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .file-card {
        padding: 1rem;
    }

    .file-card-header {
        gap: 0.75rem;
    }

    .file-type-icon {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }

    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        min-width: auto;
    }

    .filter-actions {
        justify-content: stretch;
    }

    .filter-actions .btn {
        flex: 1;
    }
}

/* ===== File Viewer Modal Styles ===== */
.file-viewer-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-viewer-content {
    max-width: 90vw;
    max-height: 90vh;
    width: 1000px;
    height: 800px;
    display: flex;
    flex-direction: column;
}

.file-viewer-content .modal-header {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.viewer-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.file-viewer-content .modal-body {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.file-viewer-container {
    flex: 1;
    overflow: auto;
    background-color: var(--bg-secondary);
}

.pdf-viewer iframe {
    width: 100%;
    height: 100%;
    min-height: 600px;
}

.image-viewer {
    padding: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.file-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.file-info {
    background-color: var(--bg-secondary);
    padding: 1rem;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
}

/* ===== Files Header Styles ===== */
.files-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.view-controls {
    display: flex;
    gap: 0.25rem;
}

.view-controls .btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* ===== File Tags Styles ===== */
.file-tags .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

/* ===== Responsive File Viewer ===== */
@media (max-width: 768px) {
    .file-viewer-content {
        width: 95vw;
        height: 95vh;
        max-width: none;
        max-height: none;
    }

    .file-viewer-content .modal-header {
        padding: 1rem;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .file-viewer-content .modal-header h5 {
        font-size: 1rem;
        flex: 1;
        min-width: 200px;
    }

    .viewer-controls {
        flex-wrap: wrap;
    }

    .viewer-controls .btn {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }

    .pdf-viewer iframe {
        min-height: 400px;
    }

    .image-viewer {
        padding: 1rem;
        min-height: 300px;
    }

    .file-preview {
        min-height: 300px;
        padding: 2rem 1rem;
    }

    .file-info {
        font-size: 0.8rem;
    }

    .file-info .row {
        margin: 0;
    }

    .file-info .col-md-6 {
        padding: 0.5rem 0;
    }
}

/* ===== Print Styles ===== */
@media print {
    .modal-header,
    .modal-footer,
    .viewer-controls {
        display: none !important;
    }

    .file-viewer-container {
        height: auto !important;
        overflow: visible !important;
    }

    .pdf-viewer iframe,
    .image-viewer img {
        width: 100% !important;
        height: auto !important;
        max-height: none !important;
    }
}

/* ===== Advanced Search Styles ===== */
.advanced-search-container {
    max-width: 1200px;
    margin: 0 auto;
}

.search-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.search-header h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.search-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.search-shortcuts .card {
    height: 100%;
}

.saved-search-item,
.history-item {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.saved-search-item:hover,
.history-item:hover {
    background-color: var(--bg-secondary);
}

.saved-search-item:last-child,
.history-item:last-child {
    border-bottom: none;
}

.search-name,
.history-summary {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.search-date,
.history-meta {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.search-actions,
.history-actions {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}

.history-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.history-actions .btn {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
}

/* ===== Search Results Styles ===== */
.results-summary {
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.results-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.search-result-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    cursor: pointer;
}

.search-result-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.result-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.result-info {
    flex: 1;
    min-width: 0;
}

.result-title {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.1rem;
}

.result-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
}

.result-category {
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
}

.result-date,
.result-size {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.result-description {
    color: var(--text-secondary);
    margin: 0.5rem 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.result-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 0.5rem;
}

.result-tags .tag {
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    font-size: 0.8rem;
    border: 1px solid var(--border-color);
}

.result-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.results-actions {
    display: flex;
    gap: 0.5rem;
}

/* ===== Quick Search Dropdown ===== */
.quick-search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
}

.quick-search-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: var(--transition);
}

.quick-search-item:hover {
    background-color: var(--bg-secondary);
}

.quick-search-item:last-child {
    border-bottom: none;
}

.quick-search-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.quick-search-meta {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* ===== Responsive Search Styles ===== */
@media (max-width: 768px) {
    .search-actions {
        flex-direction: column;
    }

    .search-actions .btn {
        width: 100%;
    }

    .search-result-item {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }

    .result-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .result-meta {
        justify-content: center;
        gap: 0.5rem;
    }

    .result-actions {
        justify-content: center;
        width: 100%;
    }

    .results-actions {
        flex-direction: column;
    }

    .results-actions .btn {
        width: 100%;
    }

    .saved-search-item .search-actions,
    .history-actions {
        flex-direction: column;
        gap: 0.25rem;
    }

    .saved-search-item .search-actions .btn,
    .history-actions .btn {
        width: 100%;
        margin-bottom: 0.25rem;
    }
}

/* ===== Search Loading Styles ===== */
.search-loading {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
}

.search-loading .spinner-border {
    margin-bottom: 1rem;
}

/* ===== Search Form Enhancements ===== */
.search-form-container .form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.search-form-container .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

/* ===== Search Statistics ===== */
.search-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
}

.search-stat {
    text-align: center;
}

.search-stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    display: block;
}

.search-stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* ===== User Management Styles ===== */
.user-management-container,
.activity-logs-container {
    max-width: 1200px;
    margin: 0 auto;
}

.management-header,
.logs-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.management-header h4,
.logs-header h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.user-actions,
.logs-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.users-stats {
    margin-bottom: 2rem;
}

.table-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.table-controls input {
    max-width: 250px;
}

.user-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* ===== User Modal Styles ===== */
.user-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-modal .modal-content {
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.user-info-card,
.user-activity-card {
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
}

.user-info-card h6,
.user-activity-card h6 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-weight: 600;
}

.user-info-card table {
    margin-bottom: 0;
}

.user-info-card td {
    padding: 0.5rem 0;
    border: none;
    vertical-align: top;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.activity-item:hover {
    background-color: var(--bg-primary);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-action {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* ===== Activity Logs Styles ===== */
.logs-controls {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.logs-controls .form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.logs-table-container .card-header h6 {
    margin: 0;
    color: var(--text-primary);
}

.log-timestamp {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.log-user {
    color: var(--text-primary);
}

.log-description {
    color: var(--text-secondary);
    line-height: 1.4;
}

/* ===== Table Enhancements ===== */
.table-hover tbody tr:hover {
    background-color: var(--bg-secondary);
}

.table-primary {
    background-color: rgba(37, 99, 235, 0.1) !important;
}

.table th {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: 600;
    border-bottom: 2px solid var(--border-color);
}

.table td {
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

/* ===== Badge Enhancements ===== */
.badge {
    font-size: 0.8rem;
    padding: 0.375rem 0.75rem;
}

.badge.bg-danger {
    background-color: var(--danger-color) !important;
}

.badge.bg-success {
    background-color: var(--success-color) !important;
}

.badge.bg-warning {
    background-color: var(--warning-color) !important;
    color: white !important;
}

.badge.bg-info {
    background-color: var(--info-color) !important;
}

.badge.bg-secondary {
    background-color: var(--secondary-color) !important;
}

.badge.bg-primary {
    background-color: var(--primary-color) !important;
}

/* ===== Responsive User Management ===== */
@media (max-width: 768px) {
    .user-actions,
    .logs-actions {
        flex-direction: column;
    }

    .user-actions .btn,
    .logs-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .table-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .table-controls input {
        max-width: none;
        margin-bottom: 0.5rem;
    }

    .logs-controls .row {
        margin: 0;
    }

    .logs-controls .col-md-3,
    .logs-controls .col-md-2,
    .logs-controls .col-md-1 {
        padding: 0.5rem 0;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: var(--border-radius) !important;
        margin-bottom: 0.25rem;
    }

    .user-modal .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .table-responsive {
        font-size: 0.8rem;
    }

    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
    }
}

/* ===== Empty State Enhancements ===== */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-muted);
}

.empty-state h4 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.empty-state p {
    margin-bottom: 1.5rem;
}

/* ===== Form Enhancements for Modals ===== */
.user-modal .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.user-modal .form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.user-modal .form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

/* ===== Loading States for Tables ===== */
.table-loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ===== Scrollbar for Activity Lists ===== */
.activity-list::-webkit-scrollbar,
.logs-table-container::-webkit-scrollbar {
    width: 6px;
}

.activity-list::-webkit-scrollbar-track,
.logs-table-container::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

.activity-list::-webkit-scrollbar-thumb,
.logs-table-container::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb:hover,
.logs-table-container::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* ===== About System Styles ===== */
.about-system-container {
    max-width: 1200px;
    margin: 0 auto;
}

.about-header {
    margin-bottom: 3rem;
    padding: 2rem;
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    border-radius: var(--border-radius-lg);
    color: white;
}

.system-logo {
    margin-bottom: 1rem;
}

.system-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.system-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.system-version {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 1rem;
}

.system-description {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.about-content {
    margin-bottom: 3rem;
}

.info-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    height: 100%;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.info-card .card-header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.info-card .card-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
}

.info-card .card-body {
    padding: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
    transition: var(--transition);
}

.features-list li:hover {
    background-color: var(--bg-secondary);
    padding-left: 1rem;
    margin: 0 -1rem;
    border-radius: var(--border-radius);
}

.features-list li:last-child {
    border-bottom: none;
}

.technologies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
}

.tech-item {
    padding: 0.75rem;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    transition: var(--transition);
}

.tech-item:hover {
    background-color: var(--bg-tertiary);
    transform: translateY(-1px);
}

.export-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.security-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.security-list li {
    padding: 0.5rem 0;
    color: var(--text-primary);
}

.about-footer {
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.footer-content p {
    margin-bottom: 0.5rem;
}

.footer-content p:last-child {
    margin-bottom: 0;
}

/* ===== Responsive About System ===== */
@media (max-width: 768px) {
    .about-header {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .system-icon {
        font-size: 3rem;
    }

    .system-title {
        font-size: 2rem;
    }

    .system-version {
        font-size: 1rem;
    }

    .system-description {
        font-size: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .technologies-grid {
        grid-template-columns: 1fr;
    }

    .export-actions {
        flex-direction: column;
    }

    .export-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .info-card .card-body {
        padding: 1rem;
    }

    .about-footer {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    .about-header {
        padding: 1rem;
    }

    .system-title {
        font-size: 1.75rem;
    }

    .features-list li:hover {
        margin: 0;
        padding-left: 0;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }
}

/* ===== Print Styles for About Page ===== */
@media print {
    .about-header {
        background: none !important;
        color: #333 !important;
        border: 2px solid #333;
    }

    .info-card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ddd;
    }

    .export-actions {
        display: none !important;
    }

    .about-footer {
        border-top: 2px solid #333;
        background: none !important;
    }
}

/* ===== Scanner Integration Styles ===== */
.scanner-container {
    max-width: 1200px;
    margin: 0 auto;
}

.scanner-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.scanner-header h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.scanner-status {
    margin-bottom: 2rem;
}

.scanner-controls .card {
    height: 100%;
}

.scan-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    justify-content: center;
}

.scan-actions .btn {
    min-width: 140px;
}

.scan-progress {
    margin-top: 1.5rem;
}

.scan-status-text {
    text-align: center;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 1rem;
}

.scan-preview .card {
    height: 100%;
}

.preview-container {
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 2px dashed var(--border-color);
}

.preview-placeholder {
    text-align: center;
    color: var(--text-secondary);
}

.preview-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-muted);
}

.preview-image {
    max-height: 280px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.preview-actions {
    text-align: center;
}

.scan-history .card {
    height: 100%;
}

.scan-history-item {
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.scan-history-item:hover {
    background-color: var(--bg-secondary);
}

.scan-history-item:last-child {
    border-bottom: none;
}

.history-file-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.scanner-help .card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
}

.help-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.help-list li {
    padding: 0.5rem 0;
    color: var(--text-primary);
    position: relative;
    padding-left: 1.5rem;
}

.help-list li:before {
    content: "•";
    color: var(--primary-color);
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* ===== Scan Result Modal Styles ===== */
.scan-result-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.scan-result-modal .modal-content {
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.scan-preview-large {
    text-align: center;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.scan-preview-large img {
    max-width: 100%;
    max-height: 400px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.scan-info {
    background-color: var(--bg-secondary);
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.scan-info h6 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

/* ===== Scanner Settings Styles ===== */
.scanner-controls .form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.scanner-controls .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

/* ===== Progress Bar Enhancements ===== */
.progress {
    height: 8px;
    background-color: var(--bg-secondary);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.progress-bar-striped {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem;
}

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position-x: 1rem;
    }
}

/* ===== Responsive Scanner Styles ===== */
@media (max-width: 768px) {
    .scan-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .scan-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
        min-width: auto;
    }

    .scanner-controls .row {
        margin: 0;
    }

    .scanner-controls .col-md-6 {
        padding: 0.5rem 0;
    }

    .preview-container {
        min-height: 200px;
    }

    .preview-icon {
        font-size: 2rem;
    }

    .scan-result-modal .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .scan-preview-large {
        padding: 0.5rem;
    }

    .scan-preview-large img {
        max-height: 250px;
    }

    .help-list li {
        font-size: 0.9rem;
        padding: 0.25rem 0;
    }
}

@media (max-width: 576px) {
    .scanner-header {
        padding: 1rem;
    }

    .scanner-header h4 {
        font-size: 1.5rem;
    }

    .scan-actions .btn {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }

    .preview-container {
        min-height: 150px;
    }

    .scan-info {
        padding: 0.75rem;
    }

    .scan-info h6 {
        font-size: 0.9rem;
    }

    .scan-info small {
        font-size: 0.8rem;
    }
}

/* ===== Scanner Status Alerts ===== */
.scanner-status .alert {
    border-radius: var(--border-radius);
    border: none;
    font-weight: 500;
}

.scanner-status .alert-info {
    background-color: rgba(8, 145, 178, 0.1);
    color: var(--info-color);
}

.scanner-status .alert-success {
    background-color: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
}

.scanner-status .alert-warning {
    background-color: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
}

.scanner-status .alert-danger {
    background-color: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

/* ===== Scanner Animation Effects ===== */
.scanner-controls .card {
    transition: var(--transition);
}

.scanner-controls .card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.scan-history-item {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== Print Styles for Scanner ===== */
@media print {
    .scan-actions,
    .scanner-status,
    .scanner-help {
        display: none !important;
    }

    .scan-preview-large img {
        max-width: 100% !important;
        height: auto !important;
    }
}
