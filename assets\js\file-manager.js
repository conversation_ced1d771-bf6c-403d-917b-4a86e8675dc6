// ===== File Management System =====

class FileManager {
    constructor() {
        this.currentFiles = [];
        this.currentFilters = {};
        this.selectedFiles = new Set();
        this.uploadQueue = [];
        this.initializeFileManager();
    }

    // Initialize file manager
    async initializeFileManager() {
        console.log('Initializing FileManager components...');

        this.setupFileUpload();
        this.setupFileFilters();
        this.setupFileActions();

        // Wait for database to be ready
        const dbReady = await this.waitForDatabase();
        if (dbReady) {
            console.log('Database ready, loading files...');
            await this.loadFiles();
        } else {
            console.error('Database not ready, showing error');
            this.showAlert('error', 'فشل في تهيئة قاعدة البيانات. يرجى إعادة تحميل الصفحة.');
        }
    }

    // Wait for database to be ready
    async waitForDatabase() {
        let attempts = 0;
        const maxAttempts = 30; // 3 seconds max wait

        // Wait for localDB to be available
        while (!window.localDB && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }

        if (!window.localDB) {
            console.error('Database not available after waiting');
            return false;
        }

        console.log('Database is available');
        return true;
    }

    // Setup file upload functionality
    setupFileUpload() {
        // Create file upload area
        this.createUploadArea();
        
        // Handle file input change
        document.addEventListener('change', (e) => {
            if (e.target.type === 'file') {
                this.handleFileSelect(e.target.files);
            }
        });

        // Handle drag and drop
        document.addEventListener('dragover', (e) => {
            e.preventDefault();
            const dropZone = e.target.closest('.file-drop-zone');
            if (dropZone) {
                dropZone.classList.add('drag-over');
            }
        });

        document.addEventListener('dragleave', (e) => {
            const dropZone = e.target.closest('.file-drop-zone');
            if (dropZone && !dropZone.contains(e.relatedTarget)) {
                dropZone.classList.remove('drag-over');
            }
        });

        document.addEventListener('drop', (e) => {
            e.preventDefault();
            const dropZone = e.target.closest('.file-drop-zone');
            if (dropZone) {
                dropZone.classList.remove('drag-over');
                this.handleFileSelect(e.dataTransfer.files);
            }
        });
    }

    // Create upload area HTML
    createUploadArea() {
        const uploadHTML = `
            <div class="file-upload-section">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cloud-upload-alt me-2"></i>رفع ملف جديد</h5>
                    </div>
                    <div class="card-body">
                        <div class="file-drop-zone" onclick="document.getElementById('fileInput').click()">
                            <div class="drop-zone-content">
                                <i class="fas fa-cloud-upload-alt drop-icon"></i>
                                <h4>اسحب وأفلت الملفات هنا</h4>
                                <p>أو انقر لاختيار الملفات</p>
                                <small class="text-muted">الصيغ المدعومة: PDF, DOC, DOCX, JPG, PNG (الحد الأقصى: 10MB)</small>
                            </div>
                            <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" style="display: none;">
                        </div>
                        
                        <div class="upload-queue mt-3" id="uploadQueue" style="display: none;">
                            <h6>قائمة الانتظار:</h6>
                            <div id="uploadList"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Add to add-file section
        const addFileSection = document.getElementById('add-file-section');
        if (addFileSection) {
            addFileSection.innerHTML = uploadHTML;
        }
    }

    // Handle file selection
    async handleFileSelect(files) {
        const fileArray = Array.from(files);
        
        for (const file of fileArray) {
            if (this.validateFile(file)) {
                await this.processFile(file);
            }
        }
        
        this.updateUploadQueue();
    }

    // Validate file
    validateFile(file) {
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/jpg',
            'image/png'
        ];

        if (file.size > maxSize) {
            this.showAlert('error', `الملف "${file.name}" كبير جداً. الحد الأقصى 10MB`);
            return false;
        }

        if (!allowedTypes.includes(file.type)) {
            this.showAlert('error', `نوع الملف "${file.name}" غير مدعوم`);
            return false;
        }

        return true;
    }

    // Process file for upload
    async processFile(file) {
        const fileData = {
            id: this.generateId(),
            file: file,
            name: file.name,
            originalName: file.name,
            type: file.type,
            size: file.size,
            status: 'pending',
            progress: 0,
            category: this.detectFileCategory(file.name),
            issueDate: new Date().toISOString().split('T')[0],
            description: '',
            tags: []
        };

        this.uploadQueue.push(fileData);
        this.showUploadForm(fileData);
    }

    // Detect file category based on name
    detectFileCategory(fileName) {
        const name = fileName.toLowerCase();
        
        if (name.includes('مخالف') || name.includes('يومية')) {
            return 'يومية مخالفات';
        } else if (name.includes('افادة') || name.includes('إفادة')) {
            return 'إفادة';
        } else if (name.includes('اجازة') || name.includes('إجازة')) {
            return 'مستند إجازة';
        } else if (name.includes('رخصة') || name.includes('ادارية')) {
            return 'رخصة إدارية';
        }
        
        return 'يومية مخالفات'; // Default
    }

    // Show upload form
    showUploadForm(fileData) {
        const formHTML = `
            <div class="upload-form-modal" id="uploadForm_${fileData.id}">
                <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h5>تفاصيل الملف: ${fileData.name}</h5>
                        <button class="btn-close" onclick="this.closest('.upload-form-modal').remove()"></button>
                    </div>
                    <div class="modal-body">
                        <form id="fileDetailsForm_${fileData.id}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" data-translate="file_name">اسم الملف</label>
                                    <input type="text" class="form-control" name="name" value="${fileData.name}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" data-translate="category">التصنيف</label>
                                    <select class="form-control" name="category" required>
                                        <option value="يومية مخالفات" ${fileData.category === 'يومية مخالفات' ? 'selected' : ''} data-translate="violation_diary">يومية مخالفات</option>
                                        <option value="إفادة" ${fileData.category === 'إفادة' ? 'selected' : ''} data-translate="certificate">إفادة</option>
                                        <option value="مستند إجازة" ${fileData.category === 'مستند إجازة' ? 'selected' : ''} data-translate="leave_document">مستند إجازة</option>
                                        <option value="رخصة إدارية" ${fileData.category === 'رخصة إدارية' ? 'selected' : ''} data-translate="admin_license">رخصة إدارية</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" data-translate="course_code">رمز الدورة</label>
                                    <input type="text" class="form-control" name="courseCode" placeholder="" data-translate-placeholder="course_code_placeholder" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" data-translate="course_number">رقم الدورة</label>
                                    <input type="number" class="form-control" name="courseNumber" placeholder="" data-translate-placeholder="course_number_placeholder" required min="1">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" data-translate="issue_date">تاريخ الإصدار</label>
                                    <input type="date" class="form-control" name="issueDate" value="${fileData.issueDate}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" data-translate="keywords">الكلمات المفتاحية</label>
                                    <input type="text" class="form-control" name="tags" placeholder="" data-translate-placeholder="keywords_placeholder">
                                </div>
                                <div class="col-12 mb-3">
                                    <label class="form-label" data-translate="description">الوصف</label>
                                    <textarea class="form-control" name="description" rows="3" placeholder="" data-translate-placeholder="description_placeholder"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.upload-form-modal').remove()">إلغاء</button>
                        <button class="btn btn-primary" onclick="fileManager.uploadFile('${fileData.id}')">رفع الملف</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', formHTML);
    }

    // Upload file to database
    async uploadFile(fileId) {
        const fileData = this.uploadQueue.find(f => f.id === fileId);
        if (!fileData) return;

        const form = document.getElementById(`fileDetailsForm_${fileId}`);
        const formData = new FormData(form);
        
        try {
            // Show loading
            this.updateFileStatus(fileId, 'uploading', 0);
            
            // Convert file to base64
            const base64Content = await this.fileToBase64(fileData.file);
            
            // Prepare file data for database
            const dbFileData = {
                name: formData.get('name'),
                originalName: fileData.originalName,
                category: formData.get('category'),
                type: fileData.type,
                size: fileData.size,
                content: base64Content,
                description: formData.get('description'),
                tags: formData.get('tags').split(',').map(tag => tag.trim()).filter(tag => tag),
                issueDate: formData.get('issueDate'),
                courseCode: formData.get('courseCode'),
                courseNumber: parseInt(formData.get('courseNumber')),
                createdBy: window.authSystem?.currentUser?.id || 'unknown'
            };

            // Simulate upload progress
            for (let progress = 0; progress <= 100; progress += 20) {
                this.updateFileStatus(fileId, 'uploading', progress);
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // Save to database
            const savedFile = await window.localDB.addFile(dbFileData);
            
            // Update status
            this.updateFileStatus(fileId, 'completed', 100);
            
            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('file_upload', `تم رفع الملف: ${savedFile.name}`);
            }
            
            // Remove from queue after delay
            setTimeout(() => {
                this.removeFromQueue(fileId);
                document.getElementById(`uploadForm_${fileId}`)?.remove();
            }, 2000);
            
            // Refresh file list
            this.loadFiles();
            
            this.showAlert('success', 'تم رفع الملف بنجاح');
            
        } catch (error) {
            console.error('Upload error:', error);
            this.updateFileStatus(fileId, 'error', 0);
            this.showAlert('error', 'حدث خطأ أثناء رفع الملف');
        }
    }

    // Convert file to base64
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }

    // Update file status in queue
    updateFileStatus(fileId, status, progress) {
        const fileData = this.uploadQueue.find(f => f.id === fileId);
        if (fileData) {
            fileData.status = status;
            fileData.progress = progress;
            this.updateUploadQueue();
        }
    }

    // Remove file from queue
    removeFromQueue(fileId) {
        this.uploadQueue = this.uploadQueue.filter(f => f.id !== fileId);
        this.updateUploadQueue();
    }

    // Update upload queue display
    updateUploadQueue() {
        const queueContainer = document.getElementById('uploadQueue');
        const listContainer = document.getElementById('uploadList');
        
        if (!queueContainer || !listContainer) return;
        
        if (this.uploadQueue.length === 0) {
            queueContainer.style.display = 'none';
            return;
        }
        
        queueContainer.style.display = 'block';
        listContainer.innerHTML = this.uploadQueue.map(file => `
            <div class="upload-item">
                <div class="upload-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-size">${this.formatFileSize(file.size)}</div>
                </div>
                <div class="upload-progress">
                    <div class="progress">
                        <div class="progress-bar ${this.getProgressBarClass(file.status)}" 
                             style="width: ${file.progress}%"></div>
                    </div>
                    <small class="status-text">${this.getStatusText(file.status)}</small>
                </div>
            </div>
        `).join('');
    }

    // Get progress bar class based on status
    getProgressBarClass(status) {
        switch (status) {
            case 'completed': return 'bg-success';
            case 'error': return 'bg-danger';
            case 'uploading': return 'bg-primary';
            default: return 'bg-secondary';
        }
    }

    // Get status text
    getStatusText(status) {
        switch (status) {
            case 'pending': return 'في الانتظار';
            case 'uploading': return 'جاري الرفع...';
            case 'completed': return 'تم بنجاح';
            case 'error': return 'خطأ';
            default: return '';
        }
    }

    // Format file size
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Format date
    formatDate(dateString) {
        if (!dateString) return 'غير محدد';

        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return 'تاريخ غير صحيح';

            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } catch (error) {
            console.error('Error formatting date:', error);
            return 'تاريخ غير صحيح';
        }
    }

    // Render files based on current view
    renderFiles() {
        if (this.currentFiles.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-folder-open"></i>
                    <h4>لا توجد ملفات</h4>
                    <p>لم يتم العثور على أي ملفات تطابق معايير البحث</p>
                    <button class="btn btn-primary" onclick="showSection('add-file')">
                        <i class="fas fa-plus"></i> إضافة ملف جديد
                    </button>
                </div>
            `;
        }

        return `
            <div class="files-grid" id="filesGrid">
                ${this.currentFiles.map(file => this.renderFileCard(file)).join('')}
            </div>
        `;
    }

    // Render individual file card
    renderFileCard(file) {
        // Ensure file object has required properties
        const safeFile = {
            id: file.id || 'unknown',
            name: file.name || 'ملف بدون اسم',
            type: file.type || 'application/octet-stream',
            size: file.size || 0,
            category: file.category || 'غير مصنف',
            description: file.description || '',
            tags: file.tags || [],
            createdAt: file.createdAt || new Date().toISOString(),
            courseCode: file.courseCode || '',
            courseNumber: file.courseNumber || ''
        };

        const fileIcon = this.getFileIcon(safeFile.type);
        const fileSize = this.formatFileSize(safeFile.size);
        const createdDate = this.formatDate(safeFile.createdAt);

        return `
            <div class="file-card" onclick="fileManager.viewFile('${safeFile.id}')">
                <div class="file-card-header">
                    <div class="file-type-icon">
                        <i class="${fileIcon}"></i>
                    </div>
                    <div class="file-card-info">
                        <h6>${safeFile.name}</h6>
                        <div class="file-card-meta">
                            ${fileSize} • ${createdDate}
                        </div>
                    </div>
                </div>
                <div class="file-card-body">
                    <div class="file-category">${safeFile.category}</div>
                    ${safeFile.description ? `<div class="file-description">${safeFile.description}</div>` : ''}
                    ${safeFile.tags.length > 0 ? `
                        <div class="file-tags mt-2">
                            ${safeFile.tags.map(tag => `<span class="badge bg-secondary me-1">${tag}</span>`).join('')}
                        </div>
                    ` : ''}
                </div>
                <div class="file-card-footer">
                    <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); fileManager.viewFile('${safeFile.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="event.stopPropagation(); fileManager.downloadFile('${safeFile.id}')">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="event.stopPropagation(); fileManager.editFile('${safeFile.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    ${window.authSystem?.hasPermission('delete') ? `
                        <button class="btn btn-sm btn-outline-danger" onclick="event.stopPropagation(); fileManager.deleteFile('${safeFile.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // Decode base64 text content
    decodeBase64Text(base64Content) {
        try {
            // Remove data URL prefix if present
            const base64Data = base64Content.replace(/^data:[^;]+;base64,/, '');

            // Decode base64
            const decodedText = atob(base64Data);

            // Convert to UTF-8 if needed
            return decodeURIComponent(escape(decodedText));
        } catch (error) {
            console.error('Error decoding base64 text:', error);
            return 'خطأ في فك تشفير النص';
        }
    }

    // Get file icon based on type
    getFileIcon(fileType) {
        if (fileType.includes('pdf')) {
            return 'fas fa-file-pdf';
        } else if (fileType.includes('word') || fileType.includes('document')) {
            return 'fas fa-file-word';
        } else if (fileType.includes('image')) {
            return 'fas fa-file-image';
        } else {
            return 'fas fa-file';
        }
    }

    // Setup file filters
    setupFileFilters() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', () => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.applyFilters();
                }, 300);
            });
        }
    }

    // Apply filters
    applyFilters() {
        const searchTerm = document.getElementById('searchInput')?.value || '';
        const category = document.getElementById('categoryFilter')?.value || '';
        const dateFrom = document.getElementById('dateFromFilter')?.value || '';
        const dateTo = document.getElementById('dateToFilter')?.value || '';

        this.currentFilters = {
            search: searchTerm,
            category: category,
            dateFrom: dateFrom,
            dateTo: dateTo
        };

        this.loadFiles();
    }

    // Clear filters
    clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('categoryFilter').value = '';
        document.getElementById('dateFromFilter').value = '';
        document.getElementById('dateToFilter').value = '';

        this.currentFilters = {};
        this.loadFiles();
    }

    // Toggle view between grid and list
    toggleView(viewType) {
        // Update button states
        document.getElementById('gridViewBtn').classList.toggle('active', viewType === 'grid');
        document.getElementById('listViewBtn').classList.toggle('active', viewType === 'list');

        // Update view (for now, only grid is implemented)
        this.currentView = viewType;
    }

    // View file in modal
    async viewFile(fileId) {
        try {
            console.log('Viewing file with ID:', fileId);
            const file = await window.localDB.getFileById(fileId);

            if (!file) {
                console.error('File not found:', fileId);
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            console.log('File loaded for viewing:', {
                id: file.id,
                name: file.name,
                type: file.type,
                hasContent: !!file.content,
                contentLength: file.content ? file.content.length : 0,
                contentPreview: file.content ? file.content.substring(0, 50) + '...' : 'No content'
            });

            this.showFileViewer(file);

            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('file_view', `عرض الملف: ${file.name}`);
            }
        } catch (error) {
            console.error('Error viewing file:', error);
            this.showAlert('error', 'حدث خطأ أثناء عرض الملف: ' + error.message);
        }
    }

    // Show file viewer modal
    showFileViewer(file) {
        // Ensure file object has required properties
        const safeFile = {
            id: file.id || 'unknown',
            name: file.name || 'ملف بدون اسم',
            type: file.type || 'application/octet-stream',
            size: file.size || 0,
            category: file.category || 'غير مصنف',
            description: file.description || '',
            tags: file.tags || [],
            createdAt: file.createdAt || new Date().toISOString(),
            issueDate: file.issueDate || 'غير محدد',
            createdBy: file.createdBy || 'غير محدد',
            courseCode: file.courseCode || '',
            courseNumber: file.courseNumber || ''
        };
        const viewerHTML = `
            <div class="file-viewer-modal" id="fileViewer_${safeFile.id}">
                <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
                <div class="modal-content file-viewer-content">
                    <div class="modal-header">
                        <h5><i class="${this.getFileIcon(safeFile.type)} me-2"></i>${safeFile.name}</h5>
                        <div class="viewer-controls">
                            <button class="btn btn-sm btn-outline-secondary" onclick="fileManager.downloadFile('${safeFile.id}')">
                                <i class="fas fa-download"></i> تحميل
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="fileManager.printFile('${safeFile.id}')">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                            <button class="btn-close" onclick="this.closest('.file-viewer-modal').remove()">×</button>
                        </div>
                    </div>
                    <div class="modal-body p-0">
                        <div class="file-viewer-container">
                            ${this.renderFileContent(safeFile)}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <div class="file-info">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong data-translate="category">التصنيف:</strong> ${safeFile.category}<br>
                                    <strong data-translate="file_size">الحجم:</strong> ${this.formatFileSize(safeFile.size)}<br>
                                    <strong data-translate="created_date">تاريخ الإنشاء:</strong> ${this.formatDate(safeFile.createdAt)}<br>
                                    ${safeFile.courseCode ? `<strong data-translate="course_code">رمز الدورة:</strong> ${safeFile.courseCode}<br>` : ''}
                                </div>
                                <div class="col-md-6">
                                    <strong data-translate="issue_date">تاريخ الإصدار:</strong> ${safeFile.issueDate}<br>
                                    <strong data-translate="created_by">المنشئ:</strong> ${safeFile.createdBy}<br>
                                    ${safeFile.courseNumber ? `<strong data-translate="course_number">رقم الدورة:</strong> ${safeFile.courseNumber}<br>` : ''}
                                    ${safeFile.tags.length > 0 ? `<strong data-translate="keywords">الكلمات المفتاحية:</strong> ${safeFile.tags.join(', ')}` : ''}
                                </div>
                            </div>
                            ${safeFile.description ? `<div class="mt-2"><strong>الوصف:</strong> ${safeFile.description}</div>` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', viewerHTML);
    }

    // Render file content based on type
    renderFileContent(file) {
        console.log('Rendering file content for:', file.name, 'Has content:', !!file.content, 'Content length:', file.content?.length);

        // Check if file has content
        if (!file.content) {
            return `
                <div class="file-preview text-center p-4">
                    <i class="${this.getFileIcon(file.type)}" style="font-size: 4rem; color: var(--primary-color);"></i>
                    <h4 class="mt-3">${file.name}</h4>
                    <p class="text-muted">محتوى الملف غير متوفر للمعاينة</p>
                    <div class="mt-3">
                        <button class="btn btn-primary me-2" onclick="fileManager.downloadFile('${file.id}')">
                            <i class="fas fa-download me-2"></i>تحميل الملف
                        </button>
                        <button class="btn btn-warning me-2" onclick="fileManager.debugFile('${file.id}')">
                            <i class="fas fa-bug me-2"></i>فحص الملف
                        </button>
                        <button class="btn btn-info" onclick="fileManager.reuploadFile('${file.id}')">
                            <i class="fas fa-upload me-2"></i>إعادة رفع
                        </button>
                    </div>
                </div>
            `;
        }

        // Prepare content URL
        let contentUrl = file.content;

        // If content is base64, create data URL
        if (file.content.startsWith('data:')) {
            contentUrl = file.content;
        } else if (file.content.length > 100 && !file.content.startsWith('http')) {
            // Assume it's base64 without data URL prefix
            contentUrl = `data:${file.type};base64,${file.content}`;
        }

        if (file.type.includes('pdf')) {
            return `
                <div class="pdf-viewer">
                    <div class="pdf-viewer-header text-center mb-3">
                        <h5><i class="fas fa-file-pdf me-2"></i>${file.name}</h5>
                        <div class="pdf-controls">
                            <button class="btn btn-sm btn-primary me-2" onclick="fileManager.openPdfWithBlob('${file.id}')">
                                <i class="fas fa-external-link-alt me-1"></i>عرض PDF
                            </button>
                            <button class="btn btn-sm btn-secondary me-2" onclick="fileManager.downloadPdfDirect('${file.id}')">
                                <i class="fas fa-download me-1"></i>تحميل
                            </button>
                            <button class="btn btn-sm btn-success" onclick="fileManager.showPdfInlineInViewer('${file.id}')">
                                <i class="fas fa-eye me-1"></i>عرض مضمن
                            </button>
                        </div>
                    </div>

                    <div id="pdf-inline-container-${file.id}" style="display: none;">
                        <div class="pdf-loading text-center p-3">
                            <i class="fas fa-spinner fa-spin"></i> جاري تحميل PDF...
                        </div>
                    </div>

                    <div id="pdf-fallback-${file.id}" class="text-center p-4">
                        <i class="fas fa-file-pdf" style="font-size: 4rem; color: #007bff;"></i>
                        <h4 class="mt-3">ملف PDF جاهز للعرض</h4>
                        <p class="text-muted">
                            انقر على "عرض PDF" لفتح الملف في نافذة جديدة<br>
                            أو "عرض مضمن" لعرضه في هذه النافذة
                        </p>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                الحجم: ${this.formatFileSize(file.size)}
                            </small>
                        </div>
                    </div>
                </div>
            `;
        } else if (file.type.includes('image')) {
            return `
                <div class="image-viewer">
                    <div class="image-viewer-header text-center mb-3">
                        <h5><i class="fas fa-image me-2"></i>${file.name}</h5>
                        <div class="image-controls">
                            <button class="btn btn-sm btn-primary me-2" onclick="fileManager.openImageWithBlob('${file.id}')">
                                <i class="fas fa-external-link-alt me-1"></i>فتح الصورة
                            </button>
                            <button class="btn btn-sm btn-secondary me-2" onclick="fileManager.downloadImageDirect('${file.id}')">
                                <i class="fas fa-download me-1"></i>تحميل
                            </button>
                            <button class="btn btn-sm btn-success" onclick="fileManager.showImageInlineInViewer('${file.id}')">
                                <i class="fas fa-eye me-1"></i>عرض مضمن
                            </button>
                        </div>
                    </div>

                    <div id="image-inline-container-${file.id}" style="display: none;">
                        <div class="image-loading text-center p-3">
                            <i class="fas fa-spinner fa-spin"></i> جاري تحميل الصورة...
                        </div>
                    </div>

                    <div id="image-fallback-${file.id}" class="text-center p-4">
                        <i class="fas fa-image" style="font-size: 4rem; color: #28a745;"></i>
                        <h4 class="mt-3">صورة جاهزة للعرض</h4>
                        <p class="text-muted">
                            انقر على "فتح الصورة" لعرضها في نافذة جديدة<br>
                            أو "عرض مضمن" لعرضها في هذه النافذة
                        </p>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                الحجم: ${this.formatFileSize(file.size)}
                            </small>
                        </div>
                    </div>
                </div>
            `;
        } else if (file.type.includes('text')) {
            return `
                <div class="text-viewer p-3">
                    <pre style="white-space: pre-wrap; max-height: 600px; overflow-y: auto;">${this.decodeBase64Text(file.content)}</pre>
                </div>
            `;
        } else {
            return `
                <div class="file-preview text-center p-4">
                    <i class="${this.getFileIcon(file.type)}" style="font-size: 4rem; color: var(--primary-color);"></i>
                    <h4 class="mt-3">${file.name}</h4>
                    <p class="text-muted">معاينة غير متاحة لهذا النوع من الملفات</p>
                    <button class="btn btn-primary" onclick="fileManager.downloadFile('${file.id}')">
                        <i class="fas fa-download me-2"></i>تحميل الملف
                    </button>
                </div>
            `;
        }
    }

    // Download file
    async downloadFile(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            // Create download link
            const link = document.createElement('a');
            link.href = file.content;
            link.download = file.originalName || file.name;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('file_download', `تحميل الملف: ${file.name}`);
            }

            this.showAlert('success', 'تم تحميل الملف بنجاح');
        } catch (error) {
            console.error('Error downloading file:', error);
            this.showAlert('error', 'حدث خطأ أثناء تحميل الملف');
        }
    }

    // Print file
    async printFile(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            // Open print window
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>طباعة - ${file.name}</title>
                        <style>
                            body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
                            .header { text-align: center; margin-bottom: 20px; border-bottom: 2px solid #333; padding-bottom: 10px; }
                            .content { text-align: center; }
                            img { max-width: 100%; height: auto; }
                            iframe { width: 100%; height: 80vh; border: none; }
                        </style>
                    </head>
                    <body>
                        <div class="header">
                            <h2>${file.name}</h2>
                            <p>التصنيف: ${file.category} | تاريخ الإصدار: ${file.issueDate}</p>
                        </div>
                        <div class="content">
                            ${file.type.includes('image') ?
                                `<img src="${file.content}" alt="${file.name}">` :
                                `<iframe src="${file.content}"></iframe>`
                            }
                        </div>
                    </body>
                </html>
            `);
            printWindow.document.close();

            setTimeout(() => {
                printWindow.print();
            }, 1000);

            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('file_print', `طباعة الملف: ${file.name}`);
            }
        } catch (error) {
            console.error('Error printing file:', error);
            this.showAlert('error', 'حدث خطأ أثناء طباعة الملف');
        }
    }

    // Edit file (placeholder)
    editFile(fileId) {
        this.showAlert('info', 'ميزة تعديل الملفات قيد التطوير');
    }

    // Delete file
    async deleteFile(fileId) {
        if (!confirm('هل أنت متأكد من حذف هذا الملف؟ لا يمكن التراجع عن هذا الإجراء.')) {
            return;
        }

        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            await window.localDB.deleteFile(fileId);

            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('file_delete', `حذف الملف: ${file.name}`);
            }

            this.showAlert('success', 'تم حذف الملف بنجاح');
            this.loadFiles(); // Refresh file list
        } catch (error) {
            console.error('Error deleting file:', error);
            this.showAlert('error', 'حدث خطأ أثناء حذف الملف');
        }
    }

    // Setup file actions
    setupFileActions() {
        // File actions are handled through onclick events in the HTML
    }

    // Load files from database
    async loadFiles() {
        try {
            // Check if database is available
            if (!window.localDB) {
                console.warn('Database not available, waiting...');
                const dbReady = await this.waitForDatabase();
                if (!dbReady) {
                    throw new Error('Database initialization failed');
                }
            }

            console.log('Loading files with filters:', this.currentFilters);
            this.currentFiles = await window.localDB.getFiles(this.currentFilters);
            console.log('Files loaded successfully:', this.currentFiles.length);

            this.displayFiles();
        } catch (error) {
            console.error('Error loading files:', error);

            // Try to initialize with empty array if database fails
            this.currentFiles = [];
            this.displayFiles();

            // Show error with reload and reset options
            this.showAlert('error', `
                حدث خطأ أثناء تحميل الملفات: ${error.message}
                <div class="mt-2">
                    <button class="btn btn-sm btn-light me-2" onclick="location.reload()">إعادة تحميل</button>
                    <button class="btn btn-sm btn-warning me-2" onclick="fileManager.resetDatabase()">إعادة تهيئة البيانات</button>
                    <button class="btn btn-sm btn-info" onclick="fileManager.debugDatabase()">فحص قاعدة البيانات</button>
                </div>
            `);
        }
    }

    // Display files
    displayFiles() {
        try {
            const filesSection = document.getElementById('files-section');
            if (!filesSection) {
                console.warn('files-section element not found');
                return;
            }

            // Ensure currentFiles is an array
            if (!Array.isArray(this.currentFiles)) {
                console.warn('currentFiles is not an array, initializing as empty array');
                this.currentFiles = [];
            }

        const filesHTML = `
            <div class="files-management">
                <div class="search-filters">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label>البحث</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="ابحث في الملفات...">
                        </div>
                        <div class="filter-group">
                            <label>التصنيف</label>
                            <select class="form-control" id="categoryFilter">
                                <option value="">جميع التصنيفات</option>
                                <option value="يومية مخالفات">يومية مخالفات</option>
                                <option value="إفادة">إفادة</option>
                                <option value="مستند إجازة">مستند إجازة</option>
                                <option value="رخصة إدارية">رخصة إدارية</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>من تاريخ</label>
                            <input type="date" class="form-control" id="dateFromFilter">
                        </div>
                        <div class="filter-group">
                            <label>إلى تاريخ</label>
                            <input type="date" class="form-control" id="dateToFilter">
                        </div>
                        <div class="filter-actions">
                            <button class="btn btn-primary" onclick="fileManager.applyFilters()">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <button class="btn btn-secondary" onclick="fileManager.clearFilters()">
                                <i class="fas fa-times"></i> مسح
                            </button>
                        </div>
                    </div>
                </div>

                <div class="files-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>الملفات (${this.currentFiles.length})</h5>
                        <div class="view-controls">
                            <button class="btn btn-success btn-sm me-2" onclick="fileManager.createTestFile()" title="إنشاء ملف تجريبي للاختبار">
                                <i class="fas fa-plus-circle me-1"></i>ملف تجريبي
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="fileManager.toggleView('grid')" id="gridViewBtn">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="fileManager.toggleView('list')" id="listViewBtn">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="files-container" id="filesContainer">
                    ${this.renderFiles()}
                </div>
            </div>
        `;

            filesSection.innerHTML = filesHTML;
            this.setupFileFilters();
        } catch (error) {
            console.error('Error displaying files:', error);

            // Show basic error message
            const filesSection = document.getElementById('files-section');
            if (filesSection) {
                filesSection.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>خطأ في عرض الملفات</h5>
                        <p>حدث خطأ أثناء عرض الملفات: ${error.message}</p>
                        <button class="btn btn-primary" onclick="fileManager.loadFiles()">إعادة المحاولة</button>
                    </div>
                `;
            }
        }
    }

    // Generate unique ID
    generateId() {
        return 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Save file directly (for testing)
    async saveFile(fileData) {
        try {
            console.log('Saving file:', fileData.name);

            // Generate ID if not provided
            if (!fileData.id) {
                fileData.id = this.generateId();
            }

            // Add timestamp
            fileData.createdAt = new Date().toISOString();
            fileData.userId = window.authSystem?.currentUser?.id || 'admin';

            // Save to database
            await window.localDB.saveFile(fileData);

            console.log('File saved successfully:', fileData.id);

            // Refresh file list if we're on the file manager page
            if (this.currentFiles) {
                await this.loadFiles();
            }

            return fileData.id;

        } catch (error) {
            console.error('Error saving file:', error);
            throw error;
        }
    }

    // Reset database
    async resetDatabase() {
        try {
            if (confirm('هل أنت متأكد من إعادة تهيئة قاعدة البيانات؟ سيتم حذف جميع البيانات الحالية.')) {
                // Clear localStorage completely
                localStorage.removeItem('archive_files');
                localStorage.removeItem('archive_folders');
                localStorage.removeItem('archive_activity_logs');

                // Force re-initialization
                if (window.localDB) {
                    window.localDB.initializeLocalStorage();
                }

                this.showAlert('success', 'تم إعادة تهيئة قاعدة البيانات بنجاح');

                // Wait a bit then reload files
                setTimeout(async () => {
                    await this.loadFiles();
                }, 500);
            }
        } catch (error) {
            console.error('Error resetting database:', error);
            this.showAlert('error', 'حدث خطأ أثناء إعادة تهيئة قاعدة البيانات: ' + error.message);
        }
    }

    // Debug database
    debugDatabase() {
        console.log('=== Database Debug Info ===');
        console.log('window.localDB exists:', !!window.localDB);

        if (window.localDB) {
            console.log('Database type:', window.localDB.db ? 'IndexedDB' : 'localStorage');
            console.log('Database object:', window.localDB.db);

            // Check localStorage
            const files = localStorage.getItem('archive_files');
            console.log('localStorage archive_files:', files);

            if (files) {
                try {
                    const parsedFiles = JSON.parse(files);
                    console.log('Parsed files count:', parsedFiles.length);
                    console.log('Sample file:', parsedFiles[0]);

                    // Check file content
                    if (parsedFiles.length > 0) {
                        const firstFile = parsedFiles[0];
                        console.log('First file content info:', {
                            hasContent: !!firstFile.content,
                            contentType: typeof firstFile.content,
                            contentLength: firstFile.content ? firstFile.content.length : 0,
                            isDataUrl: firstFile.content ? firstFile.content.startsWith('data:') : false
                        });
                    }
                } catch (e) {
                    console.error('Error parsing files from localStorage:', e);
                }
            }
        }

        this.showAlert('info', 'تم طباعة معلومات قاعدة البيانات في وحدة التحكم (Console)');
    }

    // Debug specific file
    async debugFile(fileId) {
        try {
            console.log('=== File Debug Info ===');
            console.log('File ID:', fileId);

            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                console.error('File not found in database');
                this.showAlert('error', 'الملف غير موجود في قاعدة البيانات');
                return;
            }

            console.log('File object:', file);
            console.log('File properties:', {
                id: file.id,
                name: file.name,
                type: file.type,
                size: file.size,
                hasContent: !!file.content,
                contentType: typeof file.content,
                contentLength: file.content ? file.content.length : 0,
                contentPreview: file.content ? file.content.substring(0, 100) + '...' : 'No content',
                isDataUrl: file.content ? file.content.startsWith('data:') : false
            });

            this.showAlert('info', 'تم طباعة معلومات الملف في وحدة التحكم (Console)');

        } catch (error) {
            console.error('Error debugging file:', error);
            this.showAlert('error', 'خطأ في فحص الملف: ' + error.message);
        }
    }

    // Re-upload file (for files without content)
    async reuploadFile(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            // Create file input for re-upload
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = file.type.includes('image') ? 'image/*' : '.pdf';
            input.style.display = 'none';

            input.onchange = async (e) => {
                const newFile = e.target.files[0];
                if (!newFile) return;

                try {
                    // Convert to base64
                    const base64Content = await this.fileToBase64(newFile);

                    // Update file with new content
                    const updatedFile = {
                        ...file,
                        content: base64Content,
                        size: newFile.size,
                        type: newFile.type
                    };

                    // Save updated file
                    await window.localDB.updateFile(file.id, updatedFile);

                    this.showAlert('success', 'تم تحديث الملف بنجاح');

                    // Refresh file list
                    await this.loadFiles();

                } catch (error) {
                    console.error('Error re-uploading file:', error);
                    this.showAlert('error', 'خطأ في إعادة رفع الملف: ' + error.message);
                }

                // Clean up
                document.body.removeChild(input);
            };

            document.body.appendChild(input);
            input.click();

        } catch (error) {
            console.error('Error in reupload process:', error);
            this.showAlert('error', 'خطأ في عملية إعادة الرفع: ' + error.message);
        }
    }

    // Create test file with content (for debugging)
    async createTestFile() {
        try {
            // Create a simple test image (1x1 pixel PNG)
            const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

            const testFile = {
                name: 'test-image.png',
                originalName: 'test-image.png',
                type: 'image/png',
                size: 95,
                content: testImageBase64,
                category: 'صورة تجريبية',
                description: 'ملف تجريبي لاختبار النظام',
                tags: ['تجريبي', 'اختبار'],
                courseCode: 'TEST-001',
                courseNumber: 1,
                issueDate: new Date().toISOString().split('T')[0],
                createdBy: 'admin'
            };

            const savedFile = await window.localDB.addFile(testFile);
            this.showAlert('success', 'تم إنشاء ملف تجريبي بنجاح');

            // Refresh file list
            await this.loadFiles();

            return savedFile.id;

        } catch (error) {
            console.error('Error creating test file:', error);
            this.showAlert('error', 'خطأ في إنشاء الملف التجريبي: ' + error.message);
        }
    }

    // Test file preview
    async testFilePreview(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                console.error('File not found for preview test');
                return;
            }

            console.log('=== File Preview Test ===');
            console.log('File:', file);
            console.log('Content preview:', file.content ? file.content.substring(0, 100) : 'No content');

            // Test content URL creation
            let contentUrl = file.content;
            if (file.content && !file.content.startsWith('data:') && file.content.length > 100) {
                contentUrl = `data:${file.type};base64,${file.content}`;
            }

            console.log('Generated content URL:', contentUrl.substring(0, 100) + '...');

            // Create test element
            if (file.type.includes('image')) {
                const testImg = new Image();
                testImg.onload = () => console.log('✅ Image test successful');
                testImg.onerror = (e) => console.error('❌ Image test failed:', e);
                testImg.src = contentUrl;
            }

        } catch (error) {
            console.error('Preview test failed:', error);
        }
    }

    // Open PDF with Blob URL in new tab
    async openPdfWithBlob(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            console.log('Opening PDF with blob for file:', file.name);

            // Create blob from base64
            const blobUrl = this.createPdfBlob(file);

            if (blobUrl) {
                // Open in new tab
                const newWindow = window.open(blobUrl, '_blank');

                if (newWindow) {
                    this.showAlert('success', 'تم فتح PDF في تبويب جديد');

                    // Clean up blob URL after 5 minutes
                    setTimeout(() => {
                        URL.revokeObjectURL(blobUrl);
                        console.log('Blob URL cleaned up');
                    }, 300000);
                } else {
                    this.showAlert('error', 'لا يمكن فتح نافذة جديدة. تحقق من إعدادات المتصفح.');
                    URL.revokeObjectURL(blobUrl);
                }
            }
        } catch (error) {
            console.error('Error opening PDF with blob:', error);
            this.showAlert('error', 'خطأ في فتح PDF: ' + error.message);
        }
    }

    // Download PDF directly
    async downloadPdfDirect(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            console.log('Downloading PDF:', file.name);

            // Create blob from base64
            const blobUrl = this.createPdfBlob(file);

            if (blobUrl) {
                const link = document.createElement('a');
                link.href = blobUrl;
                link.download = file.originalName || file.name;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Clean up blob URL
                setTimeout(() => {
                    URL.revokeObjectURL(blobUrl);
                }, 1000);

                this.showAlert('success', 'تم بدء تحميل الملف');
            }
        } catch (error) {
            console.error('Error downloading PDF:', error);
            this.showAlert('error', 'خطأ في تحميل الملف: ' + error.message);
        }
    }

    // Create PDF Blob from base64
    createPdfBlob(file) {
        try {
            console.log('Creating blob for file:', file.name, 'Content length:', file.content?.length);

            if (!file.content) {
                throw new Error('لا يوجد محتوى للملف');
            }

            // Remove data URL prefix if present
            let base64Data = file.content;
            if (base64Data.startsWith('data:')) {
                base64Data = base64Data.split(',')[1];
            }

            // Convert base64 to binary
            const binaryString = atob(base64Data);
            const bytes = new Uint8Array(binaryString.length);

            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            // Create blob
            const blob = new Blob([bytes], { type: 'application/pdf' });
            const blobUrl = URL.createObjectURL(blob);

            console.log('Blob created successfully:', blobUrl);
            return blobUrl;

        } catch (error) {
            console.error('Error creating PDF blob:', error);
            this.showAlert('error', 'خطأ في معالجة ملف PDF: ' + error.message);
            return null;
        }
    }

    // Show PDF inline
    async showPdfInline(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            console.log('Showing PDF inline for file:', file.name);

            const container = document.getElementById(`pdf-inline-container-${fileId}`);
            const fallback = document.getElementById(`pdf-fallback-${fileId}`);

            if (!container) {
                this.showAlert('error', 'عنصر العرض غير موجود');
                return;
            }

            // Show container and hide fallback
            container.style.display = 'block';
            if (fallback) fallback.style.display = 'none';

            // Create blob
            const blobUrl = this.createPdfBlob(file);

            if (blobUrl) {
                container.innerHTML = `
                    <div class="pdf-inline-header mb-2">
                        <small class="text-muted">
                            <i class="fas fa-file-pdf me-1"></i>
                            ${file.name} (${this.formatFileSize(file.size)})
                        </small>
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="this.closest('#pdf-inline-container-${fileId}').style.display='none'; document.getElementById('pdf-fallback-${fileId}').style.display='block';">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                    <iframe src="${blobUrl}" width="100%" height="600px" style="border: 1px solid #ddd; border-radius: 8px;"
                            onload="console.log('PDF loaded inline successfully')"
                            onerror="console.error('PDF failed to load inline'); this.parentElement.innerHTML='<div class=\\'alert alert-danger\\'>فشل في تحميل PDF</div>'">
                    </iframe>
                `;

                this.showAlert('success', 'تم عرض PDF بنجاح');

                // Clean up blob URL after 10 minutes
                setTimeout(() => {
                    URL.revokeObjectURL(blobUrl);
                    console.log('Inline PDF blob URL cleaned up');
                }, 600000);
            }

        } catch (error) {
            console.error('Error showing PDF inline:', error);
            this.showAlert('error', 'خطأ في عرض PDF: ' + error.message);
        }
    }

    // Show PDF inline in viewer modal
    async showPdfInlineInViewer(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            console.log('Showing PDF inline in viewer for file:', file.name);

            const container = document.getElementById(`pdf-inline-container-${fileId}`);
            const fallback = document.getElementById(`pdf-fallback-${fileId}`);

            if (!container) {
                this.showAlert('error', 'عنصر العرض غير موجود');
                return;
            }

            // Show container and hide fallback
            container.style.display = 'block';
            if (fallback) fallback.style.display = 'none';

            // Create blob
            const blobUrl = this.createPdfBlob(file);

            if (blobUrl) {
                container.innerHTML = `
                    <div class="pdf-inline-header mb-2">
                        <small class="text-muted">
                            <i class="fas fa-file-pdf me-1"></i>
                            ${file.name} (${this.formatFileSize(file.size)})
                        </small>
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="this.closest('#pdf-inline-container-${fileId}').style.display='none'; document.getElementById('pdf-fallback-${fileId}').style.display='block';">
                            <i class="fas fa-times"></i> إغلاق العرض
                        </button>
                    </div>
                    <div class="pdf-viewer-container" style="height: 500px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
                        <iframe src="${blobUrl}" style="width: 100%; height: 100%; border: none;"
                            onload="console.log('PDF loaded inline in viewer successfully')"
                            onerror="console.error('PDF failed to load inline in viewer'); this.parentElement.innerHTML='<div class=\\'alert alert-danger text-center\\' style=\\'margin: 50px;\\'>فشل في تحميل PDF</div>'">
                        </iframe>
                    </div>
                `;

                this.showAlert('success', 'تم عرض PDF في النافذة بنجاح');

                // Clean up blob URL after 10 minutes
                setTimeout(() => {
                    URL.revokeObjectURL(blobUrl);
                    console.log('Inline PDF blob URL cleaned up');
                }, 600000);
            }

        } catch (error) {
            console.error('Error showing PDF inline in viewer:', error);
            this.showAlert('error', 'خطأ في عرض PDF: ' + error.message);
        }
    }

    // Try PDF viewer
    async tryPdfViewer(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            let contentUrl = file.content;
            if (!file.content.startsWith('data:') && file.content.length > 100) {
                contentUrl = `data:${file.type};base64,${file.content}`;
            }

            // Hide fallback and show iframe
            const fallback = document.getElementById(`pdf-fallback-${fileId}`);
            const iframeContainer = document.getElementById(`pdf-iframe-container-${fileId}`);
            const embedContainer = document.getElementById(`pdf-embed-container-${fileId}`);

            if (fallback) fallback.style.display = 'none';

            // Try iframe first
            if (iframeContainer) {
                iframeContainer.style.display = 'block';
                const iframe = document.getElementById(`pdf-iframe-${fileId}`);

                // Set timeout to try embed if iframe fails
                setTimeout(() => {
                    if (iframe && iframe.contentDocument === null) {
                        console.log('Iframe failed, trying embed');
                        iframeContainer.style.display = 'none';
                        if (embedContainer) {
                            embedContainer.style.display = 'block';
                        }
                    }
                }, 3000);
            }

            this.showAlert('info', 'جاري محاولة عرض PDF...');

        } catch (error) {
            console.error('Error trying PDF viewer:', error);
            this.showAlert('error', 'خطأ في محاولة عرض PDF');
        }
    }

    // Open image with Blob URL in new tab
    async openImageWithBlob(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            console.log('Opening image with blob for file:', file.name);

            // Create blob from base64
            const blobUrl = this.createImageBlob(file);

            if (blobUrl) {
                // Open in new tab
                const newWindow = window.open('', '_blank');
                if (newWindow) {
                    newWindow.document.write(`
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>${file.name}</title>
                            <style>
                                body {
                                    margin: 0;
                                    padding: 20px;
                                    background: #f8f9fa;
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                    min-height: 100vh;
                                    font-family: Arial, sans-serif;
                                }
                                .image-container {
                                    text-align: center;
                                    background: white;
                                    padding: 20px;
                                    border-radius: 8px;
                                    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                                    max-width: 90vw;
                                    max-height: 90vh;
                                }
                                img {
                                    max-width: 100%;
                                    max-height: 80vh;
                                    object-fit: contain;
                                    border-radius: 4px;
                                }
                                .image-info {
                                    margin-top: 15px;
                                    color: #6c757d;
                                    font-size: 14px;
                                }
                            </style>
                        </head>
                        <body>
                            <div class="image-container">
                                <img src="${blobUrl}" alt="${file.name}">
                                <div class="image-info">
                                    <strong>${file.name}</strong><br>
                                    ${this.formatFileSize(file.size)}
                                </div>
                            </div>
                        </body>
                        </html>
                    `);
                    newWindow.document.close();

                    this.showAlert('success', 'تم فتح الصورة في تبويب جديد');

                    // Clean up blob URL after 5 minutes
                    setTimeout(() => {
                        URL.revokeObjectURL(blobUrl);
                        console.log('Image blob URL cleaned up');
                    }, 300000);
                } else {
                    this.showAlert('error', 'لا يمكن فتح نافذة جديدة. تحقق من إعدادات المتصفح.');
                    URL.revokeObjectURL(blobUrl);
                }
            }
        } catch (error) {
            console.error('Error opening image with blob:', error);
            this.showAlert('error', 'خطأ في فتح الصورة: ' + error.message);
        }
    }

    // Download image directly
    async downloadImageDirect(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            console.log('Downloading image:', file.name);

            // Create blob from base64
            const blobUrl = this.createImageBlob(file);

            if (blobUrl) {
                const link = document.createElement('a');
                link.href = blobUrl;
                link.download = file.originalName || file.name;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Clean up blob URL
                setTimeout(() => {
                    URL.revokeObjectURL(blobUrl);
                }, 1000);

                this.showAlert('success', 'تم بدء تحميل الصورة');
            }
        } catch (error) {
            console.error('Error downloading image:', error);
            this.showAlert('error', 'خطأ في تحميل الصورة: ' + error.message);
        }
    }

    // Show image inline
    async showImageInline(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            console.log('Showing image inline for file:', file.name);

            const container = document.getElementById(`image-inline-container-${fileId}`);
            const fallback = document.getElementById(`image-fallback-${fileId}`);

            if (!container) {
                this.showAlert('error', 'عنصر العرض غير موجود');
                return;
            }

            // Show container and hide fallback
            container.style.display = 'block';
            if (fallback) fallback.style.display = 'none';

            // Create blob
            const blobUrl = this.createImageBlob(file);

            if (blobUrl) {
                container.innerHTML = `
                    <div class="image-inline-header mb-2">
                        <small class="text-muted">
                            <i class="fas fa-image me-1"></i>
                            ${file.name} (${this.formatFileSize(file.size)})
                        </small>
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="this.closest('#image-inline-container-${fileId}').style.display='none'; document.getElementById('image-fallback-${fileId}').style.display='block';">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                    <div class="text-center">
                        <img src="${blobUrl}" alt="${file.name}" style="max-width: 100%; max-height: 600px; object-fit: contain; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);"
                             onload="console.log('Image loaded inline successfully')"
                             onerror="console.error('Image failed to load inline'); this.parentElement.innerHTML='<div class=\\'alert alert-danger\\'>فشل في تحميل الصورة</div>'">
                    </div>
                `;

                this.showAlert('success', 'تم عرض الصورة بنجاح');

                // Clean up blob URL after 10 minutes
                setTimeout(() => {
                    URL.revokeObjectURL(blobUrl);
                    console.log('Inline image blob URL cleaned up');
                }, 600000);
            }

        } catch (error) {
            console.error('Error showing image inline:', error);
            this.showAlert('error', 'خطأ في عرض الصورة: ' + error.message);
        }
    }

    // Show image inline in viewer modal
    async showImageInlineInViewer(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            console.log('Showing image inline in viewer for file:', file.name);

            const container = document.getElementById(`image-inline-container-${fileId}`);
            const fallback = document.getElementById(`image-fallback-${fileId}`);

            if (!container) {
                this.showAlert('error', 'عنصر العرض غير موجود');
                return;
            }

            // Show container and hide fallback
            container.style.display = 'block';
            if (fallback) fallback.style.display = 'none';

            // Create blob
            const blobUrl = this.createImageBlob(file);

            if (blobUrl) {
                container.innerHTML = `
                    <div class="image-inline-header mb-2">
                        <small class="text-muted">
                            <i class="fas fa-image me-1"></i>
                            ${file.name} (${this.formatFileSize(file.size)})
                        </small>
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="this.closest('#image-inline-container-${fileId}').style.display='none'; document.getElementById('image-fallback-${fileId}').style.display='block';">
                            <i class="fas fa-times"></i> إغلاق العرض
                        </button>
                    </div>
                    <div class="text-center image-viewer-container" style="max-height: 500px; overflow: auto; border: 1px solid #ddd; border-radius: 8px; padding: 20px; background: #f8f9fa;">
                        <img src="${blobUrl}" alt="${file.name}" style="max-width: 100%; max-height: 450px; object-fit: contain; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); background: white; padding: 10px;"
                             onload="console.log('Image loaded inline in viewer successfully')"
                             onerror="console.error('Image failed to load inline in viewer'); this.parentElement.innerHTML='<div class=\\'alert alert-danger\\'>فشل في تحميل الصورة</div>'">
                    </div>
                `;

                this.showAlert('success', 'تم عرض الصورة في النافذة بنجاح');

                // Clean up blob URL after 10 minutes
                setTimeout(() => {
                    URL.revokeObjectURL(blobUrl);
                    console.log('Inline image blob URL cleaned up');
                }, 600000);
            }

        } catch (error) {
            console.error('Error showing image inline in viewer:', error);
            this.showAlert('error', 'خطأ في عرض الصورة: ' + error.message);
        }
    }

    // Create image Blob from base64
    createImageBlob(file) {
        try {
            console.log('Creating image blob for file:', file.name, 'Content length:', file.content?.length);

            if (!file.content) {
                throw new Error('لا يوجد محتوى للملف');
            }

            // Remove data URL prefix if present
            let base64Data = file.content;
            if (base64Data.startsWith('data:')) {
                base64Data = base64Data.split(',')[1];
            }

            // Convert base64 to binary
            const binaryString = atob(base64Data);
            const bytes = new Uint8Array(binaryString.length);

            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            // Create blob with correct MIME type
            const blob = new Blob([bytes], { type: file.type });
            const blobUrl = URL.createObjectURL(blob);

            console.log('Image blob created successfully:', blobUrl);
            return blobUrl;

        } catch (error) {
            console.error('Error creating image blob:', error);
            this.showAlert('error', 'خطأ في معالجة ملف الصورة: ' + error.message);
            return null;
        }
    }

    // Use PDF.js viewer
    async usePdfJs(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            let contentUrl = file.content;
            if (!file.content.startsWith('data:') && file.content.length > 100) {
                contentUrl = `data:${file.type};base64,${file.content}`;
            }

            // Create PDF.js viewer URL
            const pdfJsUrl = `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(contentUrl)}`;

            // Open in new tab with PDF.js
            const newWindow = window.open(pdfJsUrl, '_blank');

            if (newWindow) {
                this.showAlert('success', 'تم فتح PDF باستخدام العارض المتقدم');
            } else {
                // Fallback: create our own PDF.js viewer
                this.createCustomPdfViewer(contentUrl, file.name);
            }

        } catch (error) {
            console.error('Error using PDF.js:', error);
            this.showAlert('error', 'خطأ في استخدام العارض المتقدم');
        }
    }

    // Create custom PDF viewer
    createCustomPdfViewer(contentUrl, fileName) {
        const viewerHTML = `
            <div class="pdf-js-viewer-modal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.9); z-index: 10000;">
                <div class="pdf-viewer-header" style="background: #333; color: white; padding: 10px; display: flex; justify-content: space-between; align-items: center;">
                    <h5 style="margin: 0;"><i class="fas fa-file-pdf me-2"></i>${fileName}</h5>
                    <div>
                        <button class="btn btn-sm btn-light me-2" onclick="this.closest('.pdf-js-viewer-modal').remove()">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                </div>
                <div class="pdf-viewer-content" style="height: calc(100% - 60px); padding: 10px;">
                    <iframe src="https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(contentUrl)}"
                            width="100%" height="100%" style="border: none; background: white;">
                    </iframe>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', viewerHTML);
    }

    // Use Google Docs Viewer
    async useGoogleViewer(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            // Note: Google Docs Viewer requires a public URL, not base64 data
            // This is a limitation, so we'll show an informative message
            this.showAlert('info', `
                <strong>Google Docs Viewer</strong><br>
                يتطلب Google Docs Viewer رابط عام للملف.<br>
                للاستفادة من هذه الميزة، يجب رفع الملف إلى خادم ويب.<br>
                <small class="text-muted">استخدم "العارض المتقدم" أو "فتح في تبويب جديد" كبديل.</small>
            `);

            // Alternative: Create a temporary blob URL (works in some cases)
            this.createBlobViewer(file);

        } catch (error) {
            console.error('Error using Google Viewer:', error);
            this.showAlert('error', 'خطأ في استخدام Google Viewer');
        }
    }

    // Create blob viewer
    createBlobViewer(file) {
        try {
            // Convert base64 to blob
            const base64Data = file.content.replace(/^data:[^;]+;base64,/, '');
            const binaryString = atob(base64Data);
            const bytes = new Uint8Array(binaryString.length);

            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            const blob = new Blob([bytes], { type: file.type });
            const blobUrl = URL.createObjectURL(blob);

            // Open blob URL in new tab
            const newWindow = window.open(blobUrl, '_blank');

            if (newWindow) {
                this.showAlert('success', 'تم فتح الملف باستخدام Blob URL');

                // Clean up blob URL after some time
                setTimeout(() => {
                    URL.revokeObjectURL(blobUrl);
                }, 60000); // 1 minute
            } else {
                this.showAlert('error', 'لا يمكن فتح نافذة جديدة');
            }

        } catch (error) {
            console.error('Error creating blob viewer:', error);
            this.showAlert('error', 'خطأ في إنشاء عارض Blob');
        }
    }

    // Show alert message
    showAlert(type, message) {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// Initialize file manager
let fileManager;
document.addEventListener('DOMContentLoaded', async () => {
    try {
        console.log('Initializing FileManager...');
        console.log('Database available:', !!window.localDB);

        // Wait a bit for database to be ready
        await new Promise(resolve => setTimeout(resolve, 100));

        fileManager = new FileManager();
        window.fileManager = fileManager;
        console.log('File manager initialized successfully');
    } catch (error) {
        console.error('Failed to initialize file manager:', error);

        // Show error to user
        setTimeout(() => {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-danger position-fixed';
            errorDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            errorDiv.innerHTML = `
                <strong>خطأ في تهيئة النظام:</strong><br>
                ${error.message}<br>
                <button class="btn btn-sm btn-light mt-2" onclick="location.reload()">إعادة تحميل الصفحة</button>
            `;
            document.body.appendChild(errorDiv);
        }, 1000);
    }
});

// Export for use in other modules
window.FileManager = FileManager;
