// ===== File Management System =====

class FileManager {
    constructor() {
        this.currentFiles = [];
        this.currentFilters = {};
        this.selectedFiles = new Set();
        this.uploadQueue = [];
        this.initializeFileManager();
    }

    // Initialize file manager
    async initializeFileManager() {
        console.log('Initializing FileManager components...');

        this.setupFileUpload();
        this.setupFileFilters();
        this.setupFileActions();

        // Wait for database to be ready
        const dbReady = await this.waitForDatabase();
        if (dbReady) {
            console.log('Database ready, loading files...');
            await this.loadFiles();
        } else {
            console.error('Database not ready, showing error');
            this.showAlert('error', 'فشل في تهيئة قاعدة البيانات. يرجى إعادة تحميل الصفحة.');
        }
    }

    // Wait for database to be ready
    async waitForDatabase() {
        let attempts = 0;
        const maxAttempts = 30; // 3 seconds max wait

        // Wait for localDB to be available
        while (!window.localDB && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }

        if (!window.localDB) {
            console.error('Database not available after waiting');
            return false;
        }

        console.log('Database is available');
        return true;
    }

    // Setup file upload functionality
    setupFileUpload() {
        // Create file upload area
        this.createUploadArea();
        
        // Handle file input change
        document.addEventListener('change', (e) => {
            if (e.target.type === 'file') {
                this.handleFileSelect(e.target.files);
            }
        });

        // Handle drag and drop
        document.addEventListener('dragover', (e) => {
            e.preventDefault();
            const dropZone = e.target.closest('.file-drop-zone');
            if (dropZone) {
                dropZone.classList.add('drag-over');
            }
        });

        document.addEventListener('dragleave', (e) => {
            const dropZone = e.target.closest('.file-drop-zone');
            if (dropZone && !dropZone.contains(e.relatedTarget)) {
                dropZone.classList.remove('drag-over');
            }
        });

        document.addEventListener('drop', (e) => {
            e.preventDefault();
            const dropZone = e.target.closest('.file-drop-zone');
            if (dropZone) {
                dropZone.classList.remove('drag-over');
                this.handleFileSelect(e.dataTransfer.files);
            }
        });
    }

    // Create upload area HTML
    createUploadArea() {
        const uploadHTML = `
            <div class="file-upload-section">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cloud-upload-alt me-2"></i>رفع ملف جديد</h5>
                    </div>
                    <div class="card-body">
                        <div class="file-drop-zone" onclick="document.getElementById('fileInput').click()">
                            <div class="drop-zone-content">
                                <i class="fas fa-cloud-upload-alt drop-icon"></i>
                                <h4>اسحب وأفلت الملفات هنا</h4>
                                <p>أو انقر لاختيار الملفات</p>
                                <small class="text-muted">الصيغ المدعومة: PDF, DOC, DOCX, JPG, PNG (الحد الأقصى: 10MB)</small>
                            </div>
                            <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" style="display: none;">
                        </div>
                        
                        <div class="upload-queue mt-3" id="uploadQueue" style="display: none;">
                            <h6>قائمة الانتظار:</h6>
                            <div id="uploadList"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Add to add-file section
        const addFileSection = document.getElementById('add-file-section');
        if (addFileSection) {
            addFileSection.innerHTML = uploadHTML;
        }
    }

    // Handle file selection
    async handleFileSelect(files) {
        const fileArray = Array.from(files);
        
        for (const file of fileArray) {
            if (this.validateFile(file)) {
                await this.processFile(file);
            }
        }
        
        this.updateUploadQueue();
    }

    // Validate file
    validateFile(file) {
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/jpg',
            'image/png'
        ];

        if (file.size > maxSize) {
            this.showAlert('error', `الملف "${file.name}" كبير جداً. الحد الأقصى 10MB`);
            return false;
        }

        if (!allowedTypes.includes(file.type)) {
            this.showAlert('error', `نوع الملف "${file.name}" غير مدعوم`);
            return false;
        }

        return true;
    }

    // Process file for upload
    async processFile(file) {
        const fileData = {
            id: this.generateId(),
            file: file,
            name: file.name,
            originalName: file.name,
            type: file.type,
            size: file.size,
            status: 'pending',
            progress: 0,
            category: this.detectFileCategory(file.name),
            issueDate: new Date().toISOString().split('T')[0],
            description: '',
            tags: []
        };

        this.uploadQueue.push(fileData);
        this.showUploadForm(fileData);
    }

    // Detect file category based on name
    detectFileCategory(fileName) {
        const name = fileName.toLowerCase();
        
        if (name.includes('مخالف') || name.includes('يومية')) {
            return 'يومية مخالفات';
        } else if (name.includes('افادة') || name.includes('إفادة')) {
            return 'إفادة';
        } else if (name.includes('اجازة') || name.includes('إجازة')) {
            return 'مستند إجازة';
        } else if (name.includes('رخصة') || name.includes('ادارية')) {
            return 'رخصة إدارية';
        }
        
        return 'يومية مخالفات'; // Default
    }

    // Show upload form
    showUploadForm(fileData) {
        const formHTML = `
            <div class="upload-form-modal" id="uploadForm_${fileData.id}">
                <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h5>تفاصيل الملف: ${fileData.name}</h5>
                        <button class="btn-close" onclick="this.closest('.upload-form-modal').remove()"></button>
                    </div>
                    <div class="modal-body">
                        <form id="fileDetailsForm_${fileData.id}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" data-translate="file_name">اسم الملف</label>
                                    <input type="text" class="form-control" name="name" value="${fileData.name}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" data-translate="category">التصنيف</label>
                                    <select class="form-control" name="category" required>
                                        <option value="يومية مخالفات" ${fileData.category === 'يومية مخالفات' ? 'selected' : ''} data-translate="violation_diary">يومية مخالفات</option>
                                        <option value="إفادة" ${fileData.category === 'إفادة' ? 'selected' : ''} data-translate="certificate">إفادة</option>
                                        <option value="مستند إجازة" ${fileData.category === 'مستند إجازة' ? 'selected' : ''} data-translate="leave_document">مستند إجازة</option>
                                        <option value="رخصة إدارية" ${fileData.category === 'رخصة إدارية' ? 'selected' : ''} data-translate="admin_license">رخصة إدارية</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" data-translate="course_code">رمز الدورة</label>
                                    <input type="text" class="form-control" name="courseCode" placeholder="" data-translate-placeholder="course_code_placeholder" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" data-translate="course_number">رقم الدورة</label>
                                    <input type="number" class="form-control" name="courseNumber" placeholder="" data-translate-placeholder="course_number_placeholder" required min="1">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" data-translate="issue_date">تاريخ الإصدار</label>
                                    <input type="date" class="form-control" name="issueDate" value="${fileData.issueDate}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label" data-translate="keywords">الكلمات المفتاحية</label>
                                    <input type="text" class="form-control" name="tags" placeholder="" data-translate-placeholder="keywords_placeholder">
                                </div>
                                <div class="col-12 mb-3">
                                    <label class="form-label" data-translate="description">الوصف</label>
                                    <textarea class="form-control" name="description" rows="3" placeholder="" data-translate-placeholder="description_placeholder"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.upload-form-modal').remove()">إلغاء</button>
                        <button class="btn btn-primary" onclick="fileManager.uploadFile('${fileData.id}')">رفع الملف</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', formHTML);
    }

    // Upload file to database
    async uploadFile(fileId) {
        const fileData = this.uploadQueue.find(f => f.id === fileId);
        if (!fileData) return;

        const form = document.getElementById(`fileDetailsForm_${fileId}`);
        const formData = new FormData(form);
        
        try {
            // Show loading
            this.updateFileStatus(fileId, 'uploading', 0);
            
            // Convert file to base64
            const base64Content = await this.fileToBase64(fileData.file);
            
            // Prepare file data for database
            const dbFileData = {
                name: formData.get('name'),
                originalName: fileData.originalName,
                category: formData.get('category'),
                type: fileData.type,
                size: fileData.size,
                content: base64Content,
                description: formData.get('description'),
                tags: formData.get('tags').split(',').map(tag => tag.trim()).filter(tag => tag),
                issueDate: formData.get('issueDate'),
                courseCode: formData.get('courseCode'),
                courseNumber: parseInt(formData.get('courseNumber')),
                createdBy: window.authSystem?.currentUser?.id || 'unknown'
            };

            // Simulate upload progress
            for (let progress = 0; progress <= 100; progress += 20) {
                this.updateFileStatus(fileId, 'uploading', progress);
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // Save to database
            const savedFile = await window.localDB.addFile(dbFileData);
            
            // Update status
            this.updateFileStatus(fileId, 'completed', 100);
            
            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('file_upload', `تم رفع الملف: ${savedFile.name}`);
            }
            
            // Remove from queue after delay
            setTimeout(() => {
                this.removeFromQueue(fileId);
                document.getElementById(`uploadForm_${fileId}`)?.remove();
            }, 2000);
            
            // Refresh file list
            this.loadFiles();
            
            this.showAlert('success', 'تم رفع الملف بنجاح');
            
        } catch (error) {
            console.error('Upload error:', error);
            this.updateFileStatus(fileId, 'error', 0);
            this.showAlert('error', 'حدث خطأ أثناء رفع الملف');
        }
    }

    // Convert file to base64
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }

    // Update file status in queue
    updateFileStatus(fileId, status, progress) {
        const fileData = this.uploadQueue.find(f => f.id === fileId);
        if (fileData) {
            fileData.status = status;
            fileData.progress = progress;
            this.updateUploadQueue();
        }
    }

    // Remove file from queue
    removeFromQueue(fileId) {
        this.uploadQueue = this.uploadQueue.filter(f => f.id !== fileId);
        this.updateUploadQueue();
    }

    // Update upload queue display
    updateUploadQueue() {
        const queueContainer = document.getElementById('uploadQueue');
        const listContainer = document.getElementById('uploadList');
        
        if (!queueContainer || !listContainer) return;
        
        if (this.uploadQueue.length === 0) {
            queueContainer.style.display = 'none';
            return;
        }
        
        queueContainer.style.display = 'block';
        listContainer.innerHTML = this.uploadQueue.map(file => `
            <div class="upload-item">
                <div class="upload-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-size">${this.formatFileSize(file.size)}</div>
                </div>
                <div class="upload-progress">
                    <div class="progress">
                        <div class="progress-bar ${this.getProgressBarClass(file.status)}" 
                             style="width: ${file.progress}%"></div>
                    </div>
                    <small class="status-text">${this.getStatusText(file.status)}</small>
                </div>
            </div>
        `).join('');
    }

    // Get progress bar class based on status
    getProgressBarClass(status) {
        switch (status) {
            case 'completed': return 'bg-success';
            case 'error': return 'bg-danger';
            case 'uploading': return 'bg-primary';
            default: return 'bg-secondary';
        }
    }

    // Get status text
    getStatusText(status) {
        switch (status) {
            case 'pending': return 'في الانتظار';
            case 'uploading': return 'جاري الرفع...';
            case 'completed': return 'تم بنجاح';
            case 'error': return 'خطأ';
            default: return '';
        }
    }

    // Format file size
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Format date
    formatDate(dateString) {
        if (!dateString) return 'غير محدد';

        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return 'تاريخ غير صحيح';

            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } catch (error) {
            console.error('Error formatting date:', error);
            return 'تاريخ غير صحيح';
        }
    }

    // Render files based on current view
    renderFiles() {
        if (this.currentFiles.length === 0) {
            return `
                <div class="empty-state">
                    <i class="fas fa-folder-open"></i>
                    <h4>لا توجد ملفات</h4>
                    <p>لم يتم العثور على أي ملفات تطابق معايير البحث</p>
                    <button class="btn btn-primary" onclick="showSection('add-file')">
                        <i class="fas fa-plus"></i> إضافة ملف جديد
                    </button>
                </div>
            `;
        }

        return `
            <div class="files-grid" id="filesGrid">
                ${this.currentFiles.map(file => this.renderFileCard(file)).join('')}
            </div>
        `;
    }

    // Render individual file card
    renderFileCard(file) {
        // Ensure file object has required properties
        const safeFile = {
            id: file.id || 'unknown',
            name: file.name || 'ملف بدون اسم',
            type: file.type || 'application/octet-stream',
            size: file.size || 0,
            category: file.category || 'غير مصنف',
            description: file.description || '',
            tags: file.tags || [],
            createdAt: file.createdAt || new Date().toISOString(),
            courseCode: file.courseCode || '',
            courseNumber: file.courseNumber || ''
        };

        const fileIcon = this.getFileIcon(safeFile.type);
        const fileSize = this.formatFileSize(safeFile.size);
        const createdDate = this.formatDate(safeFile.createdAt);

        return `
            <div class="file-card" onclick="fileManager.viewFile('${safeFile.id}')">
                <div class="file-card-header">
                    <div class="file-type-icon">
                        <i class="${fileIcon}"></i>
                    </div>
                    <div class="file-card-info">
                        <h6>${safeFile.name}</h6>
                        <div class="file-card-meta">
                            ${fileSize} • ${createdDate}
                        </div>
                    </div>
                </div>
                <div class="file-card-body">
                    <div class="file-category">${safeFile.category}</div>
                    ${safeFile.description ? `<div class="file-description">${safeFile.description}</div>` : ''}
                    ${safeFile.tags.length > 0 ? `
                        <div class="file-tags mt-2">
                            ${safeFile.tags.map(tag => `<span class="badge bg-secondary me-1">${tag}</span>`).join('')}
                        </div>
                    ` : ''}
                </div>
                <div class="file-card-footer">
                    <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); fileManager.viewFile('${safeFile.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="event.stopPropagation(); fileManager.downloadFile('${safeFile.id}')">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="event.stopPropagation(); fileManager.editFile('${safeFile.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    ${window.authSystem?.hasPermission('delete') ? `
                        <button class="btn btn-sm btn-outline-danger" onclick="event.stopPropagation(); fileManager.deleteFile('${safeFile.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // Get file icon based on type
    getFileIcon(fileType) {
        if (fileType.includes('pdf')) {
            return 'fas fa-file-pdf';
        } else if (fileType.includes('word') || fileType.includes('document')) {
            return 'fas fa-file-word';
        } else if (fileType.includes('image')) {
            return 'fas fa-file-image';
        } else {
            return 'fas fa-file';
        }
    }

    // Setup file filters
    setupFileFilters() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', () => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.applyFilters();
                }, 300);
            });
        }
    }

    // Apply filters
    applyFilters() {
        const searchTerm = document.getElementById('searchInput')?.value || '';
        const category = document.getElementById('categoryFilter')?.value || '';
        const dateFrom = document.getElementById('dateFromFilter')?.value || '';
        const dateTo = document.getElementById('dateToFilter')?.value || '';

        this.currentFilters = {
            search: searchTerm,
            category: category,
            dateFrom: dateFrom,
            dateTo: dateTo
        };

        this.loadFiles();
    }

    // Clear filters
    clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('categoryFilter').value = '';
        document.getElementById('dateFromFilter').value = '';
        document.getElementById('dateToFilter').value = '';

        this.currentFilters = {};
        this.loadFiles();
    }

    // Toggle view between grid and list
    toggleView(viewType) {
        // Update button states
        document.getElementById('gridViewBtn').classList.toggle('active', viewType === 'grid');
        document.getElementById('listViewBtn').classList.toggle('active', viewType === 'list');

        // Update view (for now, only grid is implemented)
        this.currentView = viewType;
    }

    // View file in modal
    async viewFile(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            this.showFileViewer(file);

            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('file_view', `عرض الملف: ${file.name}`);
            }
        } catch (error) {
            console.error('Error viewing file:', error);
            this.showAlert('error', 'حدث خطأ أثناء عرض الملف');
        }
    }

    // Show file viewer modal
    showFileViewer(file) {
        // Ensure file object has required properties
        const safeFile = {
            id: file.id || 'unknown',
            name: file.name || 'ملف بدون اسم',
            type: file.type || 'application/octet-stream',
            size: file.size || 0,
            category: file.category || 'غير مصنف',
            description: file.description || '',
            tags: file.tags || [],
            createdAt: file.createdAt || new Date().toISOString(),
            issueDate: file.issueDate || 'غير محدد',
            createdBy: file.createdBy || 'غير محدد',
            courseCode: file.courseCode || '',
            courseNumber: file.courseNumber || ''
        };
        const viewerHTML = `
            <div class="file-viewer-modal" id="fileViewer_${safeFile.id}">
                <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
                <div class="modal-content file-viewer-content">
                    <div class="modal-header">
                        <h5><i class="${this.getFileIcon(safeFile.type)} me-2"></i>${safeFile.name}</h5>
                        <div class="viewer-controls">
                            <button class="btn btn-sm btn-outline-secondary" onclick="fileManager.downloadFile('${safeFile.id}')">
                                <i class="fas fa-download"></i> تحميل
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="fileManager.printFile('${safeFile.id}')">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                            <button class="btn-close" onclick="this.closest('.file-viewer-modal').remove()">×</button>
                        </div>
                    </div>
                    <div class="modal-body p-0">
                        <div class="file-viewer-container">
                            ${this.renderFileContent(safeFile)}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <div class="file-info">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong data-translate="category">التصنيف:</strong> ${safeFile.category}<br>
                                    <strong data-translate="file_size">الحجم:</strong> ${this.formatFileSize(safeFile.size)}<br>
                                    <strong data-translate="created_date">تاريخ الإنشاء:</strong> ${this.formatDate(safeFile.createdAt)}<br>
                                    ${safeFile.courseCode ? `<strong data-translate="course_code">رمز الدورة:</strong> ${safeFile.courseCode}<br>` : ''}
                                </div>
                                <div class="col-md-6">
                                    <strong data-translate="issue_date">تاريخ الإصدار:</strong> ${safeFile.issueDate}<br>
                                    <strong data-translate="created_by">المنشئ:</strong> ${safeFile.createdBy}<br>
                                    ${safeFile.courseNumber ? `<strong data-translate="course_number">رقم الدورة:</strong> ${safeFile.courseNumber}<br>` : ''}
                                    ${safeFile.tags.length > 0 ? `<strong data-translate="keywords">الكلمات المفتاحية:</strong> ${safeFile.tags.join(', ')}` : ''}
                                </div>
                            </div>
                            ${safeFile.description ? `<div class="mt-2"><strong>الوصف:</strong> ${safeFile.description}</div>` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', viewerHTML);
    }

    // Render file content based on type
    renderFileContent(file) {
        if (file.type.includes('pdf')) {
            return `
                <div class="pdf-viewer">
                    <iframe src="${file.content}" width="100%" height="600px" style="border: none;">
                        <p>متصفحك لا يدعم عرض PDF. <a href="${file.content}" target="_blank">انقر هنا لفتح الملف</a></p>
                    </iframe>
                </div>
            `;
        } else if (file.type.includes('image')) {
            return `
                <div class="image-viewer text-center">
                    <img src="${file.content}" alt="${file.name}" style="max-width: 100%; max-height: 600px; object-fit: contain;">
                </div>
            `;
        } else {
            return `
                <div class="file-preview text-center p-4">
                    <i class="${this.getFileIcon(file.type)}" style="font-size: 4rem; color: var(--primary-color);"></i>
                    <h4 class="mt-3">${file.name}</h4>
                    <p class="text-muted">معاينة غير متاحة لهذا النوع من الملفات</p>
                    <button class="btn btn-primary" onclick="fileManager.downloadFile('${file.id}')">
                        <i class="fas fa-download me-2"></i>تحميل الملف
                    </button>
                </div>
            `;
        }
    }

    // Download file
    async downloadFile(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            // Create download link
            const link = document.createElement('a');
            link.href = file.content;
            link.download = file.originalName || file.name;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('file_download', `تحميل الملف: ${file.name}`);
            }

            this.showAlert('success', 'تم تحميل الملف بنجاح');
        } catch (error) {
            console.error('Error downloading file:', error);
            this.showAlert('error', 'حدث خطأ أثناء تحميل الملف');
        }
    }

    // Print file
    async printFile(fileId) {
        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            // Open print window
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>طباعة - ${file.name}</title>
                        <style>
                            body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
                            .header { text-align: center; margin-bottom: 20px; border-bottom: 2px solid #333; padding-bottom: 10px; }
                            .content { text-align: center; }
                            img { max-width: 100%; height: auto; }
                            iframe { width: 100%; height: 80vh; border: none; }
                        </style>
                    </head>
                    <body>
                        <div class="header">
                            <h2>${file.name}</h2>
                            <p>التصنيف: ${file.category} | تاريخ الإصدار: ${file.issueDate}</p>
                        </div>
                        <div class="content">
                            ${file.type.includes('image') ?
                                `<img src="${file.content}" alt="${file.name}">` :
                                `<iframe src="${file.content}"></iframe>`
                            }
                        </div>
                    </body>
                </html>
            `);
            printWindow.document.close();

            setTimeout(() => {
                printWindow.print();
            }, 1000);

            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('file_print', `طباعة الملف: ${file.name}`);
            }
        } catch (error) {
            console.error('Error printing file:', error);
            this.showAlert('error', 'حدث خطأ أثناء طباعة الملف');
        }
    }

    // Edit file (placeholder)
    editFile(fileId) {
        this.showAlert('info', 'ميزة تعديل الملفات قيد التطوير');
    }

    // Delete file
    async deleteFile(fileId) {
        if (!confirm('هل أنت متأكد من حذف هذا الملف؟ لا يمكن التراجع عن هذا الإجراء.')) {
            return;
        }

        try {
            const file = await window.localDB.getFileById(fileId);
            if (!file) {
                this.showAlert('error', 'الملف غير موجود');
                return;
            }

            await window.localDB.deleteFile(fileId);

            // Log activity
            if (window.authSystem) {
                window.authSystem.logActivity('file_delete', `حذف الملف: ${file.name}`);
            }

            this.showAlert('success', 'تم حذف الملف بنجاح');
            this.loadFiles(); // Refresh file list
        } catch (error) {
            console.error('Error deleting file:', error);
            this.showAlert('error', 'حدث خطأ أثناء حذف الملف');
        }
    }

    // Setup file actions
    setupFileActions() {
        // File actions are handled through onclick events in the HTML
    }

    // Load files from database
    async loadFiles() {
        try {
            // Check if database is available
            if (!window.localDB) {
                console.warn('Database not available, waiting...');
                const dbReady = await this.waitForDatabase();
                if (!dbReady) {
                    throw new Error('Database initialization failed');
                }
            }

            console.log('Loading files with filters:', this.currentFilters);
            this.currentFiles = await window.localDB.getFiles(this.currentFilters);
            console.log('Files loaded successfully:', this.currentFiles.length);

            this.displayFiles();
        } catch (error) {
            console.error('Error loading files:', error);

            // Try to initialize with empty array if database fails
            this.currentFiles = [];
            this.displayFiles();

            // Show error with reload and reset options
            this.showAlert('error', `
                حدث خطأ أثناء تحميل الملفات: ${error.message}
                <div class="mt-2">
                    <button class="btn btn-sm btn-light me-2" onclick="location.reload()">إعادة تحميل</button>
                    <button class="btn btn-sm btn-warning" onclick="fileManager.resetDatabase()">إعادة تهيئة البيانات</button>
                    <button class="btn btn-sm btn-info" onclick="fileManager.debugDatabase()">فحص قاعدة البيانات</button>
                </div>
            `);
        }
    }

    // Display files
    displayFiles() {
        try {
            const filesSection = document.getElementById('files-section');
            if (!filesSection) {
                console.warn('files-section element not found');
                return;
            }

            // Ensure currentFiles is an array
            if (!Array.isArray(this.currentFiles)) {
                console.warn('currentFiles is not an array, initializing as empty array');
                this.currentFiles = [];
            }

        const filesHTML = `
            <div class="files-management">
                <div class="search-filters">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label>البحث</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="ابحث في الملفات...">
                        </div>
                        <div class="filter-group">
                            <label>التصنيف</label>
                            <select class="form-control" id="categoryFilter">
                                <option value="">جميع التصنيفات</option>
                                <option value="يومية مخالفات">يومية مخالفات</option>
                                <option value="إفادة">إفادة</option>
                                <option value="مستند إجازة">مستند إجازة</option>
                                <option value="رخصة إدارية">رخصة إدارية</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>من تاريخ</label>
                            <input type="date" class="form-control" id="dateFromFilter">
                        </div>
                        <div class="filter-group">
                            <label>إلى تاريخ</label>
                            <input type="date" class="form-control" id="dateToFilter">
                        </div>
                        <div class="filter-actions">
                            <button class="btn btn-primary" onclick="fileManager.applyFilters()">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <button class="btn btn-secondary" onclick="fileManager.clearFilters()">
                                <i class="fas fa-times"></i> مسح
                            </button>
                        </div>
                    </div>
                </div>

                <div class="files-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>الملفات (${this.currentFiles.length})</h5>
                        <div class="view-controls">
                            <button class="btn btn-outline-secondary btn-sm" onclick="fileManager.toggleView('grid')" id="gridViewBtn">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="fileManager.toggleView('list')" id="listViewBtn">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="files-container" id="filesContainer">
                    ${this.renderFiles()}
                </div>
            </div>
        `;

            filesSection.innerHTML = filesHTML;
            this.setupFileFilters();
        } catch (error) {
            console.error('Error displaying files:', error);

            // Show basic error message
            const filesSection = document.getElementById('files-section');
            if (filesSection) {
                filesSection.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>خطأ في عرض الملفات</h5>
                        <p>حدث خطأ أثناء عرض الملفات: ${error.message}</p>
                        <button class="btn btn-primary" onclick="fileManager.loadFiles()">إعادة المحاولة</button>
                    </div>
                `;
            }
        }
    }

    // Generate unique ID
    generateId() {
        return 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Reset database
    async resetDatabase() {
        try {
            if (confirm('هل أنت متأكد من إعادة تهيئة قاعدة البيانات؟ سيتم حذف جميع البيانات الحالية.')) {
                // Clear localStorage completely
                localStorage.removeItem('archive_files');
                localStorage.removeItem('archive_folders');
                localStorage.removeItem('archive_activity_logs');

                // Force re-initialization
                if (window.localDB) {
                    window.localDB.initializeLocalStorage();
                }

                this.showAlert('success', 'تم إعادة تهيئة قاعدة البيانات بنجاح');

                // Wait a bit then reload files
                setTimeout(async () => {
                    await this.loadFiles();
                }, 500);
            }
        } catch (error) {
            console.error('Error resetting database:', error);
            this.showAlert('error', 'حدث خطأ أثناء إعادة تهيئة قاعدة البيانات: ' + error.message);
        }
    }

    // Debug database
    debugDatabase() {
        console.log('=== Database Debug Info ===');
        console.log('window.localDB exists:', !!window.localDB);

        if (window.localDB) {
            console.log('Database type:', window.localDB.db ? 'IndexedDB' : 'localStorage');
            console.log('Database object:', window.localDB.db);

            // Check localStorage
            const files = localStorage.getItem('archive_files');
            console.log('localStorage archive_files:', files);

            if (files) {
                try {
                    const parsedFiles = JSON.parse(files);
                    console.log('Parsed files count:', parsedFiles.length);
                    console.log('Sample file:', parsedFiles[0]);
                } catch (e) {
                    console.error('Error parsing files from localStorage:', e);
                }
            }
        }

        this.showAlert('info', 'تم طباعة معلومات قاعدة البيانات في وحدة التحكم (Console)');
    }

    // Show alert message
    showAlert(type, message) {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// Initialize file manager
let fileManager;
document.addEventListener('DOMContentLoaded', async () => {
    try {
        console.log('Initializing FileManager...');
        console.log('Database available:', !!window.localDB);

        // Wait a bit for database to be ready
        await new Promise(resolve => setTimeout(resolve, 100));

        fileManager = new FileManager();
        window.fileManager = fileManager;
        console.log('File manager initialized successfully');
    } catch (error) {
        console.error('Failed to initialize file manager:', error);

        // Show error to user
        setTimeout(() => {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-danger position-fixed';
            errorDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            errorDiv.innerHTML = `
                <strong>خطأ في تهيئة النظام:</strong><br>
                ${error.message}<br>
                <button class="btn btn-sm btn-light mt-2" onclick="location.reload()">إعادة تحميل الصفحة</button>
            `;
            document.body.appendChild(errorDiv);
        }, 1000);
    }
});

// Export for use in other modules
window.FileManager = FileManager;
