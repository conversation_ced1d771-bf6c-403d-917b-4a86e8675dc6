<!DOCTYPE html>
<html lang="ar" dir="rtl" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#2563eb">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="نظام الأرشفة">
    <meta name="application-name" content="نظام الأرشفة الإلكترونية">
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="msapplication-config" content="browserconfig.xml">
    <title>نظام الأرشفة الإلكترونية - تسجيل الدخول</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/login-enhanced.css" rel="stylesheet">
</head>
<body class="login-page">
    <!-- Animated Background -->
    <div class="animated-background">
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
            <div class="shape shape-6"></div>
        </div>
        <div class="gradient-overlay"></div>
    </div>

    <!-- Main Login Container -->
    <div class="login-container">
        <!-- Left Side - Branding -->
        <div class="login-branding">
            <div class="branding-content">
                <div class="brand-logo">
                    <div class="ministry-logo">
                        <img src="assets/images/ministry-logo2.png" alt="وزارة الدفاع" class="logo-image">
                    </div>
                    <div class="logo-rings">
                        <div class="ring ring-1"></div>
                        <div class="ring ring-2"></div>
                        <div class="ring ring-3"></div>
                    </div>
                </div>
                <h1 class="brand-title" data-translate="login_title">نظام الأرشفة الإلكترونية بمكتب الجزاءات</h1>
                <p class="brand-subtitle" data-translate="brand_description">
                    معهد سلاح المشاة - نظام إلكتروني لأرشفة وإدارة يوميات المخالفات والمستندات المرتبطة بذلك
                </p>
                <div class="brand-features">
                    <div class="feature-item">
                        <i class="fas fa-shield-alt"></i>
                        <span data-translate="secure_system">نظام آمن ومحمي</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-search"></i>
                        <span data-translate="advanced_search">بحث متقدم وذكي</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-cloud"></i>
                        <span data-translate="cloud_ready">جاهز للسحابة</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Login Form -->
        <div class="login-form-container">
            <div class="login-card">
                <!-- Header Controls -->
                <div class="card-header-controls">
                    <div class="theme-toggle" onclick="toggleTheme()" title="تبديل المظهر">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </div>
                    <div class="language-switcher">
                        <button class="lang-btn active" onclick="switchLanguage('ar')" data-lang="ar">
                            <span>ع</span>
                        </button>
                        <button class="lang-btn" onclick="switchLanguage('en')" data-lang="en">
                            <span>EN</span>
                        </button>
                    </div>
                </div>

                <!-- Login Header -->
                <div class="login-header">
                    <div class="welcome-text">
                        <h2 data-translate="welcome_back">مرحباً بعودتك</h2>
                        <p data-translate="login_subtitle">يرجى تسجيل الدخول للمتابعة</p>
                    </div>
                </div>

                <!-- Login Form -->
                <form id="loginForm" class="login-form">
                    <div class="form-group">
                        <label for="username" data-translate="username">اسم المستخدم</label>
                        <div class="input-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <input type="text" id="username" name="username" required
                                   placeholder="" data-translate-placeholder="username_placeholder">
                            <div class="input-focus-line"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password" data-translate="password">كلمة المرور</label>
                        <div class="input-wrapper">
                            <div class="input-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <input type="password" id="password" name="password" required
                                   placeholder="" data-translate-placeholder="password_placeholder">
                            <div class="password-toggle" onclick="togglePassword()">
                                <i class="fas fa-eye" id="passwordToggleIcon"></i>
                            </div>
                            <div class="input-focus-line"></div>
                        </div>
                    </div>

                    <div class="form-options">
                        <label class="custom-checkbox">
                            <input type="checkbox" id="rememberMe" name="rememberMe">
                            <span class="checkmark">
                                <i class="fas fa-check"></i>
                            </span>
                            <span class="checkbox-text" data-translate="remember_me">تذكرني</span>
                        </label>
                        <a href="#" class="forgot-password" data-translate="forgot_password">نسيت كلمة المرور؟</a>
                    </div>

                    <button type="submit" class="login-btn">
                        <span class="btn-content">
                            <span class="btn-text" data-translate="login_button">تسجيل الدخول</span>
                            <i class="btn-icon fas fa-arrow-right"></i>
                        </span>
                        <div class="btn-loader">
                            <div class="spinner"></div>
                        </div>
                        <div class="btn-success">
                            <i class="fas fa-check"></i>
                        </div>
                    </button>
                </form>


            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
            </div>
            <p class="loading-text" data-translate="logging_in">جاري تسجيل الدخول...</p>
        </div>
    </div>

    <!-- Success Animation -->
    <div class="success-animation" id="successAnimation">
        <div class="success-checkmark">
            <div class="check-icon">
                <span class="icon-line line-tip"></span>
                <span class="icon-line line-long"></span>
                <div class="icon-circle"></div>
                <div class="icon-fix"></div>
            </div>
        </div>
        <p class="success-text" data-translate="login_success">تم تسجيل الدخول بنجاح!</p>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="assets/js/auth.js"></script>
    <script src="assets/js/theme.js"></script>
    <script src="assets/js/language.js"></script>
</body>
</html>
