<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الأرشفة الإلكترونية لمكتب الجزاءات</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <!-- Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header text-center">
                <div class="logo-container">
                    <i class="fas fa-archive logo-icon"></i>
                </div>
                <h2 class="login-title">نظام الأرشفة الإلكترونية لمكتب الجزاءات</h2>
                <p class="login-subtitle">تسجيل الدخول إلى النظام</p>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group mb-3">
                    <label for="username" class="form-label">
                        <i class="fas fa-user me-2"></i>اسم المستخدم
                    </label>
                    <input type="text" class="form-control" id="username" name="username" required>
                </div>
                
                <div class="form-group mb-3">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock me-2"></i>كلمة المرور
                    </label>
                    <div class="password-input-container">
                        <input type="password" class="form-control" id="password" name="password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-group mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="rememberMe">
                        <label class="form-check-label" for="rememberMe">
                            تذكرني
                        </label>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary login-btn w-100">
                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                </button>
            </form>
            
            <div class="login-footer">
                <div class="language-switcher">
                    <button class="btn btn-outline-secondary btn-sm" onclick="switchLanguage('ar')">العربية</button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="switchLanguage('en')">English</button>
                </div>
                <div class="theme-switcher mt-2">
                    <button class="btn btn-outline-secondary btn-sm" onclick="toggleTheme()">
                        <i class="fas fa-moon" id="themeIcon"></i>
                        <span id="themeText">الوضع الداكن</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/auth.js"></script>
    <script src="assets/js/theme.js"></script>
    <script src="assets/js/language.js"></script>
</body>
</html>
