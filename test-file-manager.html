<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إدارة الملفات المحسنة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .upload-area {
            border: 3px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            background: #e9ecef;
            border-color: #0056b3;
        }
        .file-input {
            display: none;
        }
        .upload-btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
        .file-preview {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: white;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-folder-open me-3"></i>اختبار إدارة الملفات المحسنة</h1>
            <p>اختبار عرض الصور وملفات PDF باستخدام Blob URLs</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3><i class="fas fa-upload me-2"></i>رفع ملف للاختبار</h3>
                <div class="upload-area" onclick="document.getElementById('testFile').click()">
                    <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                    <h4>انقر هنا لاختيار ملف</h4>
                    <p class="text-muted">يدعم الصور (JPG, PNG, GIF) وملفات PDF</p>
                    <button class="upload-btn">
                        <i class="fas fa-plus me-2"></i>اختيار ملف
                    </button>
                </div>
                <input type="file" id="testFile" class="file-input" accept="image/*,.pdf" onchange="handleFileUpload(this)">
            </div>
            
            <div id="fileInfo" class="test-section" style="display: none;">
                <h3><i class="fas fa-info-circle me-2"></i>معلومات الملف</h3>
                <div id="fileDetails"></div>
            </div>
            
            <div id="filePreview" class="test-section" style="display: none;">
                <h3><i class="fas fa-eye me-2"></i>معاينة الملف</h3>
                <div id="previewContent"></div>
            </div>
            
            <div class="test-section">
                <h3><i class="fas fa-database me-2"></i>الملفات المحفوظة</h3>
                <button class="btn btn-primary" onclick="loadSavedFiles()">
                    <i class="fas fa-refresh me-2"></i>تحميل الملفات المحفوظة
                </button>
                <div id="savedFiles" class="mt-3"></div>
            </div>
        </div>
    </div>

    <script src="assets/js/database.js"></script>
    <script src="assets/js/file-manager.js"></script>
    <script>
        async function handleFileUpload(input) {
            const file = input.files[0];
            if (!file) return;
            
            console.log('File selected:', file.name, file.type, file.size);
            
            // Show file info
            document.getElementById('fileInfo').style.display = 'block';
            document.getElementById('fileDetails').innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>اسم الملف:</strong> ${file.name}<br>
                        <strong>نوع الملف:</strong> ${file.type}<br>
                        <strong>حجم الملف:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB
                    </div>
                    <div class="col-md-6">
                        <strong>تاريخ التعديل:</strong> ${new Date(file.lastModified).toLocaleString('ar-SA')}<br>
                        <strong>حالة الرفع:</strong> <span class="text-success">جاري المعالجة...</span>
                    </div>
                </div>
            `;
            
            try {
                // Check components first
                if (!checkComponents()) {
                    throw new Error('النظام غير جاهز للاستخدام');
                }

                // Convert to base64
                const base64Content = await fileToBase64(file);
                console.log('File converted to base64, length:', base64Content.length);
                
                // Save to database
                if (window.fileManager && window.localDB) {
                    const fileData = {
                        name: file.name,
                        originalName: file.name,
                        type: file.type,
                        size: file.size,
                        content: base64Content,
                        category: file.type.includes('pdf') ? 'مستند PDF' : 'صورة',
                        description: `ملف تجريبي - ${file.name}`,
                        tags: ['تجريبي', 'اختبار'],
                        courseCode: 'TEST-001',
                        courseNumber: 1,
                        issueDate: new Date().toISOString().split('T')[0]
                    };

                    try {
                        const fileId = await window.fileManager.saveFile(fileData);

                        // Update status
                        document.getElementById('fileDetails').innerHTML += `
                            <div class="status success mt-2">
                                <i class="fas fa-check-circle me-2"></i>تم حفظ الملف بنجاح في قاعدة البيانات
                                <br><small>معرف الملف: ${fileId}</small>
                            </div>
                        `;
                    } catch (saveError) {
                        console.error('Error saving to database:', saveError);
                        document.getElementById('fileDetails').innerHTML += `
                            <div class="status error mt-2">
                                <i class="fas fa-exclamation-circle me-2"></i>خطأ في حفظ الملف: ${saveError.message}
                            </div>
                        `;
                    }
                } else {
                    document.getElementById('fileDetails').innerHTML += `
                        <div class="status info mt-2">
                            <i class="fas fa-info-circle me-2"></i>تم معالجة الملف بنجاح (قاعدة البيانات غير متوفرة)
                        </div>
                    `;
                }
                
                // Show preview
                showFilePreview(file, base64Content);
                
            } catch (error) {
                console.error('Error processing file:', error);
                document.getElementById('fileDetails').innerHTML += `
                    <div class="status error mt-2">
                        <i class="fas fa-exclamation-circle me-2"></i>خطأ في معالجة الملف: ${error.message}
                    </div>
                `;
            }
        }
        
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        }
        
        function showFilePreview(file, base64Content) {
            document.getElementById('filePreview').style.display = 'block';
            const previewContent = document.getElementById('previewContent');
            
            if (file.type.includes('image')) {
                previewContent.innerHTML = `
                    <div class="text-center">
                        <h5>معاينة الصورة</h5>
                        <img src="${base64Content}" style="max-width: 100%; max-height: 400px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                    </div>
                `;
            } else if (file.type.includes('pdf')) {
                previewContent.innerHTML = `
                    <div class="text-center">
                        <h5>ملف PDF جاهز للعرض</h5>
                        <p class="text-muted">استخدم إدارة الملفات في لوحة التحكم لعرض PDF بالطرق المحسنة</p>
                        <a href="dashboard.html" class="btn btn-primary">
                            <i class="fas fa-external-link-alt me-2"></i>فتح لوحة التحكم
                        </a>
                    </div>
                `;
            }
        }
        
        async function loadSavedFiles() {
            try {
                if (!window.localDB) {
                    throw new Error('قاعدة البيانات غير متوفرة');
                }
                
                const files = await window.localDB.getFiles();
                const savedFilesDiv = document.getElementById('savedFiles');
                
                if (files.length === 0) {
                    savedFilesDiv.innerHTML = '<p class="text-muted">لا توجد ملفات محفوظة</p>';
                    return;
                }
                
                let html = '<div class="row">';
                files.forEach(file => {
                    html += `
                        <div class="col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-${file.type.includes('pdf') ? 'file-pdf' : 'image'} me-2"></i>
                                        ${file.name}
                                    </h6>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            ${file.category} • ${(file.size / 1024).toFixed(1)} KB
                                        </small>
                                    </p>
                                    <a href="dashboard.html" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye me-1"></i>عرض في لوحة التحكم
                                    </a>
                                </div>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                
                savedFilesDiv.innerHTML = html;
                
            } catch (error) {
                console.error('Error loading saved files:', error);
                document.getElementById('savedFiles').innerHTML = `
                    <div class="status error">
                        <i class="fas fa-exclamation-circle me-2"></i>خطأ في تحميل الملفات: ${error.message}
                    </div>
                `;
            }
        }
        
        // Initialize and check components
        function checkComponents() {
            console.log('Checking components...');
            console.log('window.localDB:', !!window.localDB);
            console.log('window.fileManager:', !!window.fileManager);

            if (!window.localDB) {
                document.getElementById('savedFiles').innerHTML = `
                    <div class="status error">
                        <i class="fas fa-exclamation-circle me-2"></i>قاعدة البيانات غير متوفرة
                    </div>
                `;
                return false;
            }

            if (!window.fileManager) {
                console.log('FileManager not available, creating instance...');
                try {
                    window.fileManager = new FileManager();
                    console.log('FileManager created successfully');
                } catch (error) {
                    console.error('Error creating FileManager:', error);
                    return false;
                }
            }

            return true;
        }

        // Auto-load saved files on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (checkComponents()) {
                    loadSavedFiles();
                }
            }, 1500);
        });
    </script>
</body>
</html>
